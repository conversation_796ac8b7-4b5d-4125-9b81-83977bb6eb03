﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="configurationFolder" Type="InArgument(x:String)" />
    <x:Property Name="directoriesNames" Type="InArgument(scg:List(x:String))" />
    <x:Property Name="logFolderName" Type="InArgument(x:String)" />
    <x:Property Name="numberOfParts" Type="InArgument(x:Int32)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="CheckDirectoriesSequence" sap2010:WorkflowViewState.IdRef="Sequence_1">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="logFile" />
      <Variable x:TypeArguments="x:Boolean" Name="logFileExist" />
      <Variable x:TypeArguments="x:Int32" Default="1" Name="DownloadFolderCounter" />
    </Sequence.Variables>
    <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_1" Values="[directoriesNames]">
      <ActivityAction x:TypeArguments="x:String">
        <ActivityAction.Argument>
          <DelegateInArgument x:TypeArguments="x:String" Name="directory" />
        </ActivityAction.Argument>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_2">
          <Sequence.Variables>
            <Variable x:TypeArguments="x:String" Name="folder" />
            <Variable x:TypeArguments="x:Boolean" Name="folderExist" />
          </Sequence.Variables>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_1">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[folder]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[configurationFolder+ "\"+ directory]</InArgument>
            </Assign.Value>
          </Assign>
          <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_1" IsValid="[folderExist]" Path="[folder]" />
          <If Condition="[not folderExist]" sap2010:WorkflowViewState.IdRef="If_1">
            <If.Then>
              <Sequence DisplayName="Create Directory Sequence" sap2010:WorkflowViewState.IdRef="Sequence_7">
                <ias:Directory_Create ErrorCode="{x:Null}" OutputPath="{x:Null}" ContinueOnError="False" DisplayName="Create Directory" sap2010:WorkflowViewState.IdRef="Directory_Create_5" Name="[directory]" Target="[configurationFolder]" />
              </Sequence>
            </If.Then>
          </If>
        </Sequence>
      </ActivityAction>
    </ForEach>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_2">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[logFile]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[configurationFolder+"\"+logFolderName+"\"+"JobsResults"+System.DateTime.Now.ToString("ddMMyyyy")+".txt"]</InArgument>
      </Assign.Value>
    </Assign>
    <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_2" IsValid="[logFileExist]" Path="[logFile]" />
    <If Condition="[not logFileExist]" sap2010:WorkflowViewState.IdRef="If_2">
      <If.Then>
        <ias:File_Create ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Create File" sap2010:WorkflowViewState.IdRef="File_Create_1" Name="[&quot;JobsResults&quot;+System.DateTime.Now.ToString(&quot;ddMMyyyy&quot;)+&quot;.txt&quot;]" OutputFile="[logFile]" Target="[configurationFolder+&quot;\&quot;+logFolderName]" />
      </If.Then>
    </If>
    <If Condition="[directoriesNames.contains(&quot;Success&quot;)]" sap2010:WorkflowViewState.IdRef="If_3">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_3">
          <Sequence.Variables>
            <Variable x:TypeArguments="x:Boolean" Name="currentDateSuccessFolderExist" />
          </Sequence.Variables>
          <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_3" IsValid="[currentDateSuccessFolderExist]" Path="[configurationFolder+&quot;\Success\&quot;+System.DateTime.Now.ToString(&quot;yyyy-MM-dd&quot;)]" />
          <If Condition="[not currentDateSuccessFolderExist]" sap2010:WorkflowViewState.IdRef="If_4">
            <If.Then>
              <ias:Directory_Create ErrorCode="{x:Null}" OutputPath="{x:Null}" ContinueOnError="True" DisplayName="Create Directory" sap2010:WorkflowViewState.IdRef="Directory_Create_2" Name="[System.DateTime.Now.ToString(&quot;yyyy-MM-dd&quot;)]" Target="[configurationFolder+&quot;\Success&quot;]" />
            </If.Then>
          </If>
        </Sequence>
      </If.Then>
    </If>
    <If Condition="[directoriesNames.Contains(&quot;OutlookDownloads&quot;)]" DisplayName="Check if OutlookDownloads folder" sap2010:WorkflowViewState.IdRef="If_9">
      <If.Then>
        <While sap2010:WorkflowViewState.IdRef="While_4" Condition="[DownloadFolderCounter&lt;numberOfParts+1]">
          <Sequence DisplayName="Create Process Downlaod Folder" sap2010:WorkflowViewState.IdRef="Sequence_9">
            <Sequence.Variables>
              <Variable x:TypeArguments="x:Boolean" Name="DownloadFolders" />
            </Sequence.Variables>
            <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_5" IsValid="[DownloadFolders]" Path="[configurationFolder+&quot;\OutlookDownloads\DownloadedFiles_&quot;+DownloadFolderCounter.tostring]" />
            <If Condition="[NOT DownloadFolders]" sap2010:WorkflowViewState.IdRef="If_10">
              <If.Then>
                <ias:Directory_Create ErrorCode="{x:Null}" OutputPath="{x:Null}" ContinueOnError="False" DisplayName="Create Directory" sap2010:WorkflowViewState.IdRef="Directory_Create_8" Name="[&quot;DownloadedFiles_&quot;+DownloadFolderCounter.tostring]" Target="[configurationFolder+&quot;\OutlookDownloads&quot;]" />
              </If.Then>
            </If>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_6">
              <Assign.To>
                <OutArgument x:TypeArguments="x:Int32">[DownloadFolderCounter]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:Int32">[DownloadFolderCounter+1]</InArgument>
              </Assign.Value>
            </Assign>
          </Sequence>
        </While>
      </If.Then>
    </If>
    <sads:DebugSymbol.Symbol>d11DOlxVc2Vyc1xkbWl0dGFwYWxsaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYyXENyZWF0ZURpcmVjdG9yaWVzLnhhbWw4OgOUAQ4CAQE+Mz42AgECQAVcDwIBQ10FZA4CAT1lBWW8AQIBOGYFagoCAS5rBXkKAgEeegWSAQoCAQNAggFAlgECAVlFCVoUAgFEYjBimQECAUBfMV86AgE+ZZgBZagBAgE7Za4BZbkBAgE5ZhNmJwIBL2gJaMkCAgExaxNrRQIBH20JdxQCASF6E3pOAgEEfAmQARECAQZKC1EUAgFTUgtSwAECAU5TC1kQAgFFaIACaIsCAgE2aJMCaMYCAgEzaJUBaPQBAgEycQtxqgICASlyC3YQAgEifQuPARYCAQp8RHxwAgEHTzZPWwIBVkw3TD8CAVRSngFSrQECAVFSswFSvQECAU9TGVMsAgFGVQ9XGgIBSHGeAXG/AQIBLHHFAXGnAgIBKnIZcj4CASN0D3SvAgIBJYEBDYEBoQICARiCAQ2GARICARCHAQ2OARYCAQtWEVbyAQIBSXSAAnSsAgIBJ3TAAXT4AQIBJoEBoAGBAbMBAgEcgQG5AYEBngICARmCARuCATICARGEARGEAcICAgETjAE3jAFQAgEOiQE4iQFPAgEMVtgBVu8BAgFMVsMBVtABAgFKhAGKAoQBvwICARaEAcMBhAGCAgIBFA==</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Path_Validate_1" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Directory_Create_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="222,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="464,300" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="486,588">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_1" sap:VirtualizedContainerService.HintSize="638.666666666667,740.666666666667" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="638.666666666667,62" />
      <sap2010:ViewStateData Id="Path_Validate_2" sap:VirtualizedContainerService.HintSize="638.666666666667,22" />
      <sap2010:ViewStateData Id="File_Create_1" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="638.666666666667,214" />
      <sap2010:ViewStateData Id="Path_Validate_3" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Directory_Create_2" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="464,214" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="486,400">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="638.666666666667,554" />
      <sap2010:ViewStateData Id="Path_Validate_5" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Directory_Create_8" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_10" sap:VirtualizedContainerService.HintSize="464,214" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Sequence_9" sap:VirtualizedContainerService.HintSize="486,502">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="While_4" sap:VirtualizedContainerService.HintSize="512.666666666667,666">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_9" sap:VirtualizedContainerService.HintSize="638.666666666667,820">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="660.666666666667,2736.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="700.666666666667,2856.66666666667" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>