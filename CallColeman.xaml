<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="ocrValues" Type="InArgument(s:String[])" />
    <x:Property Name="APIString" Type="InArgument(x:String)" />
    <x:Property Name="colemanAPI" Type="InArgument(x:String)" />
    <x:Property Name="Status" Type="OutArgument(x:String)" />
    <x:Property Name="comments" Type="OutArgument(x:String)" />
    <x:Property Name="httpOut" Type="OutArgument(njl:JToken)" />
    <x:Property Name="logfile" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="CallColeman" sap2010:WorkflowViewState.IdRef="Sequence_7">
    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_6">
      <Sequence.Variables>
        <Variable x:TypeArguments="njl:JToken" Name="JOutput" />
      </Sequence.Variables>
      <Assign sap2010:WorkflowViewState.IdRef="Assign_1">
        <Assign.To>
          <OutArgument x:TypeArguments="njl:JToken">[httpOut]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="njl:JToken">[{}]</InArgument>
        </Assign.Value>
      </Assign>
      <If Condition="[ocrValues(2) &lt;&gt; &quot;-999&quot; AND APIString &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_3">
        <If.Then>
          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_4">
            <Sequence.Variables>
              <Variable x:TypeArguments="x:String" Name="ColmanTemplate" />
              <Variable x:TypeArguments="njl:JToken" Name="JInput" />
              <Variable x:TypeArguments="x:Int32" Name="colResp" />
              <Variable x:TypeArguments="x:String" Name="variable1" />
              <Variable x:TypeArguments="iru:ResponseObject" Name="RespOutput" />
            </Sequence.Variables>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_3">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[APIString]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[APIString+"]"]</InArgument>
              </Assign.Value>
            </Assign>
            <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_1" Template="{}{'runtimeData': {{%APIInputArray%}},'executionType': 'MULTIPLE', 'customSchema': {}}" Text="[ColmanTemplate]">
              <ias:Template_Apply.Values>
                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>APIInputArray</x:String>
                  </scg:List>
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>APIString</x:String>
                  </scg:List>
                </scg:List>
              </ias:Template_Apply.Values>
            </ias:Template_Apply>
            <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_1" JTokenObject="[JInput]" JTokenString="[ColmanTemplate]" />
            <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_5">
              <iad:CommentOut.Activities>
                <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_1" Selection="OK" Text="[Jinput.ToString]" />
              </iad:CommentOut.Activities>
            </iad:CommentOut>
            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" QueryParameters="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_1" PostData="[JInput.ToString]" Response="[respOutput]" StatusCode="[colResp]" Url="[colemanAPI]">
              <iai:IONAPIRequestWizard.Headers>
                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>Accept</x:String>
                  </scg:List>
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>application/json</x:String>
                  </scg:List>
                </scg:List>
              </iai:IONAPIRequestWizard.Headers>
            </iai:IONAPIRequestWizard>
            <iad:CommentOut DisplayName="Comment Out">
              <iad:CommentOut.Activities>
                <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_2" Selection="OK" Text="[respOutput.ReadAsText]" />
              </iad:CommentOut.Activities>
              <sap2010:WorkflowViewState.IdRef>CommentOut_6</sap2010:WorkflowViewState.IdRef>
            </iad:CommentOut>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_5">
              <Assign.To>
                <OutArgument x:TypeArguments="njl:JToken">[httpOut]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="njl:JToken">[{}]</InArgument>
              </Assign.Value>
            </Assign>
            <If Condition="[colResp &lt;&gt; 200]" sap2010:WorkflowViewState.IdRef="If_2">
              <If.Then>
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_1">
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_6">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[comments]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">Optimizer API failure.</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_7">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">failure</InArgument>
                    </Assign.Value>
                  </Assign>
                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="[comments]" Source="[logfile]" />
                </Sequence>
              </If.Then>
              <If.Else>
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_8">
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_4">
                    <Assign.To>
                      <OutArgument x:TypeArguments="njl:JToken">[JOutput]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(RespOutput.ReadAsText)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <If Condition="[JOutput(&quot;result&quot;).ToString &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_1">
                    <If.Then>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_2">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_8">
                          <Assign.To>
                            <OutArgument x:TypeArguments="njl:JToken">[httpOut]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="njl:JToken">[{}]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_9">
                          <Assign.To>
                            <OutArgument x:TypeArguments="njl:JToken">[httpOut]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="njl:JToken">[JOutput("result")("output_data")]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_10">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[comments]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">["Results from Optimizer Obtained"]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_11">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">Success</InArgument>
                          </Assign.Value>
                        </Assign>
                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_3" Line="[comments]" Source="[logfile]" />
                      </Sequence>
                    </If.Then>
                    <If.Else>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_3">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_16">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[comments]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">Optimizer Response is not obtained</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_17">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">failure</InArgument>
                          </Assign.Value>
                        </Assign>
                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_2" Line="[comments]" Source="[logfile]" />
                      </Sequence>
                    </If.Else>
                  </If>
                </Sequence>
              </If.Else>
            </If>
          </Sequence>
        </If.Then>
        <If.Else>
          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_5">
            <Assign sap2010:WorkflowViewState.IdRef="Assign_14">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[comments]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[comments + Environment.NewLine + "Given PO number has no unmatched lines"]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_15">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">Failure</InArgument>
              </Assign.Value>
            </Assign>
          </Sequence>
        </If.Else>
      </If>
    </Sequence>
    <sads:DebugSymbol.Symbol>d1dDOlxVc2Vyc1xkbWl0dGFwYWxsaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYyXENhbGxDb2xlbWFuLnhhbWxORgOJAg4CAQFHBYcCEAIBAksHUhACAWpTB4YCDAIBA1A0UDgCAW1NNU0+AgFrUxVTYwIBBFUL8AEWAgEQ8wELhAIWAgEHXQ1kFgIBZWUNcCICAWFxDXHbAQIBXHINdh4CAVt3DYIBJwIBU4MBDYgBHgIBUokBDZABFgIBTpEBDe8BEgIBEfQBDfsBFgIBDPwBDYMCFgIBCGI4YkcCAWhfOV9EAgFmZYkCZZsCAgFjZasBZYMCAgFiccYBcdgBAgFfca4BcbgBAgFdd5oCd60CAgFad7cCd8UCAgFYd+ECd+8CAgFWd9ECd9wCAgFUjgE6jgE+AgFRiwE7iwFEAgFPkQEbkQEzAgESkwERpQEcAgFAqAER7QEcAgEU+QE4+QGDAQIBD/YBOfYBQwIBDYECOIECPwIBC/4BOf4BQQIBCZQBE5sBHAIBSpwBE6MBHAIBRqQBE6QBwQECAUGpAROwARwCATyxARPsARgCARWZAT6ZAVQCAU2WAT+WAUkCAUuhAT6hAUUCAUmeAT+eAUcCAUekAZ8BpAGrAQIBRKQBswGkAb4BAgFCrgFArgFlAgE/qwFBqwFKAgE9sQEhsQFfAgEWswEX1QEiAgEl2AEX6gEiAgEXtAEZuwEiAgE4vAEZwwEiAgEzxAEZywEiAgEvzAEZ0wEiAgEr1AEZ1AHHAQIBJtkBGeABIgIBIeEBGegBIgIBHekBGekBxwECARi5AUa5AUoCATu2AUe2AVACATnBAUbBAWgCATa+AUe+AVACATTJAUTJAWcCATLGAUXGAU8CATDRAUTRAUsCAS7OAUXOAU0CASzUAaUB1AGxAQIBKdQBuQHUAcQBAgEn3gFE3gFmAgEk2wFF2wFPAgEi5gFE5gFLAgEg4wFF4wFNAgEe6QGlAekBsQECARvpAbkB6QHEAQIBGQ==</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="1177.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="865.333333333333,62" />
      <sap2010:ViewStateData Id="Template_Apply_1" sap:VirtualizedContainerService.HintSize="865.333333333333,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_1" sap:VirtualizedContainerService.HintSize="865.333333333333,22" />
      <sap2010:ViewStateData Id="MessageBox_1" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_5" sap:VirtualizedContainerService.HintSize="865.333333333333,118" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_1" sap:VirtualizedContainerService.HintSize="865.333333333333,22" />
      <sap2010:ViewStateData Id="MessageBox_2" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_6" sap:VirtualizedContainerService.HintSize="865.333333333333,118" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="865.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_7" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="264,348">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_11" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="264,552">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_16" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_17" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="553,700" />
      <sap2010:ViewStateData Id="Sequence_8" sap:VirtualizedContainerService.HintSize="575.333333333333,924">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="865.333333333333,1078" />
      <sap2010:ViewStateData Id="Sequence_4" sap:VirtualizedContainerService.HintSize="887.333333333333,1908">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_14" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_15" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="264,286">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="1177.33333333333,2062">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_6" sap:VirtualizedContainerService.HintSize="1199.33333333333,2288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="1221.33333333333,2412">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="1261.33333333333,2532" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>