﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Desktop;assembly=Infor.Activities.Desktop"
 xmlns:iad1="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Web;assembly=Infor.Activities.Web"
 xmlns:iro="clr-namespace:Infor.RPA.OCR;assembly=Infor.RPA.OCR"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="ocrValues" Type="OutArgument(s:String[])" />
    <x:Property Name="documentPath" Type="InArgument(x:String)" />
    <x:Property Name="ocrLineValues" Type="OutArgument(scg:List(scg:List(x:String)))" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="promptPath" Type="InArgument(x:String)" />
    <x:Property Name="customPrompt" Type="InArgument(x:String)" />
    <x:Property Name="genAiVersion" Type="InArgument(x:String)" />
    <x:Property Name="genAiModel" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.OCR</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System.IO</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.Text.RegularExpressions</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.OCR</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="GenAI Sequence" sap2010:WorkflowViewState.IdRef="Sequence_3">
    <Sequence.Variables>
      <Variable x:TypeArguments="scg:Dictionary(x:String, scg:List(x:String))" Name="ocrDictionary" />
      <Variable x:TypeArguments="njl:JToken" Name="values" />
      <Variable x:TypeArguments="x:Int32" Name="responseCode" />
      <Variable x:TypeArguments="x:String" Name="promptText" />
      <Variable x:TypeArguments="x:String" Name="promptRequest" />
      <Variable x:TypeArguments="x:String" Name="base64string" />
      <Variable x:TypeArguments="x:String" Name="screenshotPath" />
      <Variable x:TypeArguments="scg:List(x:String)" Name="txtFiles" />
      <Variable x:TypeArguments="x:String" Name="pg" />
    </Sequence.Variables>
    <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Read Text File" sap2010:WorkflowViewState.IdRef="File_Read_1" Source="[promptPath]" Text="[promptText]" />
    <Assign sap2010:WorkflowViewState.IdRef="Assign_1">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(x:String))">[ocrDictionary]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(x:String))">[New Dictionary (Of String, List(Of String))]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_2">
      <Assign.To>
        <OutArgument x:TypeArguments="s:String[]">[ocrValues]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="s:String[]">[New String(20) {"","","", "","","","","","","","","","","","","","","","","",""}]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_88">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(scg:List(x:String))">[ocrLineValues]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[New List(Of List(Of String))]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_74">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[pg]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">
          <Literal x:TypeArguments="x:String" Value="" />
        </InArgument>
      </Assign.Value>
    </Assign>
    <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="True" DirPath="[Path.GetDirectoryName(documentPath)]" DisplayName="Get Files in Directory" FileType="Text" Files="[txtFiles]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_5" IncludeSubDir="True" />
    <If Condition="False" sap2010:WorkflowViewState.IdRef="If_20">
      <If.Then>
        <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_2">
          <TryCatch.Try>
            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_32">
              <Assign sap2010:WorkflowViewState.IdRef="Assign_70">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[base64string]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[txtFiles.Find(Function(s) s.Contains((Path.GetFileName(documentPath)).Split("~"c)(1)))]</InArgument>
                </Assign.Value>
              </Assign>
              <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Read Text File" sap2010:WorkflowViewState.IdRef="File_Read_4" Source="[base64string]" Text="[base64string]" />
            </Sequence>
          </TryCatch.Try>
          <TryCatch.Catches>
            <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_2">
              <ActivityAction x:TypeArguments="s:Exception">
                <ActivityAction.Argument>
                  <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                </ActivityAction.Argument>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_71">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[base64string]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[Nothing]</InArgument>
                  </Assign.Value>
                </Assign>
              </ActivityAction>
            </Catch>
          </TryCatch.Catches>
        </TryCatch>
      </If.Then>
      <If.Else>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_72">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[base64string]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[Nothing]</InArgument>
          </Assign.Value>
        </Assign>
      </If.Else>
    </If>
    <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_3">
      <TryCatch.Try>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_35">
          <Sequence.Variables>
            <Variable x:TypeArguments="x:String" Name="pg1" />
          </Sequence.Variables>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_75">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[pg1]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[documentPath.Split("~"c)(2)]</InArgument>
            </Assign.Value>
          </Assign>
          <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_10" Values="[documentPath.Split(&quot;~&quot;c)(3).Split(&quot;-&quot;c)]">
            <ActivityAction x:TypeArguments="x:String">
              <ActivityAction.Argument>
                <DelegateInArgument x:TypeArguments="x:String" Name="item" />
              </ActivityAction.Argument>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_36">
                <If Condition="[item &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_24">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_37">
                      <If Condition="[item.contains(&quot;.&quot;)]" sap2010:WorkflowViewState.IdRef="If_23">
                        <If.Then>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_89">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[item]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[item.sPlit("."c)(0)]</InArgument>
                            </Assign.Value>
                          </Assign>
                        </If.Then>
                      </If>
                      <If Condition="[(New List(Of String)(pg1.Split(&quot;,&quot;c)).IndexOf(item) + 1).ToString() &lt;&gt; &quot;0&quot;]" sap2010:WorkflowViewState.IdRef="If_25">
                        <If.Then>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_91">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[pg]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[pg + "," + (New List(Of String)(pg1.Split(","c)).IndexOf(item) + 1).ToString().trim]</InArgument>
                            </Assign.Value>
                          </Assign>
                        </If.Then>
                      </If>
                    </Sequence>
                  </If.Then>
                </If>
              </Sequence>
            </ActivityAction>
          </ForEach>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_92">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[pg]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[pg.substring(1,pg.length-1)]</InArgument>
            </Assign.Value>
          </Assign>
        </Sequence>
      </TryCatch.Try>
      <TryCatch.Catches>
        <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_3">
          <ActivityAction x:TypeArguments="s:Exception">
            <ActivityAction.Argument>
              <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
            </ActivityAction.Argument>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_76">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[pg]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">
                  <Literal x:TypeArguments="x:String" Value="" />
                </InArgument>
              </Assign.Value>
            </Assign>
          </ActivityAction>
        </Catch>
      </TryCatch.Catches>
    </TryCatch>
    <iro:DocumentOC ActivityID="f69e5a30-0b3c-4f29-9d8a-1e6947a44cd8" ContinueOnError="True" DisplayName="Get OCR Text" ErrorCode="[responseCode]" ExecuteFile="True" FilePath="[documentPath]" sap2010:WorkflowViewState.IdRef="DocumentOC_1" Pages="[pg]" ResponseObject="[values]" />
    <If Condition="[responseCode = 200]" sap2010:WorkflowViewState.IdRef="If_1">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_1">
          <Sequence.Variables>
            <Variable x:TypeArguments="x:String" Name="ocrOutput" />
            <Variable x:TypeArguments="x:String" Name="IonBody" />
            <Variable x:TypeArguments="njl:JToken" Name="IonBodyJson" />
            <Variable x:TypeArguments="njl:JToken" Name="genAIRequestToken" />
            <Variable x:TypeArguments="iru:ResponseObject" Name="genAIResponse" />
            <Variable x:TypeArguments="njl:JToken" Name="out1" />
            <Variable x:TypeArguments="x:String" Name="out2" />
            <Variable x:TypeArguments="njl:JToken" Name="jout" />
            <Variable x:TypeArguments="njl:JArray" Name="jArr1" />
            <Variable x:TypeArguments="x:Int32" Name="i" />
            <Variable x:TypeArguments="njl:JArray" Name="dn" />
            <Variable x:TypeArguments="x:String" Name="customText" />
            <Variable x:TypeArguments="x:Boolean" Name="customExists" />
            <Variable x:TypeArguments="scg:List(x:String)" Name="dList" />
            <Variable x:TypeArguments="x:String" Name="value" />
            <Variable x:TypeArguments="x:String" Name="outputStructure" />
            <Variable x:TypeArguments="njl:JToken" Name="tempJArray" />
          </Sequence.Variables>
          <If Condition="[base64string = Nothing]" sap2010:WorkflowViewState.IdRef="If_18">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_29">
                <iaw:OpenBrowser ErrorCode="{x:Null}" Browser="Chrome" ContinueOnError="True" DisplayName="Open Browser" sap2010:WorkflowViewState.IdRef="OpenBrowser_2" PrivateBrowsingEnabled="False" URL="https://www.google.com/" ZoomPercentage="60" />
                <iaw:NavigateTo ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Navigate To" sap2010:WorkflowViewState.IdRef="NavigateTo_2" URL="[Path.GetDirectoryName(documentPath) + &quot;/&quot; + Uri.EscapeDataString(Path.GetFileName(documentPath))]" WaitAfter="0" WaitBefore="0" ZoomPercentage="100" />
                <iaw:MaximizeWindow ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Maximize Window" sap2010:WorkflowViewState.IdRef="MaximizeWindow_2" WaitAfter="0" WaitBefore="0" />
                <iad:SendKeys ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Send Keys" sap2010:WorkflowViewState.IdRef="SendKeys_1" IsWeb="False" Text="^-" WaitAfter="0" WaitBefore="0" />
                <iad:SendKeys ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Send Keys" sap2010:WorkflowViewState.IdRef="SendKeys_3" IsWeb="False" Text="^-" WaitAfter="0" WaitBefore="1000" />
                <iad:SendKeys ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Send Keys" sap2010:WorkflowViewState.IdRef="SendKeys_2" IsWeb="False" Text="^-" WaitAfter="0" WaitBefore="1000" />
                <iaw:ScreenShot ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Take Screenshot" sap2010:WorkflowViewState.IdRef="ScreenShot_2" SaveLocation="[screenshotPath]" WaitAfter="0" WaitBefore="10000" />
                <ias:FileToBase64 ErrorCode="{x:Null}" Base64="[base64string]" ContinueOnError="True" DisplayName="File to Base64 String" FilePath="[screenshotPath]" sap2010:WorkflowViewState.IdRef="FileToBase64_2" />
                <iaw:CloseBrowser ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Close Browser" sap2010:WorkflowViewState.IdRef="CloseBrowser_2" />
              </Sequence>
            </If.Then>
          </If>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_93">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[customText]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">
                <Literal x:TypeArguments="x:String" Value="" />
              </InArgument>
            </Assign.Value>
          </Assign>
          <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_1" IsValid="[customExists]" Path="[customPrompt]" />
          <If Condition="[customExists]" sap2010:WorkflowViewState.IdRef="If_26">
            <If.Then>
              <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Read Text File" sap2010:WorkflowViewState.IdRef="File_Read_5" Source="[customPrompt]" Text="[customText]" />
            </If.Then>
          </If>
          <If Condition="True" sap2010:WorkflowViewState.IdRef="If_7">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_11">
                <iad1:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_9">
                  <iad1:CommentOut.Activities>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_28">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[promptText.Replace("{text}",values.tostring)]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_87">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[promptRequest.Replace("{text}",Regex.Replace(Values.ToString(), "[^A-Za-z0-9\s!@#$%^&amp;*()_+={}\[\]:;,'&lt;&gt;.?//|-]", String.Empty))]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </iad1:CommentOut.Activities>
                </iad1:CommentOut>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_94">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[promptText.Replace("{notes}",customText.tostring)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_29">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[promptRequest.Replace("'","\'")]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_104">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[outputStructure]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[promptRequest.Split(New String() {"====="}, StringSplitOptions.None)(1)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_105">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[promptRequest.Split(New String() {"====="}, StringSplitOptions.None)(0)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_103">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[value]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[Regex.Replace(Values.ToString(), "[^A-Za-z0-9\s!@#$%^&amp;*()_+={}\[\]:;,'&lt;&gt;.?//|-]", String.Empty).Replace("'","\'")]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Latest Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_8" Template="{}{&#xA;  'model': '{{%model%}}',&#xA;  'version': '{{%version%}}',&#xA;  'temperature': 0,&#xA;  'max_response': 8192,&#xA;  'stop_sequence': [&#xA;    'Explanations',&#xA;    'Explanation'&#xA;  ],&#xA;  'system': 'You are an expert document based Question-Answering tool. Your task is to answer the 2 questions using the image of only the first page in the document as reference for layout and empty values, but the extraction of values must be from the raw text. Ignore brevity and it is mandatory to include all the requested values from all the pages as in the raw text in your response. In the provided image you will see 1/N. N indicating number of pages in the full document. This means that you should look for the requested values in the question from all those N pages. Without missing a single one. The content of these pages will be available in the raw text indicated by PageNo value.',&#xA;  'messages': [&#xA;      {&#xA;        'role': 'user',&#xA;        'content': [&#xA;          {&#xA;            'type': 'image',&#xA;            'data': 'data:image/png;base64,{{%base64string%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Read the following instructions'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%prompt%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%ocrText%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Use the following output format for your response. Your response should only have the JSON without code blocks as the response. Do not include any of your analysis statements before or after the JSON in the response. Do not add any additional objects to the JSON that are not present in the following Output Format Structure.'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%outputStructure%}}'&#xA;          }&#xA;        ]&#xA;      },&#xA;      {&#xA;        'role': 'assistant',&#xA;        'content': [&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Based on the invoice document, here are the extracted values in the requested JSON format without any code blocks:'&#xA;          }&#xA;        ]&#xA;      }&#xA;  ]&#xA;}&#xA;" Text="[IonBody]">
                  <ias:Template_Apply.Values>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="8">
                        <x:String>prompt</x:String>
                        <x:String>base64string</x:String>
                        <x:String>model</x:String>
                        <x:String>version</x:String>
                        <x:String>ocrText</x:String>
                        <x:String>outputStructure</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="8">
                        <x:String>promptRequest</x:String>
                        <x:String>base64string</x:String>
                        <x:String>genAiModel</x:String>
                        <x:String>genAiVersion</x:String>
                        <x:String>value</x:String>
                        <x:String>outputStructure</x:String>
                      </scg:List>
                    </scg:List>
                  </ias:Template_Apply.Values>
                </ias:Template_Apply>
                <iad1:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_13">
                  <iad1:CommentOut.Activities>
                    <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Latest Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_9" Template="{}{&#xA;  'model': '{{%model%}}',&#xA;  'version': '{{%version%}}',&#xA;  'temperature': 0,&#xA;  'max_response': 8192,&#xA;  'stop_sequence': [&#xA;    'Explanations',&#xA;    'Explanation'&#xA;  ],&#xA;  'system': 'You are an expert document based Question-Answering tool. Your task is to answer the 2 questions using the image of only the first page in the document as reference for layout and empty values, but the extraction of values must be from the ocr text.',&#xA;  'messages': [&#xA;      {&#xA;        'role': 'user',&#xA;        'content': [&#xA;          {&#xA;            'type': 'image',&#xA;            'data': 'data:image/png;base64,{{%base64string%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Read the following instructions'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%prompt%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%ocrText%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Use the following output format for your response. Your response should only have the JSON without code blocks as the response. Do not include any of your analysis statements before or after the JSON in the response. Do not add any additional objects to the JSON that are not present in the following Output Format Structure.'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%outputStructure%}}'&#xA;          }&#xA;        ]&#xA;      },&#xA;      {&#xA;        'role': 'assistant',&#xA;        'content': [&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Based on the invoice document, here are the extracted values in the requested JSON format without any code blocks:'&#xA;          }&#xA;        ]&#xA;      }&#xA;  ]&#xA;}&#xA;" Text="[IonBody]">
                      <ias:Template_Apply.Values>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="8">
                            <x:String>prompt</x:String>
                            <x:String>base64string</x:String>
                            <x:String>model</x:String>
                            <x:String>version</x:String>
                            <x:String>ocrText</x:String>
                            <x:String>outputStructure</x:String>
                          </scg:List>
                          <scg:List x:TypeArguments="x:String" Capacity="8">
                            <x:String>promptRequest</x:String>
                            <x:String>base64string</x:String>
                            <x:String>genAiModel</x:String>
                            <x:String>genAiVersion</x:String>
                            <x:String>value</x:String>
                            <x:String>outputStructure</x:String>
                          </scg:List>
                        </scg:List>
                      </ias:Template_Apply.Values>
                    </ias:Template_Apply>
                  </iad1:CommentOut.Activities>
                </iad1:CommentOut>
                <iad1:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_11">
                  <iad1:CommentOut.Activities>
                    <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Latest Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_7" Template="{}{&#xA;  'model': '{{%model%}}',&#xA;  'version': '{{%version%}}',&#xA;  'max_response': 8192,&#xA;  'stop_sequence': [&#xA;    'Explanations',&#xA;    'Explanation'&#xA;  ],&#xA;  'system': 'You are an expert document based Question-Answering tool. Your task is to answer the 2 questions using the image of only the first page in the document as reference for layout and empty values, but the extraction of values must be from the ocr text.',&#xA;  'messages': [&#xA;      {&#xA;        'role': 'user',&#xA;        'content': [&#xA;          {&#xA;            'type': 'image',&#xA;            'data': 'data:image/png;base64,{{%base64string%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%prompt%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%ocrText%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Use the following output format for your response. Your response should only have the JSON without code blocks as the response. Do not include any of your analyses statements before or after the JSON in the response.'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%outputStructure%}}'&#xA;          }&#xA;        ]&#xA;      },&#xA;      {&#xA;        'role': 'assistant',&#xA;        'content': [&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Based on the invoice document, here are the extracted values in the requested JSON format without any code blocks:'&#xA;          }&#xA;        ]&#xA;      }&#xA;  ]&#xA;}&#xA;" Text="[IonBody]">
                      <ias:Template_Apply.Values>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="8">
                            <x:String>prompt</x:String>
                            <x:String>base64string</x:String>
                            <x:String>model</x:String>
                            <x:String>version</x:String>
                            <x:String>ocrText</x:String>
                            <x:String>outputStructure</x:String>
                          </scg:List>
                          <scg:List x:TypeArguments="x:String" Capacity="8">
                            <x:String>promptRequest</x:String>
                            <x:String>base64string</x:String>
                            <x:String>genAiModel</x:String>
                            <x:String>genAiVersion</x:String>
                            <x:String>value</x:String>
                            <x:String>outputStructure</x:String>
                          </scg:List>
                        </scg:List>
                      </ias:Template_Apply.Values>
                    </ias:Template_Apply>
                    <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_4" Template="{}{ &#xA;'config': { &#xA;'temperature': 0, &#xA;'max_response':8192,&#xA;'stop_sequence': [&#xA;                      'Explanations','Explanation'&#xA;                ]   &#xA;}, &#xA;'prompt':'{{%prompt%}}',&#xA; 'model': '{{%model%}}',   'version': '{{%version%}}','encoded_image': 'data:image/png;base64,{{%base64string%}}'}&#xA;" Text="[IonBody]">
                      <ias:Template_Apply.Values>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>prompt</x:String>
                            <x:String>base64string</x:String>
                            <x:String>model</x:String>
                            <x:String>version</x:String>
                          </scg:List>
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>promptRequest</x:String>
                            <x:String>base64string</x:String>
                            <x:String>genAiModel</x:String>
                            <x:String>genAiVersion</x:String>
                          </scg:List>
                        </scg:List>
                      </ias:Template_Apply.Values>
                    </ias:Template_Apply>
                  </iad1:CommentOut.Activities>
                </iad1:CommentOut>
              </Sequence>
            </If.Then>
          </If>
          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_5">
            <Sequence.Variables>
              <Variable x:TypeArguments="x:Int32" Name="genAIRespCode" />
              <Variable x:TypeArguments="scg:Dictionary(x:String, x:String)" Name="invoiceDictionary" />
            </Sequence.Variables>
            <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_1" JTokenObject="[genAIRequestToken]" JTokenString="[IonBody]" />
            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_2" PostData="[genAIRequestToken.tostring]" Response="[genAIResponse]" StatusCode="[genAIRespCode]" Url="[tenantID + &quot;GENAI/chatsvc/api/v1/messages&quot;]">
              <iai:IONAPIRequestWizard.Headers>
                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>x-infor-logicalidprefix</x:String>
                    <x:String>Accept</x:String>
                  </scg:List>
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>lid://infor.colemanddp</x:String>
                    <x:String>application/json</x:String>
                  </scg:List>
                </scg:List>
              </iai:IONAPIRequestWizard.Headers>
              <iai:IONAPIRequestWizard.QueryParameters>
                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                  <scg:List x:TypeArguments="x:String" Capacity="0" />
                  <scg:List x:TypeArguments="x:String" Capacity="0" />
                </scg:List>
              </iai:IONAPIRequestWizard.QueryParameters>
            </iai:IONAPIRequestWizard>
            <If Condition="[genAIRespCode = 200]" sap2010:WorkflowViewState.IdRef="If_4">
              <If.Then>
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_10">
                  <Sequence.Variables>
                    <Variable x:TypeArguments="njl:JArray" Name="jArr2" />
                    <Variable x:TypeArguments="scg:List(x:String)" Name="lines" />
                    <Variable x:TypeArguments="njl:JObject" Name="jobj" />
                    <Variable x:TypeArguments="x:String" Name="dnStr" />
                  </Sequence.Variables>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_4">
                    <Assign.To>
                      <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(genAIResponse.ReadAsText)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_5">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[out2]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[out1("content").ToString.Replace("\n","")]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_6">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[out2]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[(out2.ToString.Replace("None","")).Replace("''''","''").Replace("\","\\").replace("\\""","")]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_101">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[out2]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[out2.Substring(out2.Indexof("{"c),out2.LastIndexOf("}"c) - out2.Indexof("{"c)+1)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <iad1:CommentOut DisplayName="Comment Out">
                    <iad1:CommentOut.Activities>
                      <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_1" Selection="OK" Text="[out2]" />
                    </iad1:CommentOut.Activities>
                    <sap2010:WorkflowViewState.IdRef>CommentOut_14</sap2010:WorkflowViewState.IdRef>
                  </iad1:CommentOut>
                  <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_7">
                    <TryCatch.Try>
                      <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_5" JTokenObject="[jout]" JTokenString="[out2.ToString]" />
                    </TryCatch.Try>
                    <TryCatch.Catches>
                      <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_7">
                        <ActivityAction x:TypeArguments="s:Exception">
                          <ActivityAction.Argument>
                            <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                          </ActivityAction.Argument>
                        </ActivityAction>
                      </Catch>
                    </TryCatch.Catches>
                  </TryCatch>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_13">
                    <Assign.To>
                      <OutArgument x:TypeArguments="njl:JArray">[jArr1]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="njl:JArray">[JArray.Parse(jout("Headers")(0).ToString)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_14">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Int32">[i]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Int32">0</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_102">
                    <Assign.To>
                      <OutArgument x:TypeArguments="scg:List(x:String)">[dList]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_3" Values="[jArr1]">
                    <ActivityAction x:TypeArguments="njl:JToken">
                      <ActivityAction.Argument>
                        <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
                      </ActivityAction.Argument>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_7">
                        <If Condition="[i = 11]" sap2010:WorkflowViewState.IdRef="If_8">
                          <If.Then>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_16">
                              <Sequence.Variables>
                                <Variable x:TypeArguments="x:Int32" Name="j" />
                              </Sequence.Variables>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_35">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="njl:JArray">[jArr2]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="njl:JArray">[JArray.Parse(jout("Lines").ToString)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_5" Values="[jArr2]">
                                <ActivityAction x:TypeArguments="njl:JToken">
                                  <ActivityAction.Argument>
                                    <DelegateInArgument x:TypeArguments="njl:JToken" Name="item1" />
                                  </ActivityAction.Argument>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_15">
                                    <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_4">
                                      <TryCatch.Try>
                                        <If Condition="[ocrValues(i).Contains(item1(8).ToString)]" sap2010:WorkflowViewState.IdRef="If_9">
                                          <If.Else>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_18">
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_117">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[dnStr]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[item1(8).ToString]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <If Condition="[dnStr &lt;&gt; &quot;&quot; AND dnStr &lt;&gt; &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_16">
                                                <If.Then>
                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_26">
                                                    <If Condition="[JArray.Parse(dnStr)(0)(0).ToString.StartsWith(&quot;[&quot;)]" sap2010:WorkflowViewState.IdRef="If_39">
                                                      <If.Then>
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_119">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="njl:JToken">[tempJArray]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="njl:JToken">[JArray.Parse(dnStr)(0)]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                      </If.Then>
                                                      <If.Else>
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_120">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="njl:JToken">[tempJArray]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="njl:JToken">[JArray.Parse(dnStr)]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                      </If.Else>
                                                    </If>
                                                    <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_8">
                                                      <TryCatch.Try>
                                                        <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_13" Values="[tempJArray]">
                                                          <ActivityAction x:TypeArguments="njl:JToken">
                                                            <ActivityAction.Argument>
                                                              <DelegateInArgument x:TypeArguments="njl:JToken" Name="item3" />
                                                            </ActivityAction.Argument>
                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_39">
                                                              <If Condition="[dnStr.Trim = &quot;&quot; OR dList.Contains(item3(0).ToString)]" sap2010:WorkflowViewState.IdRef="If_28">
                                                                <If.Else>
                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_41">
                                                                    <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_14" MethodName="Add">
                                                                      <InvokeMethod.TargetObject>
                                                                        <InArgument x:TypeArguments="scg:List(x:String)">[dList]</InArgument>
                                                                      </InvokeMethod.TargetObject>
                                                                      <InArgument x:TypeArguments="x:String">[item3(0).ToString]</InArgument>
                                                                    </InvokeMethod>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_97">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[ocrValues(i)]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">[ocrValues(i) +","+ item3(0).ToString]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                  </Sequence>
                                                                </If.Else>
                                                              </If>
                                                            </Sequence>
                                                          </ActivityAction>
                                                        </ForEach>
                                                      </TryCatch.Try>
                                                      <TryCatch.Catches>
                                                        <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_8">
                                                          <ActivityAction x:TypeArguments="s:Exception">
                                                            <ActivityAction.Argument>
                                                              <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                            </ActivityAction.Argument>
                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_40">
                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_98">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="x:String">[ocrValues(i)]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="x:String">[ocrValues(i) + item1(8)(0).ToString]</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                            </Sequence>
                                                          </ActivityAction>
                                                        </Catch>
                                                      </TryCatch.Catches>
                                                    </TryCatch>
                                                  </Sequence>
                                                </If.Then>
                                                <If.Else>
                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_27">
                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_55">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="x:String">[ocrValues(i)]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="x:String">[ocrValues(i)]</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                  </Sequence>
                                                </If.Else>
                                              </If>
                                            </Sequence>
                                          </If.Else>
                                        </If>
                                      </TryCatch.Try>
                                      <TryCatch.Catches>
                                        <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_4">
                                          <ActivityAction x:TypeArguments="s:Exception">
                                            <ActivityAction.Argument>
                                              <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                            </ActivityAction.Argument>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_77">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[ocrValues(i)]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">
                                                  <Literal x:TypeArguments="x:String" Value="" />
                                                </InArgument>
                                              </Assign.Value>
                                            </Assign>
                                          </ActivityAction>
                                        </Catch>
                                      </TryCatch.Catches>
                                    </TryCatch>
                                  </Sequence>
                                </ActivityAction>
                              </ForEach>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_40">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[ocrValues(11)]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[ocrValues(11).Replace( Environment.NewLine , "").Replace(" ","").Replace("][",",")]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <If Condition="[ocrValues(11).StartsWith(&quot;,&quot;)]" sap2010:WorkflowViewState.IdRef="If_27">
                                <If.Then>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_96">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[ocrValues(11)]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[ocrValues(11).Substring(1)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </If.Then>
                              </If>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_115">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[ocrValues(11)]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[Regex.Replace(ocrValues(11),",{2,}", ",").Trim(","c)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_39">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[ocrValues(i+1)]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[item.ToString]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_38">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:Int32">[i]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:Int32">[i+2]</InArgument>
                                </Assign.Value>
                              </Assign>
                            </Sequence>
                          </If.Then>
                          <If.Else>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_17">
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_15">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[ocrValues(i)]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[item.ToString]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_16">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:Int32">[i]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:Int32">[i+1]</InArgument>
                                </Assign.Value>
                              </Assign>
                            </Sequence>
                          </If.Else>
                        </If>
                      </Sequence>
                    </ActivityAction>
                  </ForEach>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_41">
                    <Assign.To>
                      <OutArgument x:TypeArguments="njl:JArray">[jArr2]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="njl:JArray">[JArray.Parse(jout("Lines").ToString)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_43">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Int32">[i]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Int32">0</InArgument>
                    </Assign.Value>
                  </Assign>
                  <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_5">
                    <TryCatch.Try>
                      <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_7" Values="[jArr2]">
                        <ActivityAction x:TypeArguments="njl:JToken">
                          <ActivityAction.Argument>
                            <DelegateInArgument x:TypeArguments="njl:JToken" Name="item1" />
                          </ActivityAction.Argument>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_34">
                            <Sequence.Variables>
                              <Variable x:TypeArguments="x:String" Name="code" />
                            </Sequence.Variables>
                            <If Condition="[item1(8)(0).ToString &lt;&gt; &quot;&quot; AND item1(8)(0).ToString &lt;&gt; &quot;''&quot;]" sap2010:WorkflowViewState.IdRef="If_30">
                              <If.Then>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_42">
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_123">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[dnStr]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[item1(8).ToString]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <If Condition="[JArray.Parse(dnStr)(0)(0).ToString.StartsWith(&quot;[&quot;)]" sap2010:WorkflowViewState.IdRef="If_41">
                                    <If.Then>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_121">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="njl:JToken">[tempJArray]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="njl:JToken">[JArray.Parse(dnStr)(0)]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                    </If.Then>
                                    <If.Else>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_122">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="njl:JToken">[tempJArray]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="njl:JToken">[JArray.Parse(dnStr)]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                    </If.Else>
                                  </If>
                                  <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_15" Values="[tempJArray]">
                                    <ActivityAction x:TypeArguments="njl:JToken">
                                      <ActivityAction.Argument>
                                        <DelegateInArgument x:TypeArguments="njl:JToken" Name="item3" />
                                      </ActivityAction.Argument>
                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_45">
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_106">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="scg:List(x:String)">[Lines]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_112">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[code]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">[item1(2).ToString]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <If Condition="[item1(2).ToString = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_33">
                                          <If.Then>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_113">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[code]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">[item1(3).ToString]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                          </If.Then>
                                        </If>
                                        <If Condition="[code = &quot;''&quot;]" sap2010:WorkflowViewState.IdRef="If_34">
                                          <If.Then>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_114">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[code]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">
                                                  <Literal x:TypeArguments="x:String" Value="" />
                                                </InArgument>
                                              </Assign.Value>
                                            </Assign>
                                          </If.Then>
                                        </If>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_46">
                                          <InvokeMethod DisplayName="item code InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_21" MethodName="Add">
                                            <InvokeMethod.TargetObject>
                                              <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                            </InvokeMethod.TargetObject>
                                            <InArgument x:TypeArguments="x:String">[code + "(" + item3(0).ToString +"," + item1(9).ToString + ",)"]</InArgument>
                                          </InvokeMethod>
                                          <iad1:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_12">
                                            <iad1:CommentOut.Activities>
                                              <InvokeMethod DisplayName="item code InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_32" MethodName="Add">
                                                <InvokeMethod.TargetObject>
                                                  <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                                </InvokeMethod.TargetObject>
                                                <InArgument x:TypeArguments="x:String">[code + "(" + item3(0).ToString +",2000906988,)"]</InArgument>
                                              </InvokeMethod>
                                            </iad1:CommentOut.Activities>
                                          </iad1:CommentOut>
                                          <InvokeMethod DisplayName="Desc InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_22" MethodName="Add">
                                            <InvokeMethod.TargetObject>
                                              <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                            </InvokeMethod.TargetObject>
                                            <InArgument x:TypeArguments="x:String">[item1(4).ToString]</InArgument>
                                          </InvokeMethod>
                                          <If Condition="[item3(1).ToString = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_35">
                                            <If.Then>
                                              <InvokeMethod DisplayName="Qty InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_27" MethodName="Add">
                                                <InvokeMethod.TargetObject>
                                                  <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                                </InvokeMethod.TargetObject>
                                                <InArgument x:TypeArguments="x:String">[item1(1).ToString]</InArgument>
                                              </InvokeMethod>
                                            </If.Then>
                                            <If.Else>
                                              <InvokeMethod DisplayName="Qty InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_23" MethodName="Add">
                                                <InvokeMethod.TargetObject>
                                                  <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                                </InvokeMethod.TargetObject>
                                                <InArgument x:TypeArguments="x:String">[item3(1).ToString]</InArgument>
                                              </InvokeMethod>
                                            </If.Else>
                                          </If>
                                          <If Condition="[item3(1).ToString &lt;&gt; &quot;&quot; AND item3(2).ToString &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_36">
                                            <If.Then>
                                              <InvokeMethod DisplayName="Unit cost InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_28" MethodName="Add">
                                                <InvokeMethod.TargetObject>
                                                  <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                                </InvokeMethod.TargetObject>
                                                <InArgument x:TypeArguments="x:String">[(Math.Round(Convert.Todecimal(Regex.Replace(item3(2).ToString,"[^\d.,]", "").Trim) / Convert.Todecimal(Regex.Replace(item3(1).ToString,"[^\d.,]", "").Trim),2)).ToString]</InArgument>
                                              </InvokeMethod>
                                            </If.Then>
                                            <If.Else>
                                              <InvokeMethod DisplayName=" Unit cost InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_29" MethodName="Add">
                                                <InvokeMethod.TargetObject>
                                                  <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                                </InvokeMethod.TargetObject>
                                                <InArgument x:TypeArguments="x:String">[item1(5).ToString]</InArgument>
                                              </InvokeMethod>
                                            </If.Else>
                                          </If>
                                          <If Condition="[item3(2).ToString &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_37">
                                            <If.Then>
                                              <InvokeMethod DisplayName="Amount InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_30" MethodName="Add">
                                                <InvokeMethod.TargetObject>
                                                  <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                                </InvokeMethod.TargetObject>
                                                <InArgument x:TypeArguments="x:String">[item3(2).ToString]</InArgument>
                                              </InvokeMethod>
                                            </If.Then>
                                            <If.Else>
                                              <InvokeMethod DisplayName="Amount InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_31" MethodName="Add">
                                                <InvokeMethod.TargetObject>
                                                  <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                                </InvokeMethod.TargetObject>
                                                <InArgument x:TypeArguments="x:String">[item1(7).ToString]</InArgument>
                                              </InvokeMethod>
                                            </If.Else>
                                          </If>
                                        </Sequence>
                                        <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_26" MethodName="Add">
                                          <InvokeMethod.TargetObject>
                                            <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[ocrLineValues]</InArgument>
                                          </InvokeMethod.TargetObject>
                                          <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                        </InvokeMethod>
                                      </Sequence>
                                    </ActivityAction>
                                  </ForEach>
                                </Sequence>
                              </If.Then>
                              <If.Else>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_43">
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_107">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="scg:List(x:String)">[Lines]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_108">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[code]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[item1(2).ToString]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <If Condition="[item1(2).ToString = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_31">
                                    <If.Then>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_109">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[code]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[item1(3).ToString]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                    </If.Then>
                                  </If>
                                  <If Condition="[code = &quot;''&quot;]" sap2010:WorkflowViewState.IdRef="If_32">
                                    <If.Then>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_110">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[code]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">
                                            <Literal x:TypeArguments="x:String" Value="" />
                                          </InArgument>
                                        </Assign.Value>
                                      </Assign>
                                    </If.Then>
                                  </If>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_44">
                                    <InvokeMethod DisplayName="item code InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_15" MethodName="Add">
                                      <InvokeMethod.TargetObject>
                                        <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                      </InvokeMethod.TargetObject>
                                      <InArgument x:TypeArguments="x:String">[code]</InArgument>
                                    </InvokeMethod>
                                    <InvokeMethod DisplayName="Desc InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_16" MethodName="Add">
                                      <InvokeMethod.TargetObject>
                                        <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                      </InvokeMethod.TargetObject>
                                      <InArgument x:TypeArguments="x:String">[item1(4).ToString]</InArgument>
                                    </InvokeMethod>
                                    <InvokeMethod DisplayName="Qty InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_17" MethodName="Add">
                                      <InvokeMethod.TargetObject>
                                        <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                      </InvokeMethod.TargetObject>
                                      <InArgument x:TypeArguments="x:String">[item1(1).ToString]</InArgument>
                                    </InvokeMethod>
                                    <InvokeMethod DisplayName=" Unit cost InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_18" MethodName="Add">
                                      <InvokeMethod.TargetObject>
                                        <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                      </InvokeMethod.TargetObject>
                                      <InArgument x:TypeArguments="x:String">[item1(5).ToString]</InArgument>
                                    </InvokeMethod>
                                    <InvokeMethod DisplayName="Amount InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_19" MethodName="Add">
                                      <InvokeMethod.TargetObject>
                                        <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                      </InvokeMethod.TargetObject>
                                      <InArgument x:TypeArguments="x:String">[item1(7).ToString]</InArgument>
                                    </InvokeMethod>
                                  </Sequence>
                                  <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_20" MethodName="Add">
                                    <InvokeMethod.TargetObject>
                                      <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[ocrLineValues]</InArgument>
                                    </InvokeMethod.TargetObject>
                                    <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                  </InvokeMethod>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_111">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Int32">[i]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Int32">[i+1]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </Sequence>
                              </If.Else>
                            </If>
                          </Sequence>
                        </ActivityAction>
                      </ForEach>
                    </TryCatch.Try>
                    <TryCatch.Catches>
                      <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_5">
                        <ActivityAction x:TypeArguments="s:Exception">
                          <ActivityAction.Argument>
                            <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                          </ActivityAction.Argument>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_83">
                            <Assign.To>
                              <OutArgument x:TypeArguments="scg:List(scg:List(x:String))">[ocrLineValues]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[New List(Of List(Of String))]</InArgument>
                            </Assign.Value>
                          </Assign>
                        </ActivityAction>
                      </Catch>
                    </TryCatch.Catches>
                  </TryCatch>
                </Sequence>
              </If.Then>
            </If>
          </Sequence>
        </Sequence>
      </If.Then>
      <If.Else>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_2" />
      </If.Else>
    </If>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="File_Read_1" sap:VirtualizedContainerService.HintSize="1841,22" />
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="1841,60" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="1841,60" />
      <sap2010:ViewStateData Id="Assign_88" sap:VirtualizedContainerService.HintSize="1841,60" />
      <sap2010:ViewStateData Id="Assign_74" sap:VirtualizedContainerService.HintSize="1841,60" />
      <sap2010:ViewStateData Id="Directory_GetFiles_5" sap:VirtualizedContainerService.HintSize="1841,22" />
      <sap2010:ViewStateData Id="Assign_70" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="File_Read_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_32" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_71" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_2" sap:VirtualizedContainerService.HintSize="404.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_2" sap:VirtualizedContainerService.HintSize="418.666666666667,486" />
      <sap2010:ViewStateData Id="Assign_72" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_20" sap:VirtualizedContainerService.HintSize="1841,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_75" sap:VirtualizedContainerService.HintSize="664.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_89" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_23" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="Assign_91" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_25" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="Sequence_37" sap:VirtualizedContainerService.HintSize="486,596">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_24" sap:VirtualizedContainerService.HintSize="612,750" />
      <sap2010:ViewStateData Id="Sequence_36" sap:VirtualizedContainerService.HintSize="634,874">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_10" sap:VirtualizedContainerService.HintSize="664.666666666667,1026.66666666667" />
      <sap2010:ViewStateData Id="Assign_92" sap:VirtualizedContainerService.HintSize="664.666666666667,62" />
      <sap2010:ViewStateData Id="Sequence_35" sap:VirtualizedContainerService.HintSize="686.666666666667,1354.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_76" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_3" sap:VirtualizedContainerService.HintSize="691.333333333333,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_3" sap:VirtualizedContainerService.HintSize="1841,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="DocumentOC_1" sap:VirtualizedContainerService.HintSize="1841,22" />
      <sap2010:ViewStateData Id="OpenBrowser_2" sap:VirtualizedContainerService.HintSize="592.666666666667,58" />
      <sap2010:ViewStateData Id="NavigateTo_2" sap:VirtualizedContainerService.HintSize="592.666666666667,58" />
      <sap2010:ViewStateData Id="MaximizeWindow_2" sap:VirtualizedContainerService.HintSize="592.666666666667,22" />
      <sap2010:ViewStateData Id="SendKeys_1" sap:VirtualizedContainerService.HintSize="592.666666666667,22" />
      <sap2010:ViewStateData Id="SendKeys_3" sap:VirtualizedContainerService.HintSize="592.666666666667,22" />
      <sap2010:ViewStateData Id="SendKeys_2" sap:VirtualizedContainerService.HintSize="592.666666666667,22" />
      <sap2010:ViewStateData Id="ScreenShot_2" sap:VirtualizedContainerService.HintSize="592.666666666667,22" />
      <sap2010:ViewStateData Id="FileToBase64_2" sap:VirtualizedContainerService.HintSize="592.666666666667,22" />
      <sap2010:ViewStateData Id="CloseBrowser_2" sap:VirtualizedContainerService.HintSize="592.666666666667,22" />
      <sap2010:ViewStateData Id="Sequence_29" sap:VirtualizedContainerService.HintSize="614.666666666667,714">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_18" sap:VirtualizedContainerService.HintSize="1594,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_93" sap:VirtualizedContainerService.HintSize="1594,60" />
      <sap2010:ViewStateData Id="Path_Validate_1" sap:VirtualizedContainerService.HintSize="1594,22" />
      <sap2010:ViewStateData Id="File_Read_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_26" sap:VirtualizedContainerService.HintSize="1594,208" />
      <sap2010:ViewStateData Id="Assign_28" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_87" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="CommentOut_9" sap:VirtualizedContainerService.HintSize="242,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_94" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_29" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_104" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_105" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_103" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Template_Apply_8" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Template_Apply_9" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_13" sap:VirtualizedContainerService.HintSize="242,118" />
      <sap2010:ViewStateData Id="Template_Apply_7" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Template_Apply_4" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_11" sap:VirtualizedContainerService.HintSize="242,170">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_11" sap:VirtualizedContainerService.HintSize="264,1112">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_7" sap:VirtualizedContainerService.HintSize="1594,1260">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="DeserializeJSON_1" sap:VirtualizedContainerService.HintSize="1572,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_2" sap:VirtualizedContainerService.HintSize="1572,22" />
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="1425,60" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="1425,60" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="1425,60" />
      <sap2010:ViewStateData Id="Assign_101" sap:VirtualizedContainerService.HintSize="1425,60" />
      <sap2010:ViewStateData Id="MessageBox_1" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_14" sap:VirtualizedContainerService.HintSize="1425,118" />
      <sap2010:ViewStateData Id="DeserializeJSON_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_7" sap:VirtualizedContainerService.HintSize="404,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_7" sap:VirtualizedContainerService.HintSize="1425,287" />
      <sap2010:ViewStateData Id="Assign_13" sap:VirtualizedContainerService.HintSize="1425,60" />
      <sap2010:ViewStateData Id="Assign_14" sap:VirtualizedContainerService.HintSize="1425,60" />
      <sap2010:ViewStateData Id="Assign_102" sap:VirtualizedContainerService.HintSize="1425,60" />
      <sap2010:ViewStateData Id="Assign_35" sap:VirtualizedContainerService.HintSize="1062,60" />
      <sap2010:ViewStateData Id="Assign_117" sap:VirtualizedContainerService.HintSize="845,60" />
      <sap2010:ViewStateData Id="Assign_119" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_120" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_39" sap:VirtualizedContainerService.HintSize="534,208" />
      <sap2010:ViewStateData Id="InvokeMethod_14" sap:VirtualizedContainerService.HintSize="242,128" />
      <sap2010:ViewStateData Id="Assign_97" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_41" sap:VirtualizedContainerService.HintSize="264,352">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_28" sap:VirtualizedContainerService.HintSize="464,500" />
      <sap2010:ViewStateData Id="Sequence_39" sap:VirtualizedContainerService.HintSize="486,624">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_13" sap:VirtualizedContainerService.HintSize="516,772" />
      <sap2010:ViewStateData Id="Assign_98" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_40" sap:VirtualizedContainerService.HintSize="264,181">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Catch`1_8" sap:VirtualizedContainerService.HintSize="520,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_8" sap:VirtualizedContainerService.HintSize="534,999" />
      <sap2010:ViewStateData Id="Sequence_26" sap:VirtualizedContainerService.HintSize="556,1371">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_55" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_27" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_16" sap:VirtualizedContainerService.HintSize="845,1519">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_18" sap:VirtualizedContainerService.HintSize="867,1743">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_9" sap:VirtualizedContainerService.HintSize="992,1891">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_77" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Catch`1_4" sap:VirtualizedContainerService.HintSize="996,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_4" sap:VirtualizedContainerService.HintSize="1010,2118" />
      <sap2010:ViewStateData Id="Sequence_15" sap:VirtualizedContainerService.HintSize="1032,2242">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_5" sap:VirtualizedContainerService.HintSize="1062,2390" />
      <sap2010:ViewStateData Id="Assign_40" sap:VirtualizedContainerService.HintSize="1062,60" />
      <sap2010:ViewStateData Id="Assign_96" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_27" sap:VirtualizedContainerService.HintSize="1062,208" />
      <sap2010:ViewStateData Id="Assign_115" sap:VirtualizedContainerService.HintSize="1062,60" />
      <sap2010:ViewStateData Id="Assign_39" sap:VirtualizedContainerService.HintSize="1062,60" />
      <sap2010:ViewStateData Id="Assign_38" sap:VirtualizedContainerService.HintSize="1062,60" />
      <sap2010:ViewStateData Id="Sequence_16" sap:VirtualizedContainerService.HintSize="1084,3262">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_15" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_16" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_17" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_8" sap:VirtualizedContainerService.HintSize="1373,3410" />
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="1395,3534">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_3" sap:VirtualizedContainerService.HintSize="1425,3682">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_41" sap:VirtualizedContainerService.HintSize="1425,60" />
      <sap2010:ViewStateData Id="Assign_43" sap:VirtualizedContainerService.HintSize="1425,60" />
      <sap2010:ViewStateData Id="Assign_123" sap:VirtualizedContainerService.HintSize="538,60" />
      <sap2010:ViewStateData Id="Assign_121" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_122" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_41" sap:VirtualizedContainerService.HintSize="538,208" />
      <sap2010:ViewStateData Id="Assign_106" sap:VirtualizedContainerService.HintSize="486,60" />
      <sap2010:ViewStateData Id="Assign_112" sap:VirtualizedContainerService.HintSize="486,60" />
      <sap2010:ViewStateData Id="Assign_113" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_33" sap:VirtualizedContainerService.HintSize="486,208" />
      <sap2010:ViewStateData Id="Assign_114" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_34" sap:VirtualizedContainerService.HintSize="486,208" />
      <sap2010:ViewStateData Id="InvokeMethod_21" sap:VirtualizedContainerService.HintSize="464,128" />
      <sap2010:ViewStateData Id="InvokeMethod_32" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="CommentOut_12" sap:VirtualizedContainerService.HintSize="464,224" />
      <sap2010:ViewStateData Id="InvokeMethod_22" sap:VirtualizedContainerService.HintSize="464,128" />
      <sap2010:ViewStateData Id="InvokeMethod_27" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="InvokeMethod_23" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="If_35" sap:VirtualizedContainerService.HintSize="464,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeMethod_28" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="InvokeMethod_29" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="If_36" sap:VirtualizedContainerService.HintSize="464,276" />
      <sap2010:ViewStateData Id="InvokeMethod_30" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="InvokeMethod_31" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="If_37" sap:VirtualizedContainerService.HintSize="464,276" />
      <sap2010:ViewStateData Id="Sequence_46" sap:VirtualizedContainerService.HintSize="486,1407">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeMethod_26" sap:VirtualizedContainerService.HintSize="486,128" />
      <sap2010:ViewStateData Id="Sequence_45" sap:VirtualizedContainerService.HintSize="508,2395">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_15" sap:VirtualizedContainerService.HintSize="538,2543" />
      <sap2010:ViewStateData Id="Sequence_42" sap:VirtualizedContainerService.HintSize="560,3015">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_107" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_108" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_109" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_31" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Assign_110" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_32" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="InvokeMethod_15" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="InvokeMethod_16" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="InvokeMethod_17" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="InvokeMethod_18" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="InvokeMethod_19" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="Sequence_44" sap:VirtualizedContainerService.HintSize="464,924">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeMethod_20" sap:VirtualizedContainerService.HintSize="464,128" />
      <sap2010:ViewStateData Id="Assign_111" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Sequence_43" sap:VirtualizedContainerService.HintSize="486,2012">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_30" sap:VirtualizedContainerService.HintSize="1071,3163" />
      <sap2010:ViewStateData Id="Sequence_34" sap:VirtualizedContainerService.HintSize="1093,3287">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_7" sap:VirtualizedContainerService.HintSize="1123,3435" />
      <sap2010:ViewStateData Id="Assign_83" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Catch`1_5" sap:VirtualizedContainerService.HintSize="1127,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_5" sap:VirtualizedContainerService.HintSize="1425,3662" />
      <sap2010:ViewStateData Id="Sequence_10" sap:VirtualizedContainerService.HintSize="1447,8893">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="1572,9041" />
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="1594,9289">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="1616,11214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="200,99">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="1841,11362" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="1863,12254">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="1903,12534" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>