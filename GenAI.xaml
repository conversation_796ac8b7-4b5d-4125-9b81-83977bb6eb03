﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Desktop;assembly=Infor.Activities.Desktop"
 xmlns:iad1="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Web;assembly=Infor.Activities.Web"
 xmlns:iro="clr-namespace:Infor.RPA.OCR;assembly=Infor.RPA.OCR"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="ocrValues" Type="OutArgument(s:String[])" />
    <x:Property Name="documentPath" Type="InArgument(x:String)" />
    <x:Property Name="ocrLineValues" Type="OutArgument(scg:List(scg:List(x:String)))" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="promptPath" Type="InArgument(x:String)" />
    <x:Property Name="customPrompt" Type="InArgument(x:String)" />
    <x:Property Name="genAiVersion" Type="InArgument(x:String)" />
    <x:Property Name="genAiModel" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.OCR</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System.IO</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.Text.RegularExpressions</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.OCR</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="GenAI Sequence" sap2010:WorkflowViewState.IdRef="Sequence_3">
    <Sequence.Variables>
      <Variable x:TypeArguments="scg:Dictionary(x:String, scg:List(x:String))" Name="ocrDictionary" />
      <Variable x:TypeArguments="njl:JToken" Name="values" />
      <Variable x:TypeArguments="x:Int32" Name="responseCode" />
      <Variable x:TypeArguments="x:String" Name="promptText" />
      <Variable x:TypeArguments="x:String" Name="promptRequest" />
      <Variable x:TypeArguments="x:String" Name="base64string" />
      <Variable x:TypeArguments="x:String" Name="screenshotPath" />
      <Variable x:TypeArguments="scg:List(x:String)" Name="txtFiles" />
      <Variable x:TypeArguments="x:String" Name="pg" />
      <Variable x:TypeArguments="x:Int32" Name="errored" />
    </Sequence.Variables>
    <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Read Text File" sap2010:WorkflowViewState.IdRef="File_Read_1" Source="[promptPath]" Text="[promptText]" />
    <Assign sap2010:WorkflowViewState.IdRef="Assign_1">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(x:String))">[ocrDictionary]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(x:String))">[New Dictionary (Of String, List(Of String))]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_2">
      <Assign.To>
        <OutArgument x:TypeArguments="s:String[]">[ocrValues]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="s:String[]">[New String(20) {"","","", "","","","","","","","","","","","","","","","","",""}]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_88">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(scg:List(x:String))">[ocrLineValues]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[New List(Of List(Of String))]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_74">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[pg]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">
          <Literal x:TypeArguments="x:String" Value="" />
        </InArgument>
      </Assign.Value>
    </Assign>
    <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="True" DirPath="[Path.GetDirectoryName(documentPath)]" DisplayName="Get Files in Directory" FileType="Text" Files="[txtFiles]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_5" IncludeSubDir="True" />
    <If Condition="False" sap2010:WorkflowViewState.IdRef="If_20">
      <If.Then>
        <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_2">
          <TryCatch.Try>
            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_32">
              <Assign sap2010:WorkflowViewState.IdRef="Assign_70">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[base64string]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[txtFiles.Find(Function(s) s.Contains((Path.GetFileName(documentPath)).Split("~"c)(1)))]</InArgument>
                </Assign.Value>
              </Assign>
              <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Read Text File" sap2010:WorkflowViewState.IdRef="File_Read_4" Source="[base64string]" Text="[base64string]" />
            </Sequence>
          </TryCatch.Try>
          <TryCatch.Catches>
            <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_2">
              <ActivityAction x:TypeArguments="s:Exception">
                <ActivityAction.Argument>
                  <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                </ActivityAction.Argument>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_71">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[base64string]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[Nothing]</InArgument>
                  </Assign.Value>
                </Assign>
              </ActivityAction>
            </Catch>
          </TryCatch.Catches>
        </TryCatch>
      </If.Then>
      <If.Else>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_72">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[base64string]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[Nothing]</InArgument>
          </Assign.Value>
        </Assign>
      </If.Else>
    </If>
    <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_3">
      <TryCatch.Try>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_35">
          <Sequence.Variables>
            <Variable x:TypeArguments="x:String" Name="pg1" />
          </Sequence.Variables>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_75">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[pg1]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[documentPath.Split("~"c)(2)]</InArgument>
            </Assign.Value>
          </Assign>
          <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_10" Values="[documentPath.Split(&quot;~&quot;c)(3).Split(&quot;-&quot;c)]">
            <ActivityAction x:TypeArguments="x:String">
              <ActivityAction.Argument>
                <DelegateInArgument x:TypeArguments="x:String" Name="item" />
              </ActivityAction.Argument>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_36">
                <If Condition="[item &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_24">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_37">
                      <If Condition="[item.contains(&quot;.&quot;)]" sap2010:WorkflowViewState.IdRef="If_23">
                        <If.Then>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_89">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[item]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[item.sPlit("."c)(0)]</InArgument>
                            </Assign.Value>
                          </Assign>
                        </If.Then>
                      </If>
                      <If Condition="[(New List(Of String)(pg1.Split(&quot;,&quot;c)).IndexOf(item) + 1).ToString() &lt;&gt; &quot;0&quot;]" sap2010:WorkflowViewState.IdRef="If_25">
                        <If.Then>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_91">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[pg]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[pg + "," + (New List(Of String)(pg1.Split(","c)).IndexOf(item) + 1).ToString().trim]</InArgument>
                            </Assign.Value>
                          </Assign>
                        </If.Then>
                      </If>
                    </Sequence>
                  </If.Then>
                </If>
              </Sequence>
            </ActivityAction>
          </ForEach>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_92">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[pg]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[pg.substring(1,pg.length-1)]</InArgument>
            </Assign.Value>
          </Assign>
        </Sequence>
      </TryCatch.Try>
      <TryCatch.Catches>
        <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_3">
          <ActivityAction x:TypeArguments="s:Exception">
            <ActivityAction.Argument>
              <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
            </ActivityAction.Argument>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_76">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[pg]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">
                  <Literal x:TypeArguments="x:String" Value="" />
                </InArgument>
              </Assign.Value>
            </Assign>
          </ActivityAction>
        </Catch>
      </TryCatch.Catches>
    </TryCatch>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_124">
      <Assign.To>
        <OutArgument x:TypeArguments="x:Int32">[errored]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:Int32">1</InArgument>
      </Assign.Value>
    </Assign>
    <DoWhile sap2010:WorkflowViewState.IdRef="DoWhile_1" Condition="[errored = 2]">
      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_48">
        <iro:DocumentOC ActivityID="f69e5a30-0b3c-4f29-9d8a-1e6947a44cd8" ContinueOnError="True" DisplayName="Get OCR Text" ErrorCode="[responseCode]" ExecuteFile="True" FilePath="[documentPath]" sap2010:WorkflowViewState.IdRef="DocumentOC_1" Pages="[pg]" ResponseObject="[values]" />
        <If Condition="[responseCode = 200]" sap2010:WorkflowViewState.IdRef="If_1">
          <If.Then>
            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_1">
              <Sequence.Variables>
                <Variable x:TypeArguments="x:String" Name="ocrOutput" />
                <Variable x:TypeArguments="x:String" Name="IonBody" />
                <Variable x:TypeArguments="njl:JToken" Name="IonBodyJson" />
                <Variable x:TypeArguments="njl:JToken" Name="genAIRequestToken" />
                <Variable x:TypeArguments="iru:ResponseObject" Name="genAIResponse" />
                <Variable x:TypeArguments="njl:JToken" Name="out1" />
                <Variable x:TypeArguments="x:String" Name="out2" />
                <Variable x:TypeArguments="njl:JToken" Name="jout" />
                <Variable x:TypeArguments="njl:JArray" Name="jArr1" />
                <Variable x:TypeArguments="x:Int32" Name="i" />
                <Variable x:TypeArguments="njl:JArray" Name="dn" />
                <Variable x:TypeArguments="x:String" Name="customText" />
                <Variable x:TypeArguments="x:Boolean" Name="customExists" />
                <Variable x:TypeArguments="scg:List(x:String)" Name="dList" />
                <Variable x:TypeArguments="x:String" Name="value" />
                <Variable x:TypeArguments="x:String" Name="outputStructure" />
                <Variable x:TypeArguments="njl:JToken" Name="tempJArray" />
              </Sequence.Variables>
              <If Condition="[base64string = Nothing]" sap2010:WorkflowViewState.IdRef="If_18">
                <If.Then>
                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_29">
                    <iaw:OpenBrowser ErrorCode="{x:Null}" Browser="Chrome" ContinueOnError="True" DisplayName="Open Browser" sap2010:WorkflowViewState.IdRef="OpenBrowser_2" PrivateBrowsingEnabled="False" URL="https://www.google.com/" ZoomPercentage="60" />
                    <iaw:NavigateTo ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Navigate To" sap2010:WorkflowViewState.IdRef="NavigateTo_2" URL="[Path.GetDirectoryName(documentPath) + &quot;/&quot; + Uri.EscapeDataString(Path.GetFileName(documentPath))]" WaitAfter="0" WaitBefore="0" ZoomPercentage="100" />
                    <iaw:MaximizeWindow ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Maximize Window" sap2010:WorkflowViewState.IdRef="MaximizeWindow_2" WaitAfter="0" WaitBefore="0" />
                    <iad:SendKeys ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Send Keys" sap2010:WorkflowViewState.IdRef="SendKeys_1" IsWeb="False" Text="^-" WaitAfter="0" WaitBefore="0" />
                    <iad:SendKeys ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Send Keys" sap2010:WorkflowViewState.IdRef="SendKeys_3" IsWeb="False" Text="^-" WaitAfter="0" WaitBefore="1000" />
                    <iad:SendKeys ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Send Keys" sap2010:WorkflowViewState.IdRef="SendKeys_2" IsWeb="False" Text="^-" WaitAfter="0" WaitBefore="1000" />
                    <iaw:ScreenShot ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Take Screenshot" sap2010:WorkflowViewState.IdRef="ScreenShot_2" SaveLocation="[screenshotPath]" WaitAfter="0" WaitBefore="10000" />
                    <ias:FileToBase64 ErrorCode="{x:Null}" Base64="[base64string]" ContinueOnError="True" DisplayName="File to Base64 String" FilePath="[screenshotPath]" sap2010:WorkflowViewState.IdRef="FileToBase64_2" />
                    <iaw:CloseBrowser ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Close Browser" sap2010:WorkflowViewState.IdRef="CloseBrowser_2" />
                  </Sequence>
                </If.Then>
              </If>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_93">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[customText]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">
                    <Literal x:TypeArguments="x:String" Value="" />
                  </InArgument>
                </Assign.Value>
              </Assign>
              <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_1" IsValid="[customExists]" Path="[customPrompt]" />
              <If Condition="[customExists]" sap2010:WorkflowViewState.IdRef="If_26">
                <If.Then>
                  <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Read Text File" sap2010:WorkflowViewState.IdRef="File_Read_5" Source="[customPrompt]" Text="[customText]" />
                </If.Then>
              </If>
              <If Condition="True" sap2010:WorkflowViewState.IdRef="If_7">
                <If.Then>
                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_11">
                    <iad1:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_9">
                      <iad1:CommentOut.Activities>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_28">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[promptText.Replace("{text}",values.tostring)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_87">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[promptRequest.Replace("{text}",Regex.Replace(Values.ToString(), "[^A-Za-z0-9\s!@#$%^&amp;*()_+={}\[\]:;,'&lt;&gt;.?//|-]", String.Empty))]</InArgument>
                          </Assign.Value>
                        </Assign>
                      </iad1:CommentOut.Activities>
                    </iad1:CommentOut>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_94">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[promptText.Replace("{notes}",customText.tostring)]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_29">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[promptRequest.Replace("'","\'")]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_104">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[outputStructure]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[promptRequest.Split(New String() {"====="}, StringSplitOptions.None)(1)]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_105">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[promptRequest.Split(New String() {"====="}, StringSplitOptions.None)(0)]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_103">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[value]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[Regex.Replace(Values.ToString(), "[^A-Za-z0-9\s!@#$%^&amp;*()_+={}\[\]:;,'&lt;&gt;.?//|-]", String.Empty).Replace("'","\'")]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Latest Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_8" Template="{}{&#xA;  'model': '{{%model%}}',&#xA;  'version': '{{%version%}}',&#xA;  'temperature': 0,&#xA;  'max_response': 8192,&#xA;  'stop_sequence': [&#xA;    'Explanations',&#xA;    'Explanation'&#xA;  ],&#xA;  'system': 'You are an expert document based Question-Answering tool. Your task is to answer the questions using the image of only the first page in the document as reference for layout and empty values, but the extraction of values must be from the raw text. Ignore brevity and it is mandatory to include all the requested values from all the pages as in the raw text in your response. In the provided image you will see 1/N. N indicating number of pages in the full document. This means that you should look for the requested values in the question from all those N pages. Without missing a single one. The content of these pages will be available in the raw text indicated by PageNo value. Also it is important that any dates in the response follow the YYYY-MM-DD ISO 8601 date format.',&#xA;  'messages': [&#xA;      {&#xA;        'role': 'user',&#xA;        'content': [&#xA;          {&#xA;            'type': 'image',&#xA;            'data': 'data:image/png;base64,{{%base64string%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Read the following instructions'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%prompt%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%ocrText%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Use the following output format for your response. Your response should only have the JSON without code blocks as the response. Do not include any of your analysis statements before or after the JSON in the response. Do not add any additional objects to the JSON that are not present in the following Output Format Structure.'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%outputStructure%}}'&#xA;          }&#xA;        ]&#xA;      },&#xA;      {&#xA;        'role': 'assistant',&#xA;        'content': [&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Based on the invoice document, here are the extracted values in the requested JSON format without any code blocks:'&#xA;          }&#xA;        ]&#xA;      }&#xA;  ]&#xA;}&#xA;" Text="[IonBody]">
                      <ias:Template_Apply.Values>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="8">
                            <x:String>prompt</x:String>
                            <x:String>base64string</x:String>
                            <x:String>model</x:String>
                            <x:String>version</x:String>
                            <x:String>ocrText</x:String>
                            <x:String>outputStructure</x:String>
                          </scg:List>
                          <scg:List x:TypeArguments="x:String" Capacity="8">
                            <x:String>promptRequest</x:String>
                            <x:String>base64string</x:String>
                            <x:String>genAiModel</x:String>
                            <x:String>genAiVersion</x:String>
                            <x:String>value</x:String>
                            <x:String>outputStructure</x:String>
                          </scg:List>
                        </scg:List>
                      </ias:Template_Apply.Values>
                    </ias:Template_Apply>
                    <iad1:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_13">
                      <iad1:CommentOut.Activities>
                        <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Latest Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_9" Template="{}{&#xA;  'model': '{{%model%}}',&#xA;  'version': '{{%version%}}',&#xA;  'temperature': 0,&#xA;  'max_response': 8192,&#xA;  'stop_sequence': [&#xA;    'Explanations',&#xA;    'Explanation'&#xA;  ],&#xA;  'system': 'You are an expert document based Question-Answering tool. Your task is to answer the 2 questions using the image of only the first page in the document as reference for layout and empty values, but the extraction of values must be from the ocr text.',&#xA;  'messages': [&#xA;      {&#xA;        'role': 'user',&#xA;        'content': [&#xA;          {&#xA;            'type': 'image',&#xA;            'data': 'data:image/png;base64,{{%base64string%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Read the following instructions'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%prompt%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%ocrText%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Use the following output format for your response. Your response should only have the JSON without code blocks as the response. Do not include any of your analysis statements before or after the JSON in the response. Do not add any additional objects to the JSON that are not present in the following Output Format Structure.'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%outputStructure%}}'&#xA;          }&#xA;        ]&#xA;      },&#xA;      {&#xA;        'role': 'assistant',&#xA;        'content': [&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Based on the invoice document, here are the extracted values in the requested JSON format without any code blocks:'&#xA;          }&#xA;        ]&#xA;      }&#xA;  ]&#xA;}&#xA;" Text="[IonBody]">
                          <ias:Template_Apply.Values>
                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                              <scg:List x:TypeArguments="x:String" Capacity="8">
                                <x:String>prompt</x:String>
                                <x:String>base64string</x:String>
                                <x:String>model</x:String>
                                <x:String>version</x:String>
                                <x:String>ocrText</x:String>
                                <x:String>outputStructure</x:String>
                              </scg:List>
                              <scg:List x:TypeArguments="x:String" Capacity="8">
                                <x:String>promptRequest</x:String>
                                <x:String>base64string</x:String>
                                <x:String>genAiModel</x:String>
                                <x:String>genAiVersion</x:String>
                                <x:String>value</x:String>
                                <x:String>outputStructure</x:String>
                              </scg:List>
                            </scg:List>
                          </ias:Template_Apply.Values>
                        </ias:Template_Apply>
                      </iad1:CommentOut.Activities>
                    </iad1:CommentOut>
                    <iad1:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_11">
                      <iad1:CommentOut.Activities>
                        <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Latest Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_7" Template="{}{&#xA;  'model': '{{%model%}}',&#xA;  'version': '{{%version%}}',&#xA;  'max_response': 8192,&#xA;  'stop_sequence': [&#xA;    'Explanations',&#xA;    'Explanation'&#xA;  ],&#xA;  'system': 'You are an expert document based Question-Answering tool. Your task is to answer the 2 questions using the image of only the first page in the document as reference for layout and empty values, but the extraction of values must be from the ocr text.',&#xA;  'messages': [&#xA;      {&#xA;        'role': 'user',&#xA;        'content': [&#xA;          {&#xA;            'type': 'image',&#xA;            'data': 'data:image/png;base64,{{%base64string%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%prompt%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%ocrText%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Use the following output format for your response. Your response should only have the JSON without code blocks as the response. Do not include any of your analyses statements before or after the JSON in the response.'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%outputStructure%}}'&#xA;          }&#xA;        ]&#xA;      },&#xA;      {&#xA;        'role': 'assistant',&#xA;        'content': [&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Based on the invoice document, here are the extracted values in the requested JSON format without any code blocks:'&#xA;          }&#xA;        ]&#xA;      }&#xA;  ]&#xA;}&#xA;" Text="[IonBody]">
                          <ias:Template_Apply.Values>
                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                              <scg:List x:TypeArguments="x:String" Capacity="8">
                                <x:String>prompt</x:String>
                                <x:String>base64string</x:String>
                                <x:String>model</x:String>
                                <x:String>version</x:String>
                                <x:String>ocrText</x:String>
                                <x:String>outputStructure</x:String>
                              </scg:List>
                              <scg:List x:TypeArguments="x:String" Capacity="8">
                                <x:String>promptRequest</x:String>
                                <x:String>base64string</x:String>
                                <x:String>genAiModel</x:String>
                                <x:String>genAiVersion</x:String>
                                <x:String>value</x:String>
                                <x:String>outputStructure</x:String>
                              </scg:List>
                            </scg:List>
                          </ias:Template_Apply.Values>
                        </ias:Template_Apply>
                        <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_4" Template="{}{ &#xA;'config': { &#xA;'temperature': 0, &#xA;'max_response':8192,&#xA;'stop_sequence': [&#xA;                      'Explanations','Explanation'&#xA;                ]   &#xA;}, &#xA;'prompt':'{{%prompt%}}',&#xA; 'model': '{{%model%}}',   'version': '{{%version%}}','encoded_image': 'data:image/png;base64,{{%base64string%}}'}&#xA;" Text="[IonBody]">
                          <ias:Template_Apply.Values>
                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                <x:String>prompt</x:String>
                                <x:String>base64string</x:String>
                                <x:String>model</x:String>
                                <x:String>version</x:String>
                              </scg:List>
                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                <x:String>promptRequest</x:String>
                                <x:String>base64string</x:String>
                                <x:String>genAiModel</x:String>
                                <x:String>genAiVersion</x:String>
                              </scg:List>
                            </scg:List>
                          </ias:Template_Apply.Values>
                        </ias:Template_Apply>
                      </iad1:CommentOut.Activities>
                    </iad1:CommentOut>
                  </Sequence>
                </If.Then>
              </If>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_5">
                <Sequence.Variables>
                  <Variable x:TypeArguments="x:Int32" Name="genAIRespCode" />
                  <Variable x:TypeArguments="scg:Dictionary(x:String, x:String)" Name="invoiceDictionary" />
                </Sequence.Variables>
                <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_1" JTokenObject="[genAIRequestToken]" JTokenString="[IonBody]" />
                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_2" PostData="[genAIRequestToken.tostring]" Response="[genAIResponse]" StatusCode="[genAIRespCode]" Url="[tenantID + &quot;GENAI/chatsvc/api/v1/messages&quot;]">
                  <iai:IONAPIRequestWizard.Headers>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>x-infor-logicalidprefix</x:String>
                        <x:String>Accept</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>lid://infor.colemanddp</x:String>
                        <x:String>application/json</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.Headers>
                  <iai:IONAPIRequestWizard.QueryParameters>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="0" />
                      <scg:List x:TypeArguments="x:String" Capacity="0" />
                    </scg:List>
                  </iai:IONAPIRequestWizard.QueryParameters>
                </iai:IONAPIRequestWizard>
                <If Condition="[genAIRespCode = 200]" sap2010:WorkflowViewState.IdRef="If_4">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_10">
                      <Sequence.Variables>
                        <Variable x:TypeArguments="njl:JArray" Name="jArr2" />
                        <Variable x:TypeArguments="scg:List(x:String)" Name="lines" />
                        <Variable x:TypeArguments="njl:JObject" Name="jobj" />
                        <Variable x:TypeArguments="x:String" Name="dnStr" />
                      </Sequence.Variables>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_4">
                        <Assign.To>
                          <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(genAIResponse.ReadAsText)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_5">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[out2]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[out1("content").ToString.Replace("\n","")]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_6">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[out2]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[(out2.ToString.Replace("None","")).Replace("''''","''").Replace("\","\\").replace("\\""","")]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_101">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[out2]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[out2.Substring(out2.Indexof("{"c),out2.LastIndexOf("}"c) - out2.Indexof("{"c)+1)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <iad1:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_14">
                        <iad1:CommentOut.Activities>
                          <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_1" Selection="OK" Text="[out2]" />
                        </iad1:CommentOut.Activities>
                      </iad1:CommentOut>
                      <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_7">
                        <TryCatch.Try>
                          <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_5" JTokenObject="[jout]" JTokenString="[out2.ToString]" />
                        </TryCatch.Try>
                        <TryCatch.Catches>
                          <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_7">
                            <ActivityAction x:TypeArguments="s:Exception">
                              <ActivityAction.Argument>
                                <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                              </ActivityAction.Argument>
                            </ActivityAction>
                          </Catch>
                        </TryCatch.Catches>
                      </TryCatch>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_13">
                        <Assign.To>
                          <OutArgument x:TypeArguments="njl:JArray">[jArr1]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="njl:JArray">[JArray.Parse(jout("Headers")(0).ToString)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_14">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Int32">[i]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:Int32">0</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_102">
                        <Assign.To>
                          <OutArgument x:TypeArguments="scg:List(x:String)">[dList]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_3" Values="[jArr1]">
                        <ActivityAction x:TypeArguments="njl:JToken">
                          <ActivityAction.Argument>
                            <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
                          </ActivityAction.Argument>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_7">
                            <If Condition="[i = 11]" sap2010:WorkflowViewState.IdRef="If_8">
                              <If.Then>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_16">
                                  <Sequence.Variables>
                                    <Variable x:TypeArguments="x:Int32" Name="j" />
                                  </Sequence.Variables>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_35">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="njl:JArray">[jArr2]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="njl:JArray">[JArray.Parse(jout("Lines").ToString)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_5" Values="[jArr2]">
                                    <ActivityAction x:TypeArguments="njl:JToken">
                                      <ActivityAction.Argument>
                                        <DelegateInArgument x:TypeArguments="njl:JToken" Name="item1" />
                                      </ActivityAction.Argument>
                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_15">
                                        <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_4">
                                          <TryCatch.Try>
                                            <If Condition="[ocrValues(i).Contains(item1(8).ToString)]" sap2010:WorkflowViewState.IdRef="If_9">
                                              <If.Else>
                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_18">
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_117">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[dnStr]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">[item1(8).ToString]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <If Condition="[dnStr &lt;&gt; &quot;&quot; AND dnStr &lt;&gt; &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_16">
                                                    <If.Then>
                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_26">
                                                        <If Condition="[JArray.Parse(dnStr)(0)(0).ToString.StartsWith(&quot;[&quot;)]" sap2010:WorkflowViewState.IdRef="If_39">
                                                          <If.Then>
                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_119">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="njl:JToken">[tempJArray]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="njl:JToken">[JArray.Parse(dnStr)(0)]</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                          </If.Then>
                                                          <If.Else>
                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_120">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="njl:JToken">[tempJArray]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="njl:JToken">[JArray.Parse(dnStr)]</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                          </If.Else>
                                                        </If>
                                                        <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_8">
                                                          <TryCatch.Try>
                                                            <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_13" Values="[tempJArray]">
                                                              <ActivityAction x:TypeArguments="njl:JToken">
                                                                <ActivityAction.Argument>
                                                                  <DelegateInArgument x:TypeArguments="njl:JToken" Name="item3" />
                                                                </ActivityAction.Argument>
                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_39">
                                                                  <If Condition="[dnStr.Trim = &quot;&quot; OR dList.Contains(item3(0).ToString)]" sap2010:WorkflowViewState.IdRef="If_28">
                                                                    <If.Else>
                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_41">
                                                                        <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_14" MethodName="Add">
                                                                          <InvokeMethod.TargetObject>
                                                                            <InArgument x:TypeArguments="scg:List(x:String)">[dList]</InArgument>
                                                                          </InvokeMethod.TargetObject>
                                                                          <InArgument x:TypeArguments="x:String">[item3(0).ToString]</InArgument>
                                                                        </InvokeMethod>
                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_97">
                                                                          <Assign.To>
                                                                            <OutArgument x:TypeArguments="x:String">[ocrValues(i)]</OutArgument>
                                                                          </Assign.To>
                                                                          <Assign.Value>
                                                                            <InArgument x:TypeArguments="x:String">[ocrValues(i) +","+ item3(0).ToString]</InArgument>
                                                                          </Assign.Value>
                                                                        </Assign>
                                                                      </Sequence>
                                                                    </If.Else>
                                                                  </If>
                                                                </Sequence>
                                                              </ActivityAction>
                                                            </ForEach>
                                                          </TryCatch.Try>
                                                          <TryCatch.Catches>
                                                            <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_8">
                                                              <ActivityAction x:TypeArguments="s:Exception">
                                                                <ActivityAction.Argument>
                                                                  <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                </ActivityAction.Argument>
                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_40">
                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_98">
                                                                    <Assign.To>
                                                                      <OutArgument x:TypeArguments="x:String">[ocrValues(i)]</OutArgument>
                                                                    </Assign.To>
                                                                    <Assign.Value>
                                                                      <InArgument x:TypeArguments="x:String">[ocrValues(i) + item1(8)(0).ToString]</InArgument>
                                                                    </Assign.Value>
                                                                  </Assign>
                                                                </Sequence>
                                                              </ActivityAction>
                                                            </Catch>
                                                          </TryCatch.Catches>
                                                        </TryCatch>
                                                      </Sequence>
                                                    </If.Then>
                                                    <If.Else>
                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_27">
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_55">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:String">[ocrValues(i)]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:String">[ocrValues(i)]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                      </Sequence>
                                                    </If.Else>
                                                  </If>
                                                </Sequence>
                                              </If.Else>
                                            </If>
                                          </TryCatch.Try>
                                          <TryCatch.Catches>
                                            <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_4">
                                              <ActivityAction x:TypeArguments="s:Exception">
                                                <ActivityAction.Argument>
                                                  <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                </ActivityAction.Argument>
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_77">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[ocrValues(i)]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String">
                                                      <Literal x:TypeArguments="x:String" Value="" />
                                                    </InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                              </ActivityAction>
                                            </Catch>
                                          </TryCatch.Catches>
                                        </TryCatch>
                                      </Sequence>
                                    </ActivityAction>
                                  </ForEach>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_40">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[ocrValues(11)]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[ocrValues(11).Replace( Environment.NewLine , "").Replace(" ","").Replace("][",",")]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <If Condition="[ocrValues(11).StartsWith(&quot;,&quot;)]" sap2010:WorkflowViewState.IdRef="If_27">
                                    <If.Then>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_96">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[ocrValues(11)]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[ocrValues(11).Substring(1)]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                    </If.Then>
                                  </If>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_115">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[ocrValues(11)]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[Regex.Replace(ocrValues(11),",{2,}", ",").Trim(","c)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_39">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[ocrValues(i+1)]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[item.ToString]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_38">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Int32">[i]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Int32">[i+2]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </Sequence>
                              </If.Then>
                              <If.Else>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_17">
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_15">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[ocrValues(i)]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[item.ToString]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_16">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Int32">[i]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Int32">[i+1]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </Sequence>
                              </If.Else>
                            </If>
                          </Sequence>
                        </ActivityAction>
                      </ForEach>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_41">
                        <Assign.To>
                          <OutArgument x:TypeArguments="njl:JArray">[jArr2]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="njl:JArray">[JArray.Parse(jout("Lines").ToString)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_43">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Int32">[i]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:Int32">0</InArgument>
                        </Assign.Value>
                      </Assign>
                      <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_5">
                        <TryCatch.Try>
                          <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_7" Values="[jArr2]">
                            <ActivityAction x:TypeArguments="njl:JToken">
                              <ActivityAction.Argument>
                                <DelegateInArgument x:TypeArguments="njl:JToken" Name="item1" />
                              </ActivityAction.Argument>
                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_34">
                                <Sequence.Variables>
                                  <Variable x:TypeArguments="x:String" Name="code" />
                                </Sequence.Variables>
                                <If Condition="[item1(8)(0).ToString &lt;&gt; &quot;&quot; AND item1(8)(0).ToString &lt;&gt; &quot;''&quot;]" sap2010:WorkflowViewState.IdRef="If_30">
                                  <If.Then>
                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_42">
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_123">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[dnStr]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[item1(8).ToString]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <If Condition="[JArray.Parse(dnStr)(0)(0).ToString.StartsWith(&quot;[&quot;)]" sap2010:WorkflowViewState.IdRef="If_41">
                                        <If.Then>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_121">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="njl:JToken">[tempJArray]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="njl:JToken">[JArray.Parse(dnStr)(0)]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                        </If.Then>
                                        <If.Else>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_122">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="njl:JToken">[tempJArray]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="njl:JToken">[JArray.Parse(dnStr)]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                        </If.Else>
                                      </If>
                                      <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_15" Values="[tempJArray]">
                                        <ActivityAction x:TypeArguments="njl:JToken">
                                          <ActivityAction.Argument>
                                            <DelegateInArgument x:TypeArguments="njl:JToken" Name="item3" />
                                          </ActivityAction.Argument>
                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_45">
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_106">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="scg:List(x:String)">[Lines]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_112">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[code]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">[item1(2).ToString]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <If Condition="[item1(2).ToString = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_33">
                                              <If.Then>
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_113">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[code]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String">[item1(3).ToString]</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                              </If.Then>
                                            </If>
                                            <If Condition="[code = &quot;''&quot;]" sap2010:WorkflowViewState.IdRef="If_34">
                                              <If.Then>
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_114">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[code]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String">
                                                      <Literal x:TypeArguments="x:String" Value="" />
                                                    </InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                              </If.Then>
                                            </If>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_46">
                                              <InvokeMethod DisplayName="item code InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_21" MethodName="Add">
                                                <InvokeMethod.TargetObject>
                                                  <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                                </InvokeMethod.TargetObject>
                                                <InArgument x:TypeArguments="x:String">[code + "(" + item3(0).ToString +"," + item1(9).ToString + ",)"]</InArgument>
                                              </InvokeMethod>
                                              <iad1:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_12">
                                                <iad1:CommentOut.Activities>
                                                  <InvokeMethod DisplayName="item code InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_32" MethodName="Add">
                                                    <InvokeMethod.TargetObject>
                                                      <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                                    </InvokeMethod.TargetObject>
                                                    <InArgument x:TypeArguments="x:String">[code + "(" + item3(0).ToString +",2000906988,)"]</InArgument>
                                                  </InvokeMethod>
                                                </iad1:CommentOut.Activities>
                                              </iad1:CommentOut>
                                              <InvokeMethod DisplayName="Desc InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_22" MethodName="Add">
                                                <InvokeMethod.TargetObject>
                                                  <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                                </InvokeMethod.TargetObject>
                                                <InArgument x:TypeArguments="x:String">[item1(4).ToString]</InArgument>
                                              </InvokeMethod>
                                              <If Condition="[item3(1).ToString = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_35">
                                                <If.Then>
                                                  <InvokeMethod DisplayName="Qty InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_27" MethodName="Add">
                                                    <InvokeMethod.TargetObject>
                                                      <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                                    </InvokeMethod.TargetObject>
                                                    <InArgument x:TypeArguments="x:String">[Regex.Replace(item1(1).ToString,"[^\d.,]", "").Trim]</InArgument>
                                                  </InvokeMethod>
                                                </If.Then>
                                                <If.Else>
                                                  <InvokeMethod DisplayName="Qty InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_23" MethodName="Add">
                                                    <InvokeMethod.TargetObject>
                                                      <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                                    </InvokeMethod.TargetObject>
                                                    <InArgument x:TypeArguments="x:String">[Regex.Replace(item3(1).ToString,"[^\d.,]", "").Trim]</InArgument>
                                                  </InvokeMethod>
                                                </If.Else>
                                              </If>
                                              <If Condition="[item3(1).ToString &lt;&gt; &quot;&quot; AND item3(2).ToString &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_36">
                                                <If.Then>
                                                  <InvokeMethod DisplayName="Unit cost InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_28" MethodName="Add">
                                                    <InvokeMethod.TargetObject>
                                                      <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                                    </InvokeMethod.TargetObject>
                                                    <InArgument x:TypeArguments="x:String">[(Math.Round(Convert.Todecimal(Regex.Replace(item3(2).ToString,"[^\d.,]", "").Trim) / Convert.Todecimal(Regex.Replace(item3(1).ToString,"[^\d.,]", "").Trim),2)).ToString]</InArgument>
                                                  </InvokeMethod>
                                                </If.Then>
                                                <If.Else>
                                                  <InvokeMethod DisplayName=" Unit cost InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_29" MethodName="Add">
                                                    <InvokeMethod.TargetObject>
                                                      <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                                    </InvokeMethod.TargetObject>
                                                    <InArgument x:TypeArguments="x:String">[Regex.Replace(item1(5).ToString,"[^\d.,]", "").Trim]</InArgument>
                                                  </InvokeMethod>
                                                </If.Else>
                                              </If>
                                              <If Condition="[item3(2).ToString &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_37">
                                                <If.Then>
                                                  <InvokeMethod DisplayName="Amount InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_30" MethodName="Add">
                                                    <InvokeMethod.TargetObject>
                                                      <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                                    </InvokeMethod.TargetObject>
                                                    <InArgument x:TypeArguments="x:String">[Regex.Replace(item3(2).ToString,"[^\d.,]", "").Trim]</InArgument>
                                                  </InvokeMethod>
                                                </If.Then>
                                                <If.Else>
                                                  <InvokeMethod DisplayName="Amount InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_31" MethodName="Add">
                                                    <InvokeMethod.TargetObject>
                                                      <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                                    </InvokeMethod.TargetObject>
                                                    <InArgument x:TypeArguments="x:String">[Regex.Replace(item1(7).ToString,"[^\d.,]", "").Trim]</InArgument>
                                                  </InvokeMethod>
                                                </If.Else>
                                              </If>
                                            </Sequence>
                                            <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_26" MethodName="Add">
                                              <InvokeMethod.TargetObject>
                                                <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[ocrLineValues]</InArgument>
                                              </InvokeMethod.TargetObject>
                                              <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                            </InvokeMethod>
                                          </Sequence>
                                        </ActivityAction>
                                      </ForEach>
                                    </Sequence>
                                  </If.Then>
                                  <If.Else>
                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_43">
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_107">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="scg:List(x:String)">[Lines]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_108">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[code]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[item1(2).ToString]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <If Condition="[item1(2).ToString = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_31">
                                        <If.Then>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_109">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[code]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[item1(3).ToString]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                        </If.Then>
                                      </If>
                                      <If Condition="[code = &quot;''&quot;]" sap2010:WorkflowViewState.IdRef="If_32">
                                        <If.Then>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_110">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[code]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">
                                                <Literal x:TypeArguments="x:String" Value="" />
                                              </InArgument>
                                            </Assign.Value>
                                          </Assign>
                                        </If.Then>
                                      </If>
                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_44">
                                        <InvokeMethod DisplayName="item code InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_15" MethodName="Add">
                                          <InvokeMethod.TargetObject>
                                            <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                          </InvokeMethod.TargetObject>
                                          <InArgument x:TypeArguments="x:String">[code]</InArgument>
                                        </InvokeMethod>
                                        <InvokeMethod DisplayName="Desc InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_16" MethodName="Add">
                                          <InvokeMethod.TargetObject>
                                            <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                          </InvokeMethod.TargetObject>
                                          <InArgument x:TypeArguments="x:String">[item1(4).ToString]</InArgument>
                                        </InvokeMethod>
                                        <InvokeMethod DisplayName="Qty InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_17" MethodName="Add">
                                          <InvokeMethod.TargetObject>
                                            <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                          </InvokeMethod.TargetObject>
                                          <InArgument x:TypeArguments="x:String">[Regex.Replace(item1(1).ToString,"[^\d.,]", "").Trim]</InArgument>
                                        </InvokeMethod>
                                        <InvokeMethod DisplayName=" Unit cost InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_18" MethodName="Add">
                                          <InvokeMethod.TargetObject>
                                            <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                          </InvokeMethod.TargetObject>
                                          <InArgument x:TypeArguments="x:String">[Regex.Replace(item1(5).ToString,"[^\d.,]", "").Trim]</InArgument>
                                        </InvokeMethod>
                                        <InvokeMethod DisplayName="Amount InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_19" MethodName="Add">
                                          <InvokeMethod.TargetObject>
                                            <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                          </InvokeMethod.TargetObject>
                                          <InArgument x:TypeArguments="x:String">[Regex.Replace(item1(7).ToString,"[^\d.,]", "").Trim]</InArgument>
                                        </InvokeMethod>
                                      </Sequence>
                                      <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_20" MethodName="Add">
                                        <InvokeMethod.TargetObject>
                                          <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[ocrLineValues]</InArgument>
                                        </InvokeMethod.TargetObject>
                                        <InArgument x:TypeArguments="scg:List(x:String)">[Lines]</InArgument>
                                      </InvokeMethod>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_111">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:Int32">[i]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:Int32">[i+1]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                    </Sequence>
                                  </If.Else>
                                </If>
                              </Sequence>
                            </ActivityAction>
                          </ForEach>
                        </TryCatch.Try>
                        <TryCatch.Catches>
                          <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_5">
                            <ActivityAction x:TypeArguments="s:Exception">
                              <ActivityAction.Argument>
                                <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                              </ActivityAction.Argument>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_83">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="scg:List(scg:List(x:String))">[ocrLineValues]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[New List(Of List(Of String))]</InArgument>
                                </Assign.Value>
                              </Assign>
                            </ActivityAction>
                          </Catch>
                        </TryCatch.Catches>
                      </TryCatch>
                    </Sequence>
                  </If.Then>
                </If>
              </Sequence>
            </Sequence>
          </If.Then>
          <If.Else>
            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_2">
              <If Condition="[responseCode = 400]" sap2010:WorkflowViewState.IdRef="If_42">
                <If.Then>
                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_47">
                    <Delay Duration="00:00:07" sap2010:WorkflowViewState.IdRef="Delay_1" />
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_125">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:Int32">[errored]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:Int32">[errored + 1]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </Sequence>
                </If.Then>
              </If>
            </Sequence>
          </If.Else>
        </If>
      </Sequence>
    </DoWhile>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="File_Read_1" sap:VirtualizedContainerService.HintSize="2184,22" />
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="2184,62" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="2184,62" />
      <sap2010:ViewStateData Id="Assign_88" sap:VirtualizedContainerService.HintSize="2184,62" />
      <sap2010:ViewStateData Id="Assign_74" sap:VirtualizedContainerService.HintSize="2184,62" />
      <sap2010:ViewStateData Id="Directory_GetFiles_5" sap:VirtualizedContainerService.HintSize="2184,22" />
      <sap2010:ViewStateData Id="Assign_70" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="File_Read_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_32" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_71" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_2" sap:VirtualizedContainerService.HintSize="404.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_2" sap:VirtualizedContainerService.HintSize="418.666666666667,486" />
      <sap2010:ViewStateData Id="Assign_72" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_20" sap:VirtualizedContainerService.HintSize="2184,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_75" sap:VirtualizedContainerService.HintSize="664.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_89" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_23" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="Assign_91" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_25" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="Sequence_37" sap:VirtualizedContainerService.HintSize="486,596">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_24" sap:VirtualizedContainerService.HintSize="612,750" />
      <sap2010:ViewStateData Id="Sequence_36" sap:VirtualizedContainerService.HintSize="634,874">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_10" sap:VirtualizedContainerService.HintSize="664.666666666667,1026.66666666667" />
      <sap2010:ViewStateData Id="Assign_92" sap:VirtualizedContainerService.HintSize="664.666666666667,62" />
      <sap2010:ViewStateData Id="Sequence_35" sap:VirtualizedContainerService.HintSize="686.666666666667,1354.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_76" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_3" sap:VirtualizedContainerService.HintSize="691.333333333333,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_3" sap:VirtualizedContainerService.HintSize="2184,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_124" sap:VirtualizedContainerService.HintSize="2184,62" />
      <sap2010:ViewStateData Id="DocumentOC_1" sap:VirtualizedContainerService.HintSize="2135.33333333333,22" />
      <sap2010:ViewStateData Id="OpenBrowser_2" sap:VirtualizedContainerService.HintSize="592.666666666667,58" />
      <sap2010:ViewStateData Id="NavigateTo_2" sap:VirtualizedContainerService.HintSize="592.666666666667,58" />
      <sap2010:ViewStateData Id="MaximizeWindow_2" sap:VirtualizedContainerService.HintSize="592.666666666667,22" />
      <sap2010:ViewStateData Id="SendKeys_1" sap:VirtualizedContainerService.HintSize="592.666666666667,22" />
      <sap2010:ViewStateData Id="SendKeys_3" sap:VirtualizedContainerService.HintSize="592.666666666667,22" />
      <sap2010:ViewStateData Id="SendKeys_2" sap:VirtualizedContainerService.HintSize="592.666666666667,22" />
      <sap2010:ViewStateData Id="ScreenShot_2" sap:VirtualizedContainerService.HintSize="592.666666666667,22" />
      <sap2010:ViewStateData Id="FileToBase64_2" sap:VirtualizedContainerService.HintSize="592.666666666667,22" />
      <sap2010:ViewStateData Id="CloseBrowser_2" sap:VirtualizedContainerService.HintSize="592.666666666667,22" />
      <sap2010:ViewStateData Id="Sequence_29" sap:VirtualizedContainerService.HintSize="614.666666666667,714">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_18" sap:VirtualizedContainerService.HintSize="1601.33333333333,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_93" sap:VirtualizedContainerService.HintSize="1601.33333333333,62" />
      <sap2010:ViewStateData Id="Path_Validate_1" sap:VirtualizedContainerService.HintSize="1601.33333333333,22" />
      <sap2010:ViewStateData Id="File_Read_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_26" sap:VirtualizedContainerService.HintSize="1601.33333333333,214" />
      <sap2010:ViewStateData Id="Assign_28" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_87" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="CommentOut_9" sap:VirtualizedContainerService.HintSize="242,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_94" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_29" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_104" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_105" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_103" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Template_Apply_8" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Template_Apply_9" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_13" sap:VirtualizedContainerService.HintSize="242,118" />
      <sap2010:ViewStateData Id="Template_Apply_7" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Template_Apply_4" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_11" sap:VirtualizedContainerService.HintSize="242,170">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_11" sap:VirtualizedContainerService.HintSize="264,1122">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_7" sap:VirtualizedContainerService.HintSize="1601.33333333333,1276">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="DeserializeJSON_1" sap:VirtualizedContainerService.HintSize="1579.33333333333,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_2" sap:VirtualizedContainerService.HintSize="1579.33333333333,22" />
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="1431.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="1431.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="1431.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_101" sap:VirtualizedContainerService.HintSize="1431.33333333333,62" />
      <sap2010:ViewStateData Id="MessageBox_1" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_14" sap:VirtualizedContainerService.HintSize="1431.33333333333,118" />
      <sap2010:ViewStateData Id="DeserializeJSON_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_7" sap:VirtualizedContainerService.HintSize="404.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_7" sap:VirtualizedContainerService.HintSize="1431.33333333333,298" />
      <sap2010:ViewStateData Id="Assign_13" sap:VirtualizedContainerService.HintSize="1431.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_14" sap:VirtualizedContainerService.HintSize="1431.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_102" sap:VirtualizedContainerService.HintSize="1431.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_35" sap:VirtualizedContainerService.HintSize="1066.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_117" sap:VirtualizedContainerService.HintSize="847.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_119" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_120" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_39" sap:VirtualizedContainerService.HintSize="535.333333333333,216" />
      <sap2010:ViewStateData Id="InvokeMethod_14" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Assign_97" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_41" sap:VirtualizedContainerService.HintSize="264,360">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_28" sap:VirtualizedContainerService.HintSize="464,514" />
      <sap2010:ViewStateData Id="Sequence_39" sap:VirtualizedContainerService.HintSize="486,638">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_13" sap:VirtualizedContainerService.HintSize="516.666666666667,790.666666666667" />
      <sap2010:ViewStateData Id="Assign_98" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_40" sap:VirtualizedContainerService.HintSize="264,183.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Catch`1_8" sap:VirtualizedContainerService.HintSize="521.333333333333,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_8" sap:VirtualizedContainerService.HintSize="535.333333333333,1028.66666666667" />
      <sap2010:ViewStateData Id="Sequence_26" sap:VirtualizedContainerService.HintSize="557.333333333333,1408.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_55" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_27" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_16" sap:VirtualizedContainerService.HintSize="847.333333333333,1562.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_18" sap:VirtualizedContainerService.HintSize="869.333333333333,1788.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_9" sap:VirtualizedContainerService.HintSize="995.333333333333,1942.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_77" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_4" sap:VirtualizedContainerService.HintSize="1000,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_4" sap:VirtualizedContainerService.HintSize="1014,2180.66666666667" />
      <sap2010:ViewStateData Id="Sequence_15" sap:VirtualizedContainerService.HintSize="1036,2304.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_5" sap:VirtualizedContainerService.HintSize="1066.66666666667,2457.33333333333" />
      <sap2010:ViewStateData Id="Assign_40" sap:VirtualizedContainerService.HintSize="1066.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_96" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_27" sap:VirtualizedContainerService.HintSize="1066.66666666667,216" />
      <sap2010:ViewStateData Id="Assign_115" sap:VirtualizedContainerService.HintSize="1066.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_39" sap:VirtualizedContainerService.HintSize="1066.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_38" sap:VirtualizedContainerService.HintSize="1066.66666666667,62" />
      <sap2010:ViewStateData Id="Sequence_16" sap:VirtualizedContainerService.HintSize="1088.66666666667,3347.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_15" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_16" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_17" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_8" sap:VirtualizedContainerService.HintSize="1378.66666666667,3501.33333333333" />
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="1400.66666666667,3625.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_3" sap:VirtualizedContainerService.HintSize="1431.33333333333,3778">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_41" sap:VirtualizedContainerService.HintSize="1431.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_43" sap:VirtualizedContainerService.HintSize="1431.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_123" sap:VirtualizedContainerService.HintSize="538.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_121" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_122" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_41" sap:VirtualizedContainerService.HintSize="538.666666666667,216" />
      <sap2010:ViewStateData Id="Assign_106" sap:VirtualizedContainerService.HintSize="486,62" />
      <sap2010:ViewStateData Id="Assign_112" sap:VirtualizedContainerService.HintSize="486,62" />
      <sap2010:ViewStateData Id="Assign_113" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_33" sap:VirtualizedContainerService.HintSize="486,216" />
      <sap2010:ViewStateData Id="Assign_114" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_34" sap:VirtualizedContainerService.HintSize="486,216" />
      <sap2010:ViewStateData Id="InvokeMethod_21" sap:VirtualizedContainerService.HintSize="464,134" />
      <sap2010:ViewStateData Id="InvokeMethod_32" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="CommentOut_12" sap:VirtualizedContainerService.HintSize="464,230" />
      <sap2010:ViewStateData Id="InvokeMethod_22" sap:VirtualizedContainerService.HintSize="464,134" />
      <sap2010:ViewStateData Id="InvokeMethod_27" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="InvokeMethod_23" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="If_35" sap:VirtualizedContainerService.HintSize="464,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeMethod_28" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="InvokeMethod_29" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="If_36" sap:VirtualizedContainerService.HintSize="464,288" />
      <sap2010:ViewStateData Id="InvokeMethod_30" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="InvokeMethod_31" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="If_37" sap:VirtualizedContainerService.HintSize="464,288" />
      <sap2010:ViewStateData Id="Sequence_46" sap:VirtualizedContainerService.HintSize="486,1686">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeMethod_26" sap:VirtualizedContainerService.HintSize="486,134" />
      <sap2010:ViewStateData Id="Sequence_45" sap:VirtualizedContainerService.HintSize="508,2700">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_15" sap:VirtualizedContainerService.HintSize="538.666666666667,2852.66666666667" />
      <sap2010:ViewStateData Id="Sequence_42" sap:VirtualizedContainerService.HintSize="560.666666666667,3334.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_107" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_108" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_109" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_31" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="Assign_110" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_32" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="InvokeMethod_15" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="InvokeMethod_16" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="InvokeMethod_17" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="InvokeMethod_18" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="InvokeMethod_19" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="Sequence_44" sap:VirtualizedContainerService.HintSize="464,954">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeMethod_20" sap:VirtualizedContainerService.HintSize="464,134" />
      <sap2010:ViewStateData Id="Assign_111" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Sequence_43" sap:VirtualizedContainerService.HintSize="486,2070">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_30" sap:VirtualizedContainerService.HintSize="1072.66666666667,3488.66666666667" />
      <sap2010:ViewStateData Id="Sequence_34" sap:VirtualizedContainerService.HintSize="1094.66666666667,3612.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_7" sap:VirtualizedContainerService.HintSize="1125.33333333333,3765.33333333333" />
      <sap2010:ViewStateData Id="Assign_83" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Catch`1_5" sap:VirtualizedContainerService.HintSize="1127,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_5" sap:VirtualizedContainerService.HintSize="1431.33333333333,4002.66666666667" />
      <sap2010:ViewStateData Id="Sequence_10" sap:VirtualizedContainerService.HintSize="1453.33333333333,9358.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="1579.33333333333,9512.66666666667" />
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="1601.33333333333,9760.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="1623.33333333333,11711.3333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Delay_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_125" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_47" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_42" sap:VirtualizedContainerService.HintSize="464,402">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="486,526">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="2135.33333333333,11865.3333333333" />
      <sap2010:ViewStateData Id="Sequence_48" sap:VirtualizedContainerService.HintSize="2157.33333333333,12051.3333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="DoWhile_1" sap:VirtualizedContainerService.HintSize="2184,12219.3333333333" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="2206,13162.6666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="2246,13242.6666666667" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>