﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="commentStatus" Type="OutArgument(x:String)" />
    <x:Property Name="Status" Type="OutArgument(x:String)" />
    <x:Property Name="logfile" Type="InArgument(x:String)" />
    <x:Property Name="M3TotalTableRows" Type="InArgument(scg:List(s:String[]))" />
    <x:Property Name="inbnValue" Type="InArgument(x:String)" />
    <x:Property Name="division" Type="InArgument(x:String)" />
    <x:Property Name="charge" Type="InArgument(x:String)" />
    <x:Property Name="ocrValues" Type="InArgument(scg:List(x:String))" />
    <x:Property Name="chargeCode" Type="InArgument(x:String)" />
    <x:Property Name="chargeDiff" Type="OutArgument(x:String)" />
    <x:Property Name="emptyCharge" Type="InArgument(x:Boolean)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>System</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_1">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="req2" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj2" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode2" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj8" />
      <Variable x:TypeArguments="x:String" Name="puno" />
      <Variable x:TypeArguments="x:String" Name="pnli" />
      <Variable x:TypeArguments="x:Decimal" Name="tot" />
      <Variable x:TypeArguments="x:Decimal" Name="totqty" />
      <Variable x:TypeArguments="x:String" Name="ceid" />
      <Variable x:TypeArguments="x:String" Name="cdse" />
      <Variable x:TypeArguments="x:String" Name="grpr" />
      <Variable x:TypeArguments="x:Decimal" Name="totqty1" />
      <Variable x:TypeArguments="x:String" Name="ivqa" />
      <Variable x:TypeArguments="x:String" Name="repn" />
      <Variable x:TypeArguments="njl:JToken" Name="out8" />
    </Sequence.Variables>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_21">
      <Assign.To>
        <OutArgument x:TypeArguments="x:Decimal">[tot]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:Decimal">[0]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_37">
      <Assign.To>
        <OutArgument x:TypeArguments="x:Decimal">[totqty]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:Decimal">[0]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_68">
      <Assign.To>
        <OutArgument x:TypeArguments="x:Decimal">[totqty1]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:Decimal">[0]</InArgument>
      </Assign.Value>
    </Assign>
    <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_3" Values="[M3TotalTableRows]">
      <ActivityAction x:TypeArguments="s:String[]">
        <ActivityAction.Argument>
          <DelegateInArgument x:TypeArguments="s:String[]" Name="rows" />
        </ActivityAction.Argument>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_4">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_17">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[puno]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[rows(6)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_18">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[pnli]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[rows(0)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_81">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[repn]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[rows(1)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_19">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["F3RCAC,F3CDSE,F3CEID from FGRPCL where F3PUNO = " + puno +" and F3SCOC != 0.000000 and F3PNLI = " + pnli + " and F3DIVI = " + division + " AND F3REPN = " + repn]</InArgument>
            </Assign.Value>
          </Assign>
          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Export MI lines IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_5" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/EXPORTMI/Select?HDRS=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
            <iai:IONAPIRequestWizard.Headers>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>Accept</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>application/json</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.Headers>
            <iai:IONAPIRequestWizard.QueryParameters>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>QERY</x:String>
                  <x:String>SEPC</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>req2</x:String>
                  <x:String>~</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.QueryParameters>
          </iai:IONAPIRequestWizard>
          <If Condition="[StatusCode2 = 200]" sap2010:WorkflowViewState.IdRef="If_1">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_10">
                <Sequence.Variables>
                  <Variable x:TypeArguments="njl:JToken" Name="out2" />
                </Sequence.Variables>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_10">
                  <Assign.To>
                    <OutArgument x:TypeArguments="njl:JToken">[out2]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[out2(&quot;results&quot;)(0)(&quot;records&quot;).ToString &lt;&gt; &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_7">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_11">
                      <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_6" Values="[out2(&quot;results&quot;)(0)(&quot;records&quot;)]">
                        <ActivityAction x:TypeArguments="njl:JToken">
                          <ActivityAction.Argument>
                            <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
                          </ActivityAction.Argument>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_20">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:Decimal">[tot]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:Decimal">[tot + Convert.toDecimal(item("REPL").ToString.split("~"C)(0))]</InArgument>
                            </Assign.Value>
                          </Assign>
                        </ActivityAction>
                      </ForEach>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_36">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Decimal">[totqty]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:Decimal">[totqty +Convert.ToDecimal(rows(3))]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                  </If.Then>
                  <If.Else>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_67">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:Decimal">[totqty1]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:Decimal">[totqty1 +Convert.ToDecimal(rows(3))]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </If.Else>
                </If>
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_2">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_5">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching lines charges for the PO " + puno +"."]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_6">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="[commentStatus]" Source="[logfile]" />
              </Sequence>
            </If.Else>
          </If>
        </Sequence>
      </ActivityAction>
    </ForEach>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_80">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[chargeDiff]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[(Convert.ToDecimal(charge) - Convert.ToDecimal(tot)).ToString]</InArgument>
      </Assign.Value>
    </Assign>
    <If Condition="[tot = Convert.toDecimal(charge) OR tot &gt; Convert.toDecimal(charge) OR tot &lt; Convert.toDecimal(charge)]" sap2010:WorkflowViewState.IdRef="If_2">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_8">
          <Sequence.Variables>
            <Variable x:TypeArguments="x:Int32" Name="StatusCode8" />
          </Sequence.Variables>
          <If Condition="[tot = 0 and False]" sap2010:WorkflowViewState.IdRef="If_6">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_9">
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_18">
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_61">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[ceid]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[chargeCode]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_62">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[cdse]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">60</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_63">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[grpr]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[Math.Round(((Convert.toDecimal(charge))/totqty),4).ToString]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_69">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[ivqa]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[totqty.ToString]</InArgument>
                    </Assign.Value>
                  </Assign>
                </Sequence>
                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_9" Response="[respObj8]" StatusCode="[StatusCode8]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                  <iai:IONAPIRequestWizard.Headers>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>Accept</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>application/json</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.Headers>
                  <iai:IONAPIRequestWizard.QueryParameters>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="8">
                        <x:String>INBN</x:String>
                        <x:String>RDTP</x:String>
                        <x:String>DIVI</x:String>
                        <x:String>CDSE</x:String>
                        <x:String>CEID</x:String>
                        <x:String>NLAM</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="8">
                        <x:String>inbnValue</x:String>
                        <x:String>2</x:String>
                        <x:String>division</x:String>
                        <x:String>cdse</x:String>
                        <x:String>ceid</x:String>
                        <x:String>charge</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.QueryParameters>
                </iai:IONAPIRequestWizard>
              </Sequence>
            </If.Then>
            <If.Else>
              <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_4" Values="[M3TotalTableRows]">
                <ActivityAction x:TypeArguments="s:String[]">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="s:String[]" Name="rows" />
                  </ActivityAction.Argument>
                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_7">
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_22">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[puno]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[rows(6)]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_23">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[pnli]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[rows(0)]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_82">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[repn]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[rows(1)]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_25">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">["F3SCOC,F3CDSE,F3CEID from FGRPCL where F3PUNO = " + puno +" and F3SCOC != 0.000000 and F3PNLI = " + pnli + " and F3DIVI = " + division + " AND F3REPN = " + repn]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Export MI lines IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_7" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/EXPORTMI/Select?HDRS=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
                      <iai:IONAPIRequestWizard.Headers>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>Accept</x:String>
                          </scg:List>
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>application/json</x:String>
                          </scg:List>
                        </scg:List>
                      </iai:IONAPIRequestWizard.Headers>
                      <iai:IONAPIRequestWizard.QueryParameters>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>QERY</x:String>
                            <x:String>SEPC</x:String>
                          </scg:List>
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>req2</x:String>
                            <x:String>~</x:String>
                          </scg:List>
                        </scg:List>
                      </iai:IONAPIRequestWizard.QueryParameters>
                    </iai:IONAPIRequestWizard>
                    <If Condition="[StatusCode2 = 200]" sap2010:WorkflowViewState.IdRef="If_3">
                      <If.Then>
                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_5">
                          <Sequence.Variables>
                            <Variable x:TypeArguments="x:String" Name="itno" />
                            <Variable x:TypeArguments="x:String" Name="repn" />
                          </Sequence.Variables>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_14">
                            <Sequence.Variables>
                              <Variable x:TypeArguments="njl:JToken" Name="out2" />
                            </Sequence.Variables>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_47">
                              <Assign.To>
                                <OutArgument x:TypeArguments="njl:JToken">[out2]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign DisplayName="Assign itno" sap2010:WorkflowViewState.IdRef="Assign_50">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[itno]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[rows(5)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_51">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[repn]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[rows(1)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_54">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[ivqa]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[rows(3)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <If Condition="[out2(&quot;results&quot;)(0)(&quot;records&quot;).ToString &lt;&gt; &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_9">
                              <If.Then>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_13">
                                  <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_9" Values="[out2(&quot;results&quot;)(0)(&quot;records&quot;)]">
                                    <ActivityAction x:TypeArguments="njl:JToken">
                                      <ActivityAction.Argument>
                                        <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
                                      </ActivityAction.Argument>
                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_15">
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_52">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[cdse]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">[item("REPL").ToString.split("~"C)(1).ToString()]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_53">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[ceid]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">[item("REPL").ToString.split("~"C)(2).ToString()]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <If Condition="[tot = Convert.toDecimal(charge)]" sap2010:WorkflowViewState.IdRef="If_10">
                                          <If.Then>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_55">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[grpr]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">[Math.Round((Convert.toDecimal(item("REPL").ToString.split("~"C)(0))),4).ToString]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                          </If.Then>
                                          <If.Else>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_16">
                                              <If Condition="[emptyCharge AND tot &gt; 0]" sap2010:WorkflowViewState.IdRef="If_17">
                                                <If.Then>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_89">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[grpr]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">0</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                </If.Then>
                                                <If.Else>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_56">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[grpr]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">[Math.Round(((Convert.toDecimal(item("REPL").ToString.split("~"C)(0))) - (tot - Convert.toDecimal(charge))/totqty),4).ToString()]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                </If.Else>
                                              </If>
                                            </Sequence>
                                          </If.Else>
                                        </If>
                                      </Sequence>
                                    </ActivityAction>
                                  </ForEach>
                                  <If Condition="[emptyCharge AND tot = 0]" sap2010:WorkflowViewState.IdRef="If_18">
                                    <If.Else>
                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_29">
                                        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_4" Response="[respObj8]" StatusCode="[StatusCode8]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                          <iai:IONAPIRequestWizard.Headers>
                                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                <x:String>Accept</x:String>
                                              </scg:List>
                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                <x:String>application/json</x:String>
                                              </scg:List>
                                            </scg:List>
                                          </iai:IONAPIRequestWizard.Headers>
                                          <iai:IONAPIRequestWizard.QueryParameters>
                                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                              <scg:List x:TypeArguments="x:String" Capacity="16">
                                                <x:String>INBN</x:String>
                                                <x:String>RDTP</x:String>
                                                <x:String>DIVI</x:String>
                                                <x:String>CDSE</x:String>
                                                <x:String>CEID</x:String>
                                                <x:String>ITNO</x:String>
                                                <x:String>PNLI</x:String>
                                                <x:String>PUNO</x:String>
                                                <x:String>REPN</x:String>
                                                <x:String>IVQA</x:String>
                                                <x:String>GRPR</x:String>
                                              </scg:List>
                                              <scg:List x:TypeArguments="x:String" Capacity="16">
                                                <x:String>inbnValue</x:String>
                                                <x:String>5</x:String>
                                                <x:String>division</x:String>
                                                <x:String>cdse</x:String>
                                                <x:String>ceid</x:String>
                                                <x:String>itno</x:String>
                                                <x:String>pnli</x:String>
                                                <x:String>puno</x:String>
                                                <x:String>repn</x:String>
                                                <x:String>ivqa</x:String>
                                                <x:String>grpr</x:String>
                                              </scg:List>
                                            </scg:List>
                                          </iai:IONAPIRequestWizard.QueryParameters>
                                        </iai:IONAPIRequestWizard>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_83">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="njl:JToken">[out8]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj8.ReadAsText)]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <If Condition="[out8(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_15">
                                          <If.Then>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_25">
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_84">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[(out8("results")(0)("errorMessage")).ToString]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_10" Line="[commentStatus]" Source="[logfile]" />
                                            </Sequence>
                                          </If.Then>
                                          <If.Else>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_26">
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_85">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">Line charge added</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_11" Line="[commentStatus]" Source="[logfile]" />
                                            </Sequence>
                                          </If.Else>
                                        </If>
                                      </Sequence>
                                    </If.Else>
                                  </If>
                                </Sequence>
                              </If.Then>
                              <If.Else>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_19">
                                  <If Condition="[tot = 0]" sap2010:WorkflowViewState.IdRef="If_11">
                                    <If.Then>
                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_20">
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_64">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[ceid]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">[chargeCode]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_70">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">["F3CDSE from FGRPCL where F3CEID = '" + ceid+"'"]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Export MI lines IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_11" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/EXPORTMI/Select?HDRS=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
                                          <iai:IONAPIRequestWizard.Headers>
                                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                <x:String>Accept</x:String>
                                              </scg:List>
                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                <x:String>application/json</x:String>
                                              </scg:List>
                                            </scg:List>
                                          </iai:IONAPIRequestWizard.Headers>
                                          <iai:IONAPIRequestWizard.QueryParameters>
                                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                <x:String>QERY</x:String>
                                                <x:String>SEPC</x:String>
                                              </scg:List>
                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                <x:String>req2</x:String>
                                                <x:String>~</x:String>
                                              </scg:List>
                                            </scg:List>
                                          </iai:IONAPIRequestWizard.QueryParameters>
                                        </iai:IONAPIRequestWizard>
                                        <If Condition="[StatusCode2 = 200]" sap2010:WorkflowViewState.IdRef="If_13">
                                          <If.Then>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_22">
                                              <Sequence.Variables>
                                                <Variable x:TypeArguments="njl:JToken" Name="out2" />
                                              </Sequence.Variables>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_71">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="njl:JToken">[out2]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <If Condition="[out2(&quot;results&quot;)(0)(&quot;records&quot;).ToString &lt;&gt; &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_12">
                                                <If.Then>
                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_21">
                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_77">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="x:String">[cdse]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="x:String">[out2("results")(0)("records")(0)("REPL").ToString.split("~"C)(0)]</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                  </Sequence>
                                                </If.Then>
                                                <If.Else>
                                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_7" Line="[&quot;No record available for the charge code: &quot; + ceid]" Source="[logfile]" />
                                                </If.Else>
                                              </If>
                                              <If Condition="[cdse &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_14">
                                                <If.Then>
                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_24">
                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_78">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="x:String">[cdse]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="x:String">[out2("results")(0)("records")(0)("REPL").ToString.split("~"C)(0)]</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                    <If Condition="[emptyCharge AND tot &gt; 0]" sap2010:WorkflowViewState.IdRef="If_21">
                                                      <If.Then>
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_95">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:String">[grpr]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:String">0</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                      </If.Then>
                                                      <If.Else>
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_79">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:String">[grpr]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:String">[Math.Round((Convert.toDecimal(charge))/totqty1,4).ToString()]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                      </If.Else>
                                                    </If>
                                                    <If Condition="[emptyCharge AND tot = 0]" sap2010:WorkflowViewState.IdRef="If_20">
                                                      <If.Else>
                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_32">
                                                          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_12" Response="[respObj8]" StatusCode="[StatusCode8]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                            <iai:IONAPIRequestWizard.Headers>
                                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                  <x:String>Accept</x:String>
                                                                </scg:List>
                                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                  <x:String>application/json</x:String>
                                                                </scg:List>
                                                              </scg:List>
                                                            </iai:IONAPIRequestWizard.Headers>
                                                            <iai:IONAPIRequestWizard.QueryParameters>
                                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                  <x:String>INBN</x:String>
                                                                  <x:String>RDTP</x:String>
                                                                  <x:String>DIVI</x:String>
                                                                  <x:String>CDSE</x:String>
                                                                  <x:String>CEID</x:String>
                                                                  <x:String>ITNO</x:String>
                                                                  <x:String>PNLI</x:String>
                                                                  <x:String>PUNO</x:String>
                                                                  <x:String>REPN</x:String>
                                                                  <x:String>IVQA</x:String>
                                                                  <x:String>GRPR</x:String>
                                                                </scg:List>
                                                                <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                  <x:String>inbnValue</x:String>
                                                                  <x:String>5</x:String>
                                                                  <x:String>division</x:String>
                                                                  <x:String>cdse</x:String>
                                                                  <x:String>ceid</x:String>
                                                                  <x:String>itno</x:String>
                                                                  <x:String>pnli</x:String>
                                                                  <x:String>puno</x:String>
                                                                  <x:String>repn</x:String>
                                                                  <x:String>ivqa</x:String>
                                                                  <x:String>grpr</x:String>
                                                                </scg:List>
                                                              </scg:List>
                                                            </iai:IONAPIRequestWizard.QueryParameters>
                                                          </iai:IONAPIRequestWizard>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_86">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="njl:JToken">[out8]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj8.ReadAsText)]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <If Condition="[out8(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_16">
                                                            <If.Then>
                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_27">
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_87">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:String">[(out8("results")(0)("errorMessage")).ToString]</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_12" Line="[commentStatus]" Source="[logfile]" />
                                                              </Sequence>
                                                            </If.Then>
                                                            <If.Else>
                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_28">
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_88">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:String">Line charge added</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_13" Line="[commentStatus]" Source="[logfile]" />
                                                              </Sequence>
                                                            </If.Else>
                                                          </If>
                                                        </Sequence>
                                                      </If.Else>
                                                    </If>
                                                  </Sequence>
                                                </If.Then>
                                                <If.Else>
                                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_8" Line="[&quot;No record available for the charge code: &quot; + ceid]" Source="[logfile]" />
                                                </If.Else>
                                              </If>
                                            </Sequence>
                                          </If.Then>
                                          <If.Else>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_23">
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_75">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching lines charges for the PO " + puno +"."]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_76">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_6" Line="[commentStatus]" Source="[logfile]" />
                                            </Sequence>
                                          </If.Else>
                                        </If>
                                      </Sequence>
                                    </If.Then>
                                  </If>
                                </Sequence>
                              </If.Else>
                            </If>
                          </Sequence>
                        </Sequence>
                      </If.Then>
                      <If.Else>
                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_6">
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_34">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching lines charges for the PO " + puno +"."]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_35">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_2" Line="[commentStatus]" Source="[logfile]" />
                        </Sequence>
                      </If.Else>
                    </If>
                  </Sequence>
                </ActivityAction>
              </ForEach>
            </If.Else>
          </If>
        </Sequence>
      </If.Then>
    </If>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_21" sap:VirtualizedContainerService.HintSize="3179.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_37" sap:VirtualizedContainerService.HintSize="3179.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_68" sap:VirtualizedContainerService.HintSize="3179.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_17" sap:VirtualizedContainerService.HintSize="886.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_18" sap:VirtualizedContainerService.HintSize="886.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_81" sap:VirtualizedContainerService.HintSize="886.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_19" sap:VirtualizedContainerService.HintSize="886.666666666667,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_5" sap:VirtualizedContainerService.HintSize="886.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="574.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_20" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="ForEach`1_6" sap:VirtualizedContainerService.HintSize="284.666666666667,214.666666666667" />
      <sap2010:ViewStateData Id="Assign_36" sap:VirtualizedContainerService.HintSize="284.666666666667,62" />
      <sap2010:ViewStateData Id="Sequence_11" sap:VirtualizedContainerService.HintSize="306.666666666667,440.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_67" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_7" sap:VirtualizedContainerService.HintSize="574.666666666667,594.666666666667" />
      <sap2010:ViewStateData Id="Sequence_10" sap:VirtualizedContainerService.HintSize="596.666666666667,820.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="886.666666666667,974.666666666667" />
      <sap2010:ViewStateData Id="Sequence_4" sap:VirtualizedContainerService.HintSize="908.666666666667,1568.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_3" sap:VirtualizedContainerService.HintSize="3179.33333333333,1721.33333333333" />
      <sap2010:ViewStateData Id="Assign_80" sap:VirtualizedContainerService.HintSize="3179.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_61" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_62" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_63" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_69" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_18" sap:VirtualizedContainerService.HintSize="264,492">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_9" sap:VirtualizedContainerService.HintSize="264,22" />
      <sap2010:ViewStateData Id="Sequence_9" sap:VirtualizedContainerService.HintSize="286,678">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_22" sap:VirtualizedContainerService.HintSize="2666.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_23" sap:VirtualizedContainerService.HintSize="2666.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_82" sap:VirtualizedContainerService.HintSize="2666.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_25" sap:VirtualizedContainerService.HintSize="2666.66666666667,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_7" sap:VirtualizedContainerService.HintSize="2666.66666666667,22" />
      <sap2010:ViewStateData Id="Assign_47" sap:VirtualizedContainerService.HintSize="2332.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_50" sap:VirtualizedContainerService.HintSize="2332.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_51" sap:VirtualizedContainerService.HintSize="2332.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_54" sap:VirtualizedContainerService.HintSize="2332.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_52" sap:VirtualizedContainerService.HintSize="800,62" />
      <sap2010:ViewStateData Id="Assign_53" sap:VirtualizedContainerService.HintSize="800,62" />
      <sap2010:ViewStateData Id="Assign_55" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_89" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_56" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_17" sap:VirtualizedContainerService.HintSize="510,216" />
      <sap2010:ViewStateData Id="Sequence_16" sap:VirtualizedContainerService.HintSize="532,340">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_10" sap:VirtualizedContainerService.HintSize="800,494" />
      <sap2010:ViewStateData Id="Sequence_15" sap:VirtualizedContainerService.HintSize="822,822">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_9" sap:VirtualizedContainerService.HintSize="852.666666666667,974.666666666667" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_4" sap:VirtualizedContainerService.HintSize="554,22" />
      <sap2010:ViewStateData Id="Assign_83" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_84" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_10" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_25" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_85" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_11" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_26" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_15" sap:VirtualizedContainerService.HintSize="554,402" />
      <sap2010:ViewStateData Id="Sequence_29" sap:VirtualizedContainerService.HintSize="576,690">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_18" sap:VirtualizedContainerService.HintSize="852.666666666667,844" />
      <sap2010:ViewStateData Id="Sequence_13" sap:VirtualizedContainerService.HintSize="874.666666666667,1982.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_64" sap:VirtualizedContainerService.HintSize="1262,62" />
      <sap2010:ViewStateData Id="Assign_70" sap:VirtualizedContainerService.HintSize="1262,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_11" sap:VirtualizedContainerService.HintSize="1262,22" />
      <sap2010:ViewStateData Id="Assign_71" sap:VirtualizedContainerService.HintSize="950,62" />
      <sap2010:ViewStateData Id="Assign_77" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_21" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_7" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_12" sap:VirtualizedContainerService.HintSize="950,340" />
      <sap2010:ViewStateData Id="Assign_78" sap:VirtualizedContainerService.HintSize="702,62" />
      <sap2010:ViewStateData Id="Assign_95" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_79" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_21" sap:VirtualizedContainerService.HintSize="702,216" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_12" sap:VirtualizedContainerService.HintSize="554,22" />
      <sap2010:ViewStateData Id="Assign_86" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_87" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_12" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_27" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_88" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_13" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_28" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_16" sap:VirtualizedContainerService.HintSize="554,402" />
      <sap2010:ViewStateData Id="Sequence_32" sap:VirtualizedContainerService.HintSize="576,690">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_20" sap:VirtualizedContainerService.HintSize="702,844" />
      <sap2010:ViewStateData Id="Sequence_24" sap:VirtualizedContainerService.HintSize="724,1326">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_8" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_14" sap:VirtualizedContainerService.HintSize="950,1480" />
      <sap2010:ViewStateData Id="Sequence_22" sap:VirtualizedContainerService.HintSize="972,2086">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_75" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_76" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_6" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_23" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_13" sap:VirtualizedContainerService.HintSize="1262,2240" />
      <sap2010:ViewStateData Id="Sequence_20" sap:VirtualizedContainerService.HintSize="1284,2630">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_11" sap:VirtualizedContainerService.HintSize="1410,2784" />
      <sap2010:ViewStateData Id="Sequence_19" sap:VirtualizedContainerService.HintSize="1432,2908">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_9" sap:VirtualizedContainerService.HintSize="2332.66666666667,3062" />
      <sap2010:ViewStateData Id="Sequence_14" sap:VirtualizedContainerService.HintSize="2354.66666666667,3594">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="2376.66666666667,3718">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_34" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_35" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_6" sap:VirtualizedContainerService.HintSize="264,348">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="2666.66666666667,3872" />
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="2688.66666666667,4466">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_4" sap:VirtualizedContainerService.HintSize="2719.33333333333,4618.66666666667" />
      <sap2010:ViewStateData Id="If_6" sap:VirtualizedContainerService.HintSize="3031.33333333333,4772.66666666667" />
      <sap2010:ViewStateData Id="Sequence_8" sap:VirtualizedContainerService.HintSize="3053.33333333333,4896.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="3179.33333333333,5050.66666666667" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="3201.33333333333,7344">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="3241.33333333333,7544" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>