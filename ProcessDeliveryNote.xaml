﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:sl="clr-namespace:System.Linq;assembly=System.Core"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="commentStatus" Type="OutArgument(x:String)" />
    <x:Property Name="matchVendorItemCode" Type="InArgument(x:Boolean)" />
    <x:Property Name="ocrValues" Type="InArgument(s:String[])" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="colemanAPI" Type="InArgument(x:String)" />
    <x:Property Name="vendorId" Type="InOutArgument(x:String)" />
    <x:Property Name="vendorName" Type="OutArgument(x:String)" />
    <x:Property Name="company" Type="InOutArgument(x:String)" />
    <x:Property Name="poDiscountsHandlingConfig" Type="InArgument(x:Boolean)" />
    <x:Property Name="authUser" Type="InArgument(x:String)" />
    <x:Property Name="OCRText" Type="InArgument(njl:JToken)" />
    <x:Property Name="documentPath" Type="InArgument(x:String)" />
    <x:Property Name="vatCodeConfig" Type="InArgument(x:String)" />
    <x:Property Name="approvalRequired" Type="InArgument(x:Boolean)" />
    <x:Property Name="checkAmountBussinessRule" Type="InArgument(x:String)" />
    <x:Property Name="approvalWorkflow" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
    <x:Property Name="chargeCode" Type="InArgument(x:String)" />
    <x:Property Name="discountCode" Type="InArgument(x:String)" />
    <x:Property Name="status" Type="OutArgument(x:String)" />
    <x:Property Name="division" Type="InOutArgument(x:String)" />
    <x:Property Name="result" Type="InArgument(scg:List(x:String))" />
    <x:Property Name="Eresult" Type="InArgument(scg:List(x:String))" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="correlationID" Type="OutArgument(x:String)" />
    <x:Property Name="ocrLineValues" Type="InArgument(scg:List(scg:List(x:String)))" />
    <x:Property Name="AYear" Type="OutArgument(x:String)" />
    <x:Property Name="InYear" Type="OutArgument(x:String)" />
    <x:Property Name="approvalList" Type="OutArgument(scg:List(x:String))" />
    <x:Property Name="APResp" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.Text.RegularExpressions</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_2">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:Int32" Name="x" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="ColemanOutPut" />
      <Variable x:TypeArguments="x:Int32" Name="ColemanRespCode" />
      <Variable x:TypeArguments="x:String" Name="tepy" />
      <Variable x:TypeArguments="x:Boolean" Name="rctAvail" />
      <Variable x:TypeArguments="x:String" Name="pono" />
      <Variable x:TypeArguments="x:String" Name="bkid" />
      <Variable x:TypeArguments="x:String" Name="sino" />
      <Variable x:TypeArguments="x:String" Name="cuam" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="invoiceExistsOcrWorkflowOutput" />
      <Variable x:TypeArguments="x:Int32" Name="invoiceExistsOcrWorkflowStatus" />
      <Variable x:TypeArguments="x:Boolean" Name="InvoiceAlreadyExists" />
      <Variable x:TypeArguments="x:String" Name="colAmount" />
      <Variable x:TypeArguments="x:String" Name="d" />
      <Variable x:TypeArguments="x:String" Name="ivdate" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respForFiscalYear" />
      <Variable x:TypeArguments="x:Int32" Name="respForFiscalYearCode" />
    </Sequence.Variables>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_791">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(x:String)">[approvalList]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(x:String)">[new list(Of string)]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_535">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[division]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[division]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_536">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[company]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[miscValues("company").ToString]</InArgument>
      </Assign.Value>
    </Assign>
    <Sequence DisplayName="Fiscal year Sequence" sap2010:WorkflowViewState.IdRef="Sequence_391">
      <Assign DisplayName="Assign ivdate" sap2010:WorkflowViewState.IdRef="Assign_772">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[ivdate]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">[DateTime.ParseExact(ocrValues(1),"dd/MM/yyyy",Nothing).ToString("yyyyMMdd")]</InArgument>
        </Assign.Value>
      </Assign>
      <Assign sap2010:WorkflowViewState.IdRef="Assign_776">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[InYear]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">[ivdate.SubString(0,4)]</InArgument>
        </Assign.Value>
      </Assign>
      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Get Fiscal year IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_16" Response="[respForFiscalYear]" StatusCode="[respForFiscalYearCode]" Url="[tenantID+&quot;M3/m3api-rest/v2/execute/CRS900MI/GetSysCalDate&quot;]">
        <iai:IONAPIRequestWizard.Headers>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>Accept</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>application/json</x:String>
            </scg:List>
          </scg:List>
        </iai:IONAPIRequestWizard.Headers>
        <iai:IONAPIRequestWizard.QueryParameters>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>YMD8</x:String>
              <x:String>DIVI</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>ivdate</x:String>
              <x:String>division</x:String>
            </scg:List>
          </scg:List>
        </iai:IONAPIRequestWizard.QueryParameters>
      </iai:IONAPIRequestWizard>
      <If Condition="[respForFiscalYearCode = 200]" sap2010:WorkflowViewState.IdRef="If_332">
        <If.Then>
          <If Condition="[respForFiscalYear.ReadAsJson(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_331">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_392">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_775">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[AYear]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[ivdate.SubString(0,4)]</InArgument>
                  </Assign.Value>
                </Assign>
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_390">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_774">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[AYear]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[respForFiscalYear.ReadAsJson("results")(0)("records")(0)("CYP1").ToString.Substring(0,4)]</InArgument>
                  </Assign.Value>
                </Assign>
              </Sequence>
            </If.Else>
          </If>
        </If.Then>
      </If>
    </Sequence>
    <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;SupInvoiceNo&quot;,ocrValues(0)},{&quot;division&quot;,division},{&quot;AYear&quot;,AYear},{&quot;vendorID&quot;,vendorID}}]" ContinueOnError="True" DisplayName="VerifyInvoiceExists Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_27" OutputArguments="[invoiceExistsOcrWorkflowOutput]" ResponseCode="[invoiceExistsOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\VerifyInvoiceExists.xaml&quot;]" />
    <Assign sap2010:WorkflowViewState.IdRef="Assign_388">
      <Assign.To>
        <OutArgument x:TypeArguments="x:Boolean">[InvoiceAlreadyExists]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:Boolean">[CType(invoiceExistsOcrWorkflowOutput("invoiceExist"),boolean)]</InArgument>
      </Assign.Value>
    </Assign>
    <If Condition="[NOT InvoiceAlreadyExists]" sap2010:WorkflowViewState.IdRef="If_178">
      <If.Then>
        <Sequence DisplayName="ProcessPOInvoiceAPISequence" sap2010:WorkflowViewState.IdRef="Sequence_157">
          <Sequence.Variables>
            <Variable x:TypeArguments="iru:ResponseObject" Name="respObj1" />
            <Variable x:TypeArguments="njl:JToken" Name="out1" />
            <Variable x:TypeArguments="x:String" Name="req1" />
            <Variable x:TypeArguments="x:Int32" Name="count1" />
            <Variable x:TypeArguments="x:String" Name="req2" />
            <Variable x:TypeArguments="iru:ResponseObject" Name="respObj2" />
            <Variable x:TypeArguments="njl:JToken" Name="out2" />
            <Variable x:TypeArguments="scg:List(s:String[])" Name="M3TotalTableRows" />
            <Variable x:TypeArguments="x:Int32" Name="i" />
            <Variable x:TypeArguments="s:String[]" Name="M3Values" />
            <Variable x:TypeArguments="x:String" Name="cono" />
            <Variable x:TypeArguments="x:String" Name="cucd" />
            <Variable x:TypeArguments="x:String" Name="SupplierNo" />
            <Variable x:TypeArguments="x:String" Name="APIString" />
            <Variable x:TypeArguments="x:Int32" Name="StatusCode1" />
            <Variable x:TypeArguments="x:String" Name="pyme" />
            <Variable x:TypeArguments="scg:List(x:String)" Name="invoicesItemNumbers" />
            <Variable x:TypeArguments="x:Boolean" Name="allLinesReceived" />
            <Variable x:TypeArguments="scg:IEnumerable(sl:IGrouping(x:String, s:String[]))" Name="transDateGroups" />
            <Variable x:TypeArguments="scg:Dictionary(x:String, scg:List(s:String[]))" Name="transDateDictionary" />
            <Variable x:TypeArguments="x:String" Name="rctComments" />
            <Variable x:TypeArguments="scg:List(x:String)" Name="deliveryNumbers" />
            <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="vendorOcrWorkflowOutput" />
            <Variable x:TypeArguments="x:Int32" Name="vendorOcrWorkflowStatus" />
            <Variable x:TypeArguments="x:Boolean" Name="dnExists" />
            <Variable x:TypeArguments="x:String" Name="dn" />
            <Variable x:TypeArguments="x:String" Name="withPO" />
            <Variable x:TypeArguments="scg:List(x:String)" Name="vendorResult" />
          </Sequence.Variables>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_437">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(x:String)">[deliveryNumbers]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_300">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(x:String)">[deliveryNumbers]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(x:String)">[ocrValues(11).split(","c).ToList]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_787">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(x:String)">[deliveryNumbers]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(x:String)">[deliveryNumbers.Distinct().ToList()]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign sino" sap2010:WorkflowViewState.IdRef="Assign_370">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[sino]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[ocrValues(0)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign cuam" sap2010:WorkflowViewState.IdRef="Assign_371">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[cuam]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[ocrValues(2)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign ivdate" sap2010:WorkflowViewState.IdRef="Assign_373">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[ivdate]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[DateTime.ParseExact(ocrValues(1),"dd/MM/yyyy",Nothing).ToString("yyyyMMdd")]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign cuam" sap2010:WorkflowViewState.IdRef="Assign_372">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[cuam]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[cuam.Replace(",","")]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_111">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[pono]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[ocrValues(4)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_292">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Boolean">[rctAvail]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Boolean">True</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_294">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[rctComments]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">
                <Literal x:TypeArguments="x:String" Value="" />
              </InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_420">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Boolean">[dnExists]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Boolean">True</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_426">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(s:String[])">[new list(OF string())]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_556">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[withPO]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">False</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_454">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[dn]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">
                <Literal x:TypeArguments="x:String" Value="" />
              </InArgument>
            </Assign.Value>
          </Assign>
          <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_20" Values="[deliveryNumbers]">
            <ActivityAction x:TypeArguments="x:String">
              <ActivityAction.Argument>
                <DelegateInArgument x:TypeArguments="x:String" Name="item0" />
              </ActivityAction.Argument>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_204">
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_170">
                  <Sequence.Variables>
                    <Variable x:TypeArguments="x:String" Name="tolamount" />
                    <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="linesOcrWorkflowOutput" />
                    <Variable x:TypeArguments="x:Int32" Name="linesOcrWorkflowStatus" />
                  </Sequence.Variables>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_397">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String" xml:space="preserve">["F2PNLI,F2REPN,F2SCOC,F2RPQA,F2TRDT,F2ITNO,F2CONO,F2CUCD,F2DIVI,F2SUNO,F2IVQA,F2PUNO,F2SUDO,F2RCAC,F2RPQT,F2SCOP,F2SERA,F2IVQT,F2ICAC,F2PNLS  from FGRECL where F2SUDO = '" + item0.trim() + "' and F2IMST != 9 and F2SUNO = '" + vendorId + "' and F2DIVI = '"+division+"'"]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;miscValues&quot;,miscValues},{&quot;req2&quot;,req2}}]" ContinueOnError="True" DisplayName="Lines extraction Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_30" OutputArguments="[linesOcrWorkflowOutput]" ResponseCode="[linesOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\LinesExtractionWithPO.xaml&quot;]" />
                  <If Condition="[linesOcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_146">
                    <If.Then>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_168">
                        <Sequence.Variables>
                          <Variable x:TypeArguments="scg:List(s:String[])" Name="M3TotalTableRows1" />
                          <Variable x:TypeArguments="x:Boolean" Name="DNinM3exists" />
                        </Sequence.Variables>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_398">
                          <Assign.To>
                            <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows1]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="scg:List(s:String[])">[CType(linesOcrWorkflowOutput("M3TotalTableRows"), List(Of String()))]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_456">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Boolean">[DNinM3exists]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Boolean">[CType(linesOcrWorkflowOutput("DNinM3exists"), Boolean)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <If Condition="[M3TotalTableRows1.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_181">
                          <If.Then>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_212">
                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_76" Line="[&quot;Receipt lines for the delivery note number &quot; + item0.trim + &quot; is extracted.&quot;]" Source="[logfile]" />
                              <If Condition="[dnExists]" sap2010:WorkflowViewState.IdRef="If_200">
                                <If.Then>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_238">
                                    <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_23" Values="[M3TotalTableRows1]">
                                      <ActivityAction x:TypeArguments="s:String[]">
                                        <ActivityAction.Argument>
                                          <DelegateInArgument x:TypeArguments="s:String[]" Name="item" />
                                        </ActivityAction.Argument>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_235">
                                          <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_4" MethodName="Add">
                                            <InvokeMethod.TargetObject>
                                              <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</InArgument>
                                            </InvokeMethod.TargetObject>
                                            <InArgument x:TypeArguments="s:String[]">[item]</InArgument>
                                          </InvokeMethod>
                                        </Sequence>
                                      </ActivityAction>
                                    </ForEach>
                                  </Sequence>
                                </If.Then>
                              </If>
                            </Sequence>
                          </If.Then>
                          <If.Else>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_211">
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_453">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[dn]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[dn + item0.trim + ", "]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <If Condition="[DNinM3exists]" sap2010:WorkflowViewState.IdRef="If_201">
                                <If.Then>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_239">
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_399">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String" xml:space="preserve">["Lines already invoiced for delivery notes " +  dn.Substring(0,dn.Length-2)]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_459">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_83" Line="[&quot;Lines already invoiced for delivery note: &quot; + item0.trim]" Source="[logfile]" />
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_423">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:Boolean">[dnExists]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                  </Sequence>
                                </If.Then>
                                <If.Else>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_270">
                                    <If Condition="[deliveryNumbers.Count = 1]" sap2010:WorkflowViewState.IdRef="If_226">
                                      <If.Then>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_268">
                                          <Sequence.Variables>
                                            <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="vendorResponseDictionary" />
                                            <Variable x:TypeArguments="x:Int32" Name="vendorResponseCode" />
                                            <Variable x:TypeArguments="x:String" Name="withVendor" />
                                          </Sequence.Variables>
                                          <If Condition="[ocrValues(4) &lt;&gt; &quot;&quot; AND miscValues(&quot;MandateDeliveryNoteWhenPo&quot;).ToString.ToLower = &quot;false&quot; AND miscValues(&quot;processDeliveryNote&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_355">
                                            <If.Then>
                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_420">
                                                <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;vendorId&quot;,vendorId},{&quot;projectPath&quot;,projectPath},{&quot;ocrValues&quot;,ocrValues},{&quot;logfile&quot;,logfile},{&quot;miscValues&quot;,miscValues},{&quot;division&quot;,division}}]" ContinueOnError="True" DisplayName="OneDelivery Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_47" OutputArguments="[vendorResponseDictionary]" ResponseCode="[vendorResponseCode]" WorkflowFile="[projectPath+&quot;\OneDeliveryNotFound.xaml&quot;]" />
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_518">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="scg:List(s:String[])">[CType(vendorResponseDictionary("M3TotalTableRows"), List(Of String()))]</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_522">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("commentStatus"), String)]</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_555">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[withPO]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("withPO"), String)]</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_568">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[withVendor]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("withVendor"), String)]</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_521">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("Status"), String)]</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                              </Sequence>
                                            </If.Then>
                                            <If.Else>
                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_421">
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_828">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="scg:List(s:String[])">[new list(OF string())]</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_829">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String">No receipts available for the delivery note number.</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_830">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[withPO]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String">False</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_831">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[withVendor]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String">False</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_832">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String">PONOTRECEIVED</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                              </Sequence>
                                            </If.Else>
                                          </If>
                                        </Sequence>
                                      </If.Then>
                                      <If.Else>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_269">
                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_417">
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_817">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String" xml:space="preserve">["F2PNLI,F2REPN,F2SCOC,F2RPQA,F2TRDT,F2ITNO,F2CONO,F2CUCD,F2DIVI,F2SUNO,F2IVQA,F2PUNO,F2SUDO,F2RCAC,F2RPQT,F2SCOP,F2SERA,F2IVQT,F2ICAC,F2PNLS  from FGRECL where F2SUDO = '" + item0.trim() + "' and F2SUNO = '" + vendorId + "' and F2DIVI = '"+division+"'"]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;miscValues&quot;,miscValues},{&quot;req2&quot;,req2}}]" ContinueOnError="True" DisplayName="Lines extraction Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_70" OutputArguments="[linesOcrWorkflowOutput]" ResponseCode="[linesOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\LinesExtractionWithPO.xaml&quot;]" />
                                            <If Condition="[linesOcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_351">
                                              <If.Then>
                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_416">
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_818">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows1]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="scg:List(s:String[])">[CType(linesOcrWorkflowOutput("M3TotalTableRows"), List(Of String()))]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_819">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:Boolean">[DNinM3exists]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:Boolean">[CType(linesOcrWorkflowOutput("DNinM3exists"), Boolean)]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <If Condition="[M3TotalTableRows1.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_350">
                                                    <If.Else>
                                                      <If Condition="[DNinM3exists]" sap2010:WorkflowViewState.IdRef="If_349">
                                                        <If.Then>
                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_414">
                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_820">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="x:String">["Lines already invoiced for delivery note numbers " + dn.Substring(0,dn.Length-2)]</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_821">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_111" Line="[&quot;Lines already invoiced for delivery note number &quot; + item0.trim]" Source="[logfile]" />
                                                          </Sequence>
                                                        </If.Then>
                                                        <If.Else>
                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_415">
                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_823">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="x:String">["No receipts available for the given Delivery Note numbers " + dn.Substring(0,dn.Length-2)]</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_824">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="x:String">PONOTRECEIVED</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_112" Line="[&quot;No receipts available for the given Delivery Note number: &quot; + item0.trim]" Source="[logfile]" />
                                                          </Sequence>
                                                        </If.Else>
                                                      </If>
                                                    </If.Else>
                                                  </If>
                                                </Sequence>
                                              </If.Then>
                                            </If>
                                          </Sequence>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_529">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:Boolean">[dnExists]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                        </Sequence>
                                      </If.Else>
                                    </If>
                                  </Sequence>
                                </If.Else>
                              </If>
                            </Sequence>
                          </If.Else>
                        </If>
                      </Sequence>
                    </If.Then>
                    <If.Else>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_169">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_296">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[CType(linesOcrWorkflowOutput("commentStatus"), String)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_297">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[CType(linesOcrWorkflowOutput("Status"), String)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_424">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Boolean">[dnExists]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                          </Assign.Value>
                        </Assign>
                      </Sequence>
                    </If.Else>
                  </If>
                </Sequence>
              </Sequence>
            </ActivityAction>
          </ForEach>
          <If Condition="[M3TotalTableRows.Count &gt; 0 AND dnExists]" sap2010:WorkflowViewState.IdRef="If_177">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_207">
                <Sequence.Variables>
                  <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="BROcrWorkflowOutput" />
                  <Variable x:TypeArguments="x:Int32" Name="BROcrWorkflowStatus" />
                  <Variable x:TypeArguments="x:String" Name="tolPercentage" />
                  <Variable x:TypeArguments="x:String" Name="tolAmt" />
                  <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="POocrWorkflowOutput" />
                  <Variable x:TypeArguments="x:Int32" Name="POocrWorkflowStatus" />
                  <Variable x:TypeArguments="scg:List(x:String)" Name="vendorList" />
                  <Variable x:TypeArguments="x:String" Name="httpoutString" />
                  <Variable x:TypeArguments="njl:JToken" Name="httpoutdup" />
                  <Variable x:TypeArguments="x:Decimal" Name="calcTotal" />
                  <Variable x:TypeArguments="x:Boolean" Name="Colemancall" />
                  <Variable x:TypeArguments="x:String" Name="diffamt" />
                  <Variable x:TypeArguments="x:Boolean" Name="optimizerSuccess" />
                  <Variable x:TypeArguments="x:Int32" Name="ValidateStatus" />
                  <Variable x:TypeArguments="x:String" Name="discountTerms" />
                  <Variable x:TypeArguments="x:String" Name="payee" />
                </Sequence.Variables>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_480">
                  <Assign.To>
                    <OutArgument x:TypeArguments="scg:List(x:String)">[vendorList]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="scg:List(x:String)">[M3TotalTableRows.Where(Function(arr) arr.Length &gt; 1). Select(Function(arr) arr(7)).Distinct().ToList()]</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[vendorList.Count &gt; 1]" DisplayName="vendor ID If" sap2010:WorkflowViewState.IdRef="If_231">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_274">
                      <Sequence.Variables>
                        <Variable x:TypeArguments="x:Int32" Name="vendorResponseCode" />
                        <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="vendorResponseDictionary" />
                        <Variable x:TypeArguments="x:String" Name="vendorStr" />
                      </Sequence.Variables>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_523">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[vendorStr]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">
                            <Literal x:TypeArguments="x:String" Value="" />
                          </InArgument>
                        </Assign.Value>
                      </Assign>
                      <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_31" Values="[vendorList]">
                        <ActivityAction x:TypeArguments="x:String">
                          <ActivityAction.Argument>
                            <DelegateInArgument x:TypeArguments="x:String" Name="item" />
                          </ActivityAction.Argument>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_271">
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_524">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[vendorStr]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[vendorStr + item + ";"]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </Sequence>
                        </ActivityAction>
                      </ForEach>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_525">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[vendorStr]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[vendorStr.SubString(0,vendorStr.length-1)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;vendorName&quot;,ocrValues(8)},{&quot;vendorStr&quot;,vendorStr}}]" ContinueOnError="True" DisplayName="VendorID Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_48" OutputArguments="[vendorResponseDictionary]" ResponseCode="[vendorResponseCode]" WorkflowFile="[projectPath+&quot;\vendorID.xaml&quot;]" />
                      <If Condition="[vendorResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_229">
                        <If.Then>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_273">
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_526">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("vendorID"), String)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <If Condition="[NOT vendorId = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_228">
                              <If.Then>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_272">
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_527">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows.Where(Function(arr) arr.Length &gt; 1 AndAlso arr(7) = vendorId).ToList()]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </Sequence>
                              </If.Then>
                            </If>
                          </Sequence>
                        </If.Then>
                      </If>
                    </Sequence>
                  </If.Then>
                  <If.Else>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_275">
                      <If Condition="[vendorList.Count = 1]" sap2010:WorkflowViewState.IdRef="If_230">
                        <If.Then>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_528">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[M3TotalTableRows(0)(7)]</InArgument>
                            </Assign.Value>
                          </Assign>
                        </If.Then>
                      </If>
                    </Sequence>
                  </If.Else>
                </If>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_486">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[division]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[M3TotalTableRows(0)(8)]</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[miscValues(&quot;useBusinessRuleForTelerance&quot;).ToString.ToLower =&quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_245">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_286">
                      <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;VendorID&quot;,vendorId},{&quot;division&quot;,division},{&quot;businessRule&quot;,miscValues(&quot;businessRuleForTolerance&quot;).ToString}}]" ContinueOnError="True" DisplayName="Percentage &amp; amt Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_49" OutputArguments="[BROcrWorkflowOutput]" ResponseCode="[BROcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\DivisionVendorTolerance.xaml&quot;]" />
                      <If Condition="[BROcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_244">
                        <If.Then>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_284">
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_560">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[tolPercentage]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[CType(BROcrWorkflowOutput("percentage"), String)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_561">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[tolAmt]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[CType(BROcrWorkflowOutput("amount"), String)]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </Sequence>
                        </If.Then>
                        <If.Else>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_285">
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_562">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">Percentage and tolerance amount is not obtained</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_563">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">NEEDSVERIFICATION</InArgument>
                              </Assign.Value>
                            </Assign>
                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_88" Line="[commentStatus]" Source="[logfile]" />
                          </Sequence>
                        </If.Else>
                      </If>
                    </Sequence>
                  </If.Then>
                  <If.Else>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_287">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_564">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[tolPercentage]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">100</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_565">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[tolAmt]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">100</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                  </If.Else>
                </If>
                <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_7">
                  <TryCatch.Try>
                    <If Condition="[miscValues(&quot;Comments&quot;).ToString.ToLower.Contains(&quot;approved&quot;)]" sap2010:WorkflowViewState.IdRef="If_366">
                      <If.Then>
                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_429">
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_858">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[tolPercentage]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">100</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_859">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[tolAmt]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[ocrValues(2)]</InArgument>
                            </Assign.Value>
                          </Assign>
                        </Sequence>
                      </If.Then>
                    </If>
                  </TryCatch.Try>
                  <TryCatch.Catches>
                    <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_7">
                      <ActivityAction x:TypeArguments="s:Exception">
                        <ActivityAction.Argument>
                          <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                        </ActivityAction.Argument>
                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_430">
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_860">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[tolPercentage]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">100</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_861">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[tolAmt]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">100</InArgument>
                            </Assign.Value>
                          </Assign>
                        </Sequence>
                      </ActivityAction>
                    </Catch>
                  </TryCatch.Catches>
                </TryCatch>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_569">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[pono]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[M3TotalTableRows(0)(6)]</InArgument>
                  </Assign.Value>
                </Assign>
                <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;pono&quot;,pono}}]" ContinueOnError="True" DisplayName="PO details Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_50" OutputArguments="[POocrWorkflowOutput]" ResponseCode="[POocrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\ExportMI.xaml&quot;]" />
                <If Condition="[POocrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_249">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_289">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_570">
                        <Assign.To>
                          <OutArgument x:TypeArguments="scg:List(x:String)">[EResult]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="scg:List(x:String)">[CType(POocrWorkflowOutput("result"), List(Of String))]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_571">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[company]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[Eresult(0)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_572">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[division]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[Eresult(1)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_573">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[cucd]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[Eresult(3)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_574">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[tepy]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[Eresult(4)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_575">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[pyme]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[Eresult(5)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <If Condition="[vendorId = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_248">
                        <If.Then>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_576">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[CType(POocrWorkflowOutput("vendorId"), String)]</InArgument>
                            </Assign.Value>
                          </Assign>
                        </If.Then>
                      </If>
                    </Sequence>
                  </If.Then>
                </If>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_577">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[vendorId.Split("-"c)(0)]</InArgument>
                  </Assign.Value>
                </Assign>
                <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantId&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;vendorId&quot;,vendorId}}]" ContinueOnError="True" DisplayName="Vendor Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_51" OutputArguments="[vendorOcrWorkflowOutput]" ResponseCode="[vendorOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\getvendordetails.xaml&quot;]" />
                <If Condition="[vendorOcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_251">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_292">
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_290">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_578">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[CType(vendorOcrWorkflowOutput("Status"), String)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_579">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[CType(vendorOcrWorkflowOutput("commentStatus"), String)]</InArgument>
                          </Assign.Value>
                        </Assign>
                      </Sequence>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_580">
                        <Assign.To>
                          <OutArgument x:TypeArguments="scg:List(x:String)">[vendorResult]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="scg:List(x:String)">[CType(vendorOcrWorkflowOutput("result"), List(Of String))]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_581">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[vendorName]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[vendorResult(0)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_583">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[bkid]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[vendorResult(2)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_863">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[discountTerms]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[vendorResult(3)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_868">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[payee]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[vendorResult(4)]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                  </If.Then>
                </If>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_567">
                  <Assign.To>
                    <OutArgument x:TypeArguments="scg:IEnumerable(sl:IGrouping(x:String, s:String[]))">[transDateGroups]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="scg:IEnumerable(sl:IGrouping(x:String, s:String[]))">[M3TotalTableRows.GroupBy(Function(item) item(4))]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_653">
                  <Assign.To>
                    <OutArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(s:String[]))">[transDateDictionary]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(s:String[]))">[new Dictionary(Of String,list(OF string()))]</InArgument>
                  </Assign.Value>
                </Assign>
                <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_10" MethodName="Add">
                  <InvokeMethod.TargetObject>
                    <InArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(s:String[]))">[transDateDictionary]</InArgument>
                  </InvokeMethod.TargetObject>
                  <InArgument x:TypeArguments="x:String">[transDateGroups(0).key]</InArgument>
                  <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</InArgument>
                </InvokeMethod>
                <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_1">
                  <iad:CommentOut.Activities>
                    <If Condition="[miscValues(&quot;groupByTransDate&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_283">
                      <If.Then>
                        <ForEach x:TypeArguments="sl:IGrouping(x:String, s:String[])" DisplayName="ForEach&lt;IGrouping&lt;String,String[]&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_34" Values="[transDateGroups]">
                          <ActivityAction x:TypeArguments="sl:IGrouping(x:String, s:String[])">
                            <ActivityAction.Argument>
                              <DelegateInArgument x:TypeArguments="sl:IGrouping(x:String, s:String[])" Name="group" />
                            </ActivityAction.Argument>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_288">
                              <If Condition="[M3TotalTableRows.Count &gt; 0 and matchVendorItemCode]" sap2010:WorkflowViewState.IdRef="If_247">
                                <If.Then>
                                  <If Condition="[group.ToList().Count &gt;=  ocrLineValues.Count]" sap2010:WorkflowViewState.IdRef="If_246">
                                    <If.Then>
                                      <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_5" MethodName="Add">
                                        <InvokeMethod.TargetObject>
                                          <InArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(s:String[]))">[transDateDictionary]</InArgument>
                                        </InvokeMethod.TargetObject>
                                        <InArgument x:TypeArguments="x:String">[group.Key]</InArgument>
                                        <InArgument x:TypeArguments="scg:List(s:String[])">[group.ToList]</InArgument>
                                      </InvokeMethod>
                                    </If.Then>
                                  </If>
                                </If.Then>
                                <If.Else>
                                  <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_6" MethodName="Add">
                                    <InvokeMethod.TargetObject>
                                      <InArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(s:String[]))">[transDateDictionary]</InArgument>
                                    </InvokeMethod.TargetObject>
                                    <InArgument x:TypeArguments="x:String">[group.Key]</InArgument>
                                    <InArgument x:TypeArguments="scg:List(s:String[])">[group.ToList]</InArgument>
                                  </InvokeMethod>
                                </If.Else>
                              </If>
                            </Sequence>
                          </ActivityAction>
                        </ForEach>
                      </If.Then>
                      <If.Else>
                        <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_8" MethodName="Add">
                          <InvokeMethod.TargetObject>
                            <InArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(s:String[]))">[transDateDictionary]</InArgument>
                          </InvokeMethod.TargetObject>
                          <InArgument x:TypeArguments="x:String">[transDateGroups(0).key]</InArgument>
                          <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</InArgument>
                        </InvokeMethod>
                      </If.Else>
                    </If>
                  </iad:CommentOut.Activities>
                </iad:CommentOut>
                <If Condition="[transDateDictionary.count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_252">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_293">
                      <Sequence.Variables>
                        <Variable x:TypeArguments="x:Int32" Name="AddLineOcrWorkflowStatus" />
                        <Variable x:TypeArguments="x:Boolean" Name="chargeExists" />
                        <Variable x:TypeArguments="x:Decimal" Name="tolPercent" />
                      </Sequence.Variables>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_585">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Boolean">[optimizerSuccess]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ForEach x:TypeArguments="scg:KeyValuePair(x:String, scg:List(s:String[]))" DisplayName="ForEach&lt;KeyValuePair&lt;String,List&lt;String[]&gt;&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_37" Values="[transDateDictionary]">
                        <ActivityAction x:TypeArguments="scg:KeyValuePair(x:String, scg:List(s:String[]))">
                          <ActivityAction.Argument>
                            <DelegateInArgument x:TypeArguments="scg:KeyValuePair(x:String, scg:List(s:String[]))" Name="group" />
                          </ActivityAction.Argument>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_294">
                            <Sequence.Variables>
                              <Variable x:TypeArguments="x:Int32" Name="AddHeadOcrWorkflowStatus" />
                              <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="AddHeadOcrWorkflowOutput" />
                              <Variable x:TypeArguments="x:String" Name="inbnValue" />
                              <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="AddLineOcrWorkflowOutput" />
                            </Sequence.Variables>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_584">
                              <Assign.To>
                                <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="scg:List(s:String[])">[group.value]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <If Condition="[not optimizerSuccess]" sap2010:WorkflowViewState.IdRef="If_253">
                              <If.Then>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_295">
                                  <Sequence.Variables>
                                    <Variable x:TypeArguments="x:String" Name="LessCharge" />
                                    <Variable x:TypeArguments="x:String" Name="FinAmt" />
                                  </Sequence.Variables>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_586">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Int32">[x]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Int32">1</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_587">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Decimal">[calcTotal]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Decimal">[0]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_48" Values="[M3TotalTableRows]">
                                    <ActivityAction x:TypeArguments="s:String[]">
                                      <ActivityAction.Argument>
                                        <DelegateInArgument x:TypeArguments="s:String[]" Name="item" />
                                      </ActivityAction.Argument>
                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_426">
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_842">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="s:String[]">[m3Values]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="s:String[]">[item]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_843">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:Decimal">[calcTotal]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:Decimal">[calcTotal+ (Convert.toDecimal(m3Values(2))*Convert.toDecimal(m3Values(3)))]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                      </Sequence>
                                    </ActivityAction>
                                  </ForEach>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_849">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Decimal">[tolPercent]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Decimal">[Convert.toDecimal(calcTotal) * Convert.toDecimal(tolPercentage) *0.01D]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <If Condition="[tolPercent &gt; Convert.toDecimal(tolAmt)]" sap2010:WorkflowViewState.IdRef="If_362">
                                    <If.Then>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_850">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:Decimal">[tolPercent]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:Decimal">[Convert.toDecimal(tolAmt)]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                    </If.Then>
                                  </If>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_852">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[LessCharge]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">0</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <If Condition="[ocrValues(6) &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_363">
                                    <If.Then>
                                      <If Condition="[CInt(ocrValues(6)) &lt;&gt; 0 AND Convert.ToDecimal(ocrValues(6)) &lt; tolPercent]" sap2010:WorkflowViewState.IdRef="If_364">
                                        <If.Then>
                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_427">
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_853">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[LessCharge]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">[ocrValues(6)]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_851">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:Boolean">[chargeExists]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                          </Sequence>
                                        </If.Then>
                                      </If>
                                    </If.Then>
                                  </If>
                                  <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_38" Values="[M3TotalTableRows]">
                                    <ActivityAction x:TypeArguments="s:String[]">
                                      <ActivityAction.Argument>
                                        <DelegateInArgument x:TypeArguments="s:String[]" Name="item" />
                                      </ActivityAction.Argument>
                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_298">
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_854">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[FinAmt]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">[(Convert.toDecimal(ocrValues(2)) - Convert.toDecimal(LessCharge)).ToString]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_588">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="s:String[]">[m3Values]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="s:String[]">[item]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <If Condition="[x=1]" sap2010:WorkflowViewState.IdRef="If_254">
                                          <If.Then>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_296">
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_590">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[APIString]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">["[["+m3Values(0)+","+m3Values(1)+","+m3Values(2)+","+m3Values(3)+","+FinAmt+","+ tolPercentage +"," + tolAmt +"]"]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_591">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[httpoutString]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">["[{"+ "line_number :" + m3Values(0)+","+ "rcd_number :" +m3Values(1)+"}"]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                            </Sequence>
                                          </If.Then>
                                          <If.Else>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_297">
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_592">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[APIString]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[APIString+",["+m3Values(0)+","+m3Values(1)+","+m3Values(2)+","+m3Values(3)+","+FinAmt+","+ tolPercentage +"," + tolAmt +"]"]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_593">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[httpoutString]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[httpoutstring+",{"+"line_number :" + m3Values(0)+","+ "rcd_number :" +m3Values(1)+"}"]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                            </Sequence>
                                          </If.Else>
                                        </If>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_594">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:Int32">[x]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:Int32">[x+1]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                      </Sequence>
                                    </ActivityAction>
                                  </ForEach>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_595">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Boolean">[Colemancall]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_836">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Boolean">[chargeExists]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_301">
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_596">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[httpoutString]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">[httpoutstring+"]"]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_597">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="njl:JToken">[httpoutdup]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(httpoutstring)]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <If Condition="[calcTotal &gt; Convert.toDecimal(ocrValues(2)) - Convert.toDecimal(LessCharge) - tolPercent and  calcTotal &lt; Convert.toDecimal(ocrValues(2)) - Convert.toDecimal(LessCharge)  + tolPercent]" sap2010:WorkflowViewState.IdRef="If_256">
                                      <If.Then>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_299">
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_600">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">["Compared totals with given tolerance"]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_601">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">["Success"]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_602">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:Boolean">[Colemancall]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_603">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:Int32">[Colemanrespcode]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:Int32">200</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_604">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[diffAmt]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[(Convert.ToDecimal(ocrValues(2)) - Convert.toDecimal(LessCharge) -Convert.ToDecimal(calcTotal)).ToString]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_605">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[colAmount]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[(Convert.toDecimal(ocrValues(2)) - Convert.toDecimal(LessCharge)).Tostring]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                        </Sequence>
                                      </If.Then>
                                      <If.Else>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_300">
                                          <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;ocrValues&quot;,ocrValues},{&quot;colemanAPI&quot;,colemanAPI},{&quot;APIString&quot;,APIString},{&quot;logfile&quot;,logfile}}]" ContinueOnError="True" DisplayName="Coleman Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_52" OutputArguments="[ColemanOutPut]" ResponseCode="[ColemanRespCode]" WorkflowFile="[projectPath+&quot;\CallColeman.xaml&quot;]" />
                                        </Sequence>
                                      </If.Else>
                                    </If>
                                  </Sequence>
                                  <If Condition="[ColemanRespCode = 200]" sap2010:WorkflowViewState.IdRef="If_282">
                                    <If.Then>
                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_328">
                                        <Sequence.Variables>
                                          <Variable x:TypeArguments="njl:JToken" Name="httpOut" />
                                        </Sequence.Variables>
                                        <If Condition="[colemanCall]" sap2010:WorkflowViewState.IdRef="If_257">
                                          <If.Then>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_302">
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_607">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[ColemanOutPut("comments").ToString()]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_608">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[ColemanOutPut("Status").ToString()]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                            </Sequence>
                                          </If.Then>
                                        </If>
                                        <If Condition="[Status = &quot;Success&quot;]" sap2010:WorkflowViewState.IdRef="If_265">
                                          <If.Then>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_311">
                                              <Sequence.Variables>
                                                <Variable x:TypeArguments="x:String" Name="colamt" />
                                              </Sequence.Variables>
                                              <If Condition="[colemanCall]" sap2010:WorkflowViewState.IdRef="If_258">
                                                <If.Then>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_609">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="njl:JToken">[httpOut]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(ColemanOutPut("httpOut").ToString)]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                </If.Then>
                                                <If.Else>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_610">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="njl:JToken">[httpOut]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(httpoutstring)]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                </If.Else>
                                              </If>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_611">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[colAmount]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[ocrValues(2)]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <If Condition="[(OCRvalues(6) &lt;&gt; &quot;&quot; OR OCRValues(5) &lt;&gt; &quot;&quot;) AND httpOut.ToString.Contains(&quot;Not Allocated&quot;)]" sap2010:WorkflowViewState.IdRef="If_264">
                                                <If.Then>
                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_309">
                                                    <If Condition="[CInt(ocrValues(6)) &lt;&gt; 0 OR CInt(ocrValues(5)) &lt;&gt; 0]" sap2010:WorkflowViewState.IdRef="If_263">
                                                      <If.Then>
                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_308">
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_612">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:String">[colamt]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:String">[(Convert.toDecimal(ocrValues(2)) - Convert.toDecimal(ocrValues(6)) - Convert.toDecimal(ocrValues(5))).ToString()]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_613">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:Int32">[x]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:Int32">1</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_39" Values="[M3TotalTableRows]">
                                                            <ActivityAction x:TypeArguments="s:String[]">
                                                              <ActivityAction.Argument>
                                                                <DelegateInArgument x:TypeArguments="s:String[]" Name="item" />
                                                              </ActivityAction.Argument>
                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_305">
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_614">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="s:String[]">[m3Values]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="s:String[]">[item]</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                                <If Condition="[x=1]" sap2010:WorkflowViewState.IdRef="If_259">
                                                                  <If.Then>
                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_303">
                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_615">
                                                                        <Assign.To>
                                                                          <OutArgument x:TypeArguments="x:String">[APIString]</OutArgument>
                                                                        </Assign.To>
                                                                        <Assign.Value>
                                                                          <InArgument x:TypeArguments="x:String">["[["+m3Values(0)+","+m3Values(1)+","+m3Values(2)+","+m3Values(3)+","+colamt+","+ tolPercentage +"," + tolAmt +"]"]</InArgument>
                                                                        </Assign.Value>
                                                                      </Assign>
                                                                    </Sequence>
                                                                  </If.Then>
                                                                  <If.Else>
                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_304">
                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_616">
                                                                        <Assign.To>
                                                                          <OutArgument x:TypeArguments="x:String">[APIString]</OutArgument>
                                                                        </Assign.To>
                                                                        <Assign.Value>
                                                                          <InArgument x:TypeArguments="x:String">[APIString+",["+m3Values(0)+","+m3Values(1)+","+m3Values(2)+","+m3Values(3)+","+colamt+","+ tolPercentage +"," + tolAmt +"]"]</InArgument>
                                                                        </Assign.Value>
                                                                      </Assign>
                                                                    </Sequence>
                                                                  </If.Else>
                                                                </If>
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_617">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:Int32">[x]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:Int32">[x+1]</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                              </Sequence>
                                                            </ActivityAction>
                                                          </ForEach>
                                                          <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;ocrValues&quot;,ocrValues},{&quot;colemanAPI&quot;,colemanAPI},{&quot;APIString&quot;,APIString},{&quot;logfile&quot;,logfile}}]" ContinueOnError="True" DisplayName="Coleman Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_53" OutputArguments="[ColemanOutPut]" ResponseCode="[ColemanRespCode]" WorkflowFile="[projectPath+&quot;\CallColeman.xaml&quot;]" />
                                                          <If Condition="[ColemanRespCode = 200]" sap2010:WorkflowViewState.IdRef="If_262">
                                                            <If.Then>
                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_307">
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_618">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="njl:JToken">[httpOut]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(ColemanOutPut("httpOut").ToString)]</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_619">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:String">[ColemanOutPut("comments").ToString()]</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_620">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:String">[ColemanOutPut("Status").ToString()]</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                                <If Condition="[Status = &quot;Success&quot;]" sap2010:WorkflowViewState.IdRef="If_261">
                                                                  <If.Then>
                                                                    <If Condition="[NOT httpOut.ToString.Contains(&quot;Not Allocated&quot;)]" sap2010:WorkflowViewState.IdRef="If_260">
                                                                      <If.Then>
                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_306">
                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_621">
                                                                            <Assign.To>
                                                                              <OutArgument x:TypeArguments="x:Boolean">[chargeExists]</OutArgument>
                                                                            </Assign.To>
                                                                            <Assign.Value>
                                                                              <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                                                            </Assign.Value>
                                                                          </Assign>
                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_622">
                                                                            <Assign.To>
                                                                              <OutArgument x:TypeArguments="x:String">[colAmount]</OutArgument>
                                                                            </Assign.To>
                                                                            <Assign.Value>
                                                                              <InArgument x:TypeArguments="x:String">[colAmt]</InArgument>
                                                                            </Assign.Value>
                                                                          </Assign>
                                                                        </Sequence>
                                                                      </If.Then>
                                                                    </If>
                                                                  </If.Then>
                                                                </If>
                                                              </Sequence>
                                                            </If.Then>
                                                          </If>
                                                        </Sequence>
                                                      </If.Then>
                                                    </If>
                                                  </Sequence>
                                                </If.Then>
                                                <If.Else>
                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_310" />
                                                </If.Else>
                                              </If>
                                            </Sequence>
                                          </If.Then>
                                        </If>
                                        <If Condition="[Status = &quot;Success&quot;]" sap2010:WorkflowViewState.IdRef="If_281">
                                          <If.Then>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_327">
                                              <If Condition="[colemanCall]" sap2010:WorkflowViewState.IdRef="If_266">
                                                <If.Then>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_623">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="njl:JToken">[httpOut]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(ColemanOutPut("httpOut").ToString)]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                </If.Then>
                                                <If.Else>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_624">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="njl:JToken">[httpOut]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(httpoutstring)]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                </If.Else>
                                              </If>
                                              <If Condition="[(NOT (httpOut.ToString.Contains(&quot;Not Allocated&quot;) AND miscValues(&quot;createInvoiceIrrespectiveTolerance&quot;).ToString.ToLower = &quot;false&quot;) ) OR miscValues(&quot;Comments&quot;).ToString.ToLower.Contains(&quot;approved&quot;)]" sap2010:WorkflowViewState.IdRef="If_280">
                                                <If.Then>
                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_381">
                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_715">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="x:Boolean">[optimizerSuccess]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="x:Boolean">[True]</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                    <Assign DisplayName="Assign ivdate" sap2010:WorkflowViewState.IdRef="Assign_716">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="x:String">[ivdate]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="x:String">[DateTime.ParseExact(ocrValues(1),"dd/MM/yyyy",Nothing).ToString("yyyyMMdd")]</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                    <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_717">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="x:String">[System.DateTime.Now.ToString("yyyyMMddHHmmssfff").tostring + " -"+ ocrValues(0) + "-" + division + "-" + vendorID]</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                    <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_4">
                                                      <TryCatch.Try>
                                                        <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_718">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:String">[correlationID.Substring(0,35)]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                      </TryCatch.Try>
                                                      <TryCatch.Catches>
                                                        <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_4">
                                                          <ActivityAction x:TypeArguments="s:Exception">
                                                            <ActivityAction.Argument>
                                                              <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                            </ActivityAction.Argument>
                                                            <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_719">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="x:String">[correlationID]</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                          </ActivityAction>
                                                        </Catch>
                                                      </TryCatch.Catches>
                                                    </TryCatch>
                                                    <If Condition="[miscValues(&quot;BusinessRuleAPResp&quot;).ToString.ToUpper() &lt;&gt; &quot;NA&quot;]" sap2010:WorkflowViewState.IdRef="If_354">
                                                      <If.Then>
                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_418">
                                                          <Sequence.Variables>
                                                            <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="APRespRespDict" />
                                                            <Variable x:TypeArguments="x:Int32" Name="APRespRespCode" />
                                                          </Sequence.Variables>
                                                          <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;division&quot;,division},{&quot;company&quot;,company},{&quot;VendorID&quot;,vendorID}}]" ContinueOnError="True" DisplayName="AP resp Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_71" OutputArguments="[APRespRespDict]" ResponseCode="[APRespRespCode]" WorkflowFile="[projectPath+&quot;\APResp.xaml&quot;]" />
                                                          <If Condition="[APRespRespCode = 200]" sap2010:WorkflowViewState.IdRef="If_352">
                                                            <If.Then>
                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_825">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="x:String">[APResp]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="x:String">[CType(APRespRespDict("APResp"), String)]</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                            </If.Then>
                                                          </If>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_826">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:String">[authUser]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:String">[APResp]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                        </Sequence>
                                                      </If.Then>
                                                      <If.Else>
                                                        <If Condition="[authUser.ToUpper() = &quot;SUPPLIERAUTHUSER&quot;]" sap2010:WorkflowViewState.IdRef="If_353">
                                                          <If.Then>
                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_419">
                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_827">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="x:String">[authUser]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="x:String">[vendorResult(1)]</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                            </Sequence>
                                                          </If.Then>
                                                        </If>
                                                      </If.Else>
                                                    </If>
                                                    <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;bkid&quot;,bkid},{&quot;authUser&quot;,authUser},{&quot;cuam&quot;,ocrValues(2).Replace(&quot;,&quot;,&quot;&quot;)},{&quot;pyme&quot;,pyme},{&quot;tepy&quot;,tepy},{&quot;cucd&quot;,cucd},{&quot;sino&quot;,ocrValues(0)},{&quot;division&quot;,division},{&quot;ivdate&quot;,ivdate},{&quot;SupplierNo&quot;,VendorID},{&quot;correlationID&quot;,correlationID},{&quot;discountTerms&quot;,discountTerms},{&quot;miscValues&quot;,miscValues},{&quot;payee&quot;,payee}}]" ContinueOnError="True" DisplayName="Add Head Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_61" OutputArguments="[AddHeadOcrWorkflowOutput]" ResponseCode="[AddHeadOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\AddHead.xaml&quot;]" />
                                                    <If Condition="[AddHeadOcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_328">
                                                      <If.Then>
                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_380">
                                                          <Sequence.Variables>
                                                            <Variable x:TypeArguments="scg:List(s:String[])" Name="M3TotalTableRowsAndColeman" />
                                                          </Sequence.Variables>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_720">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:String">[inbnValue]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:String">[CType(AddHeadOcrWorkflowOutput("inbnValue"), String)]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_721">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:String">[CType(AddHeadOcrWorkflowOutput("Status"), String)]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_722">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:String">[CType(AddHeadOcrWorkflowOutput("commentStatus"), String)]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <If Condition="[inbnValue &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_327">
                                                            <If.Then>
                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_379">
                                                                <Sequence.Variables>
                                                                  <Variable x:TypeArguments="x:Decimal" Name="lineAmt" />
                                                                  <Variable x:TypeArguments="x:Decimal" Name="qty" />
                                                                  <Variable x:TypeArguments="x:String" Name="includeDistribution" />
                                                                  <Variable x:TypeArguments="iru:ResponseObject" Name="resp" />
                                                                  <Variable x:TypeArguments="x:String" Name="vat" />
                                                                  <Variable x:TypeArguments="x:Boolean" Name="chFound" />
                                                                  <Variable x:TypeArguments="x:String" Name="supa" />
                                                                  <Variable x:TypeArguments="x:String" Name="chargeDiff" />
                                                                  <Variable x:TypeArguments="x:Decimal" Name="tot" />
                                                                </Sequence.Variables>
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_723">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRowsAndColeman]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="scg:List(s:String[])">[new list(OF string())]</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_724">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:Decimal">[lineAmt]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:Decimal">[0]</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_725">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:Decimal">[qty]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:Decimal">[0]</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                                <If Condition="[httpOut.ToString.Contains(&quot;Not Allocated&quot;)]" sap2010:WorkflowViewState.IdRef="If_309">
                                                                  <If.Then>
                                                                    <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_44" Values="[M3TotalTableRows]">
                                                                      <ActivityAction x:TypeArguments="s:String[]">
                                                                        <ActivityAction.Argument>
                                                                          <DelegateInArgument x:TypeArguments="s:String[]" Name="rows" />
                                                                        </ActivityAction.Argument>
                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_359">
                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_726">
                                                                            <Assign.To>
                                                                              <OutArgument x:TypeArguments="x:Decimal">[lineAmt]</OutArgument>
                                                                            </Assign.To>
                                                                            <Assign.Value>
                                                                              <InArgument x:TypeArguments="x:Decimal">[lineAmt +convert.ToDecimal(rows(2))*convert.ToDecimal(rows(3))]</InArgument>
                                                                            </Assign.Value>
                                                                          </Assign>
                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_727">
                                                                            <Assign.To>
                                                                              <OutArgument x:TypeArguments="x:Decimal">[qty]</OutArgument>
                                                                            </Assign.To>
                                                                            <Assign.Value>
                                                                              <InArgument x:TypeArguments="x:Decimal">[qty + convert.ToDecimal(rows(3))]</InArgument>
                                                                            </Assign.Value>
                                                                          </Assign>
                                                                          <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_13" MethodName="Add">
                                                                            <InvokeMethod.TargetObject>
                                                                              <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRowsAndColeman]</InArgument>
                                                                            </InvokeMethod.TargetObject>
                                                                            <InArgument x:TypeArguments="s:String[]">[rows]</InArgument>
                                                                          </InvokeMethod>
                                                                        </Sequence>
                                                                      </ActivityAction>
                                                                    </ForEach>
                                                                  </If.Then>
                                                                  <If.Else>
                                                                    <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_46" Values="[httpOut]">
                                                                      <ActivityAction x:TypeArguments="njl:JToken">
                                                                        <ActivityAction.Argument>
                                                                          <DelegateInArgument x:TypeArguments="njl:JToken" Name="out" />
                                                                        </ActivityAction.Argument>
                                                                        <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_45" Values="[M3TotalTableRows]">
                                                                          <ActivityAction x:TypeArguments="s:String[]">
                                                                            <ActivityAction.Argument>
                                                                              <DelegateInArgument x:TypeArguments="s:String[]" Name="rows" />
                                                                            </ActivityAction.Argument>
                                                                            <If Condition="[out(&quot;line_number&quot;).ToString=rows(0) And out(&quot;rcd_number&quot;).ToString=rows(1)]" sap2010:WorkflowViewState.IdRef="If_308">
                                                                              <If.Then>
                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_360">
                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_728">
                                                                                    <Assign.To>
                                                                                      <OutArgument x:TypeArguments="x:Decimal">[lineAmt]</OutArgument>
                                                                                    </Assign.To>
                                                                                    <Assign.Value>
                                                                                      <InArgument x:TypeArguments="x:Decimal">[lineAmt +convert.ToDecimal(rows(2))*convert.ToDecimal(rows(3))]</InArgument>
                                                                                    </Assign.Value>
                                                                                  </Assign>
                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_729">
                                                                                    <Assign.To>
                                                                                      <OutArgument x:TypeArguments="x:Decimal">[qty]</OutArgument>
                                                                                    </Assign.To>
                                                                                    <Assign.Value>
                                                                                      <InArgument x:TypeArguments="x:Decimal">[qty + convert.ToDecimal(rows(3))]</InArgument>
                                                                                    </Assign.Value>
                                                                                  </Assign>
                                                                                  <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_14" MethodName="Add">
                                                                                    <InvokeMethod.TargetObject>
                                                                                      <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRowsAndColeman]</InArgument>
                                                                                    </InvokeMethod.TargetObject>
                                                                                    <InArgument x:TypeArguments="s:String[]">[rows]</InArgument>
                                                                                  </InvokeMethod>
                                                                                </Sequence>
                                                                              </If.Then>
                                                                            </If>
                                                                          </ActivityAction>
                                                                        </ForEach>
                                                                      </ActivityAction>
                                                                    </ForEach>
                                                                  </If.Else>
                                                                </If>
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_730">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:String">[diffamt]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:String">[((convert.ToDecimal(colAmount) - lineAmt)/qty).ToString]</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                                <If Condition="[chargeExists]" sap2010:WorkflowViewState.IdRef="If_313">
                                                                  <If.Then>
                                                                    <Sequence DisplayName="Amount is matched with subtotal Sequence" sap2010:WorkflowViewState.IdRef="Sequence_362">
                                                                      <If Condition="[ocrValues(6) &lt;&gt; &quot;0&quot;]" sap2010:WorkflowViewState.IdRef="If_310">
                                                                        <If.Then>
                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_361">
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_731">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="x:String">[diffamt]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="x:String">[((convert.ToDecimal(ocrValues(2)) - convert.ToDecimal(Ocrvalues(6)) - lineAmt)/qty).ToString]</InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                          </Sequence>
                                                                        </If.Then>
                                                                      </If>
                                                                    </Sequence>
                                                                  </If.Then>
                                                                  <If.Else>
                                                                    <Sequence DisplayName="Total Amount is within Tol" sap2010:WorkflowViewState.IdRef="Sequence_364">
                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_732">
                                                                        <Assign.To>
                                                                          <OutArgument x:TypeArguments="x:String">[diffamt]</OutArgument>
                                                                        </Assign.To>
                                                                        <Assign.Value>
                                                                          <InArgument x:TypeArguments="x:String">[((convert.ToDecimal(colAmount) - lineAmt)/qty).ToString]</InArgument>
                                                                        </Assign.Value>
                                                                      </Assign>
                                                                      <If Condition="[CInt(ocrValues(6)) &lt;&gt; 0]" DisplayName="Matched with total but there is charge" sap2010:WorkflowViewState.IdRef="If_312">
                                                                        <If.Then>
                                                                          <If Condition="[Convert.ToDecimal(ocrValues(6)) - convert.ToDecimal(diffAmt) * Convert.ToDecimal(qty) &lt; 1]" DisplayName="If diff amount = charge in invoice" sap2010:WorkflowViewState.IdRef="If_311">
                                                                            <If.Then>
                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_363">
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_733">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[diffamt]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">0</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_734">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:Boolean">[ChargeExists]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                              </Sequence>
                                                                            </If.Then>
                                                                          </If>
                                                                        </If.Then>
                                                                      </If>
                                                                    </Sequence>
                                                                  </If.Else>
                                                                </If>
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_735">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:String">[includeDistribution]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:String">[miscValues("includeDistribution").ToString]</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                                <If Condition="[httpOut.ToString.Contains(&quot;Not Allocated&quot;)]" sap2010:WorkflowViewState.IdRef="If_315">
                                                                  <If.Then>
                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_365">
                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_736">
                                                                        <Assign.To>
                                                                          <OutArgument x:TypeArguments="x:String">[diffamt]</OutArgument>
                                                                        </Assign.To>
                                                                        <Assign.Value>
                                                                          <InArgument x:TypeArguments="x:String">[((Math.Round(convert.ToDecimal(ocrValues(2)),2) - Math.Round(convert.ToDecimal(Ocrvalues(6)),2) - Math.Round(convert.ToDecimal(Ocrvalues(5)),2) - Math.Round(lineAmt,2))/Math.Round(qty,2)).ToString]</InArgument>
                                                                        </Assign.Value>
                                                                      </Assign>
                                                                      <If Condition="[CInt(ocrValues(6)) &lt;&gt; 0]" sap2010:WorkflowViewState.IdRef="If_314">
                                                                        <If.Then>
                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_737">
                                                                            <Assign.To>
                                                                              <OutArgument x:TypeArguments="x:Boolean">[ChargeExists]</OutArgument>
                                                                            </Assign.To>
                                                                            <Assign.Value>
                                                                              <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                                                            </Assign.Value>
                                                                          </Assign>
                                                                        </If.Then>
                                                                      </If>
                                                                    </Sequence>
                                                                  </If.Then>
                                                                </If>
                                                                <If Condition="[diffamt &lt;&gt; &quot;0&quot; AND includeDistribution.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_316">
                                                                  <If.Then>
                                                                    <ForEach x:TypeArguments="x:Int32" DisplayName="ForEach&lt;Int32&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_47" Values="[Enumerable.Range(0,M3TotalTableRowsAndColeman.count)]">
                                                                      <ActivityAction x:TypeArguments="x:Int32">
                                                                        <ActivityAction.Argument>
                                                                          <DelegateInArgument x:TypeArguments="x:Int32" Name="i" />
                                                                        </ActivityAction.Argument>
                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_366">
                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_738">
                                                                            <Assign.To>
                                                                              <OutArgument x:TypeArguments="x:String">[M3TotalTableRowsAndColeman(i)(2)]</OutArgument>
                                                                            </Assign.To>
                                                                            <Assign.Value>
                                                                              <InArgument x:TypeArguments="x:String">[(convert.ToDecimal(M3TotalTableRowsAndColeman(i)(2)) + convert.Todecimal(diffamt)).ToString]</InArgument>
                                                                            </Assign.Value>
                                                                          </Assign>
                                                                        </Sequence>
                                                                      </ActivityAction>
                                                                    </ForEach>
                                                                  </If.Then>
                                                                </If>
                                                                <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;M3TotalTableRows&quot;,M3TotalTableRowsAndColeman},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;division&quot;,division},{&quot;inbnValue&quot;,inbnValue}}]" ContinueOnError="True" DisplayName="Add Lines Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_62" OutputArguments="[AddLineOcrWorkflowOutput]" ResponseCode="[AddLineOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\AddLine.xaml&quot;]" />
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_784">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:String">[chargeDiff]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:String">0</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                                <If Condition="[(chargeExists AND CInt(ocrValues(6)) &lt;&gt; 0)]" sap2010:WorkflowViewState.IdRef="If_317">
                                                                  <If.Then>
                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_396">
                                                                      <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;M3TotalTableRows&quot;,M3TotalTableRowsAndColeman},{&quot;division&quot;,division},{&quot;inbnValue&quot;,inbnValue},{&quot;charge&quot;,ocrvalues(6)},{&quot;chargeCode&quot;,chargeCode},{&quot;emptyCharge&quot;,False}}]" ContinueOnError="True" DisplayName="Add Charges Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_63" OutputArguments="[AddLineOcrWorkflowOutput]" ResponseCode="[AddLineOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\AddCharge.xaml&quot;]" />
                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_783">
                                                                        <Assign.To>
                                                                          <OutArgument x:TypeArguments="x:String">[chargeDiff]</OutArgument>
                                                                        </Assign.To>
                                                                        <Assign.Value>
                                                                          <InArgument x:TypeArguments="x:String">[Ctype(AddLineOcrWorkflowOutput("chargeDiff"),String)]</InArgument>
                                                                        </Assign.Value>
                                                                      </Assign>
                                                                    </Sequence>
                                                                  </If.Then>
                                                                </If>
                                                                <If Condition="[chargeExists AND CInt(ocrValues(5)) &lt;&gt; 0]" sap2010:WorkflowViewState.IdRef="If_318">
                                                                  <If.Then>
                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_367">
                                                                      <Sequence.Variables>
                                                                        <Variable x:TypeArguments="x:String" Name="vatCode" />
                                                                      </Sequence.Variables>
                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_739">
                                                                        <Assign.To>
                                                                          <OutArgument x:TypeArguments="x:String">[vat]</OutArgument>
                                                                        </Assign.To>
                                                                        <Assign.Value>
                                                                          <InArgument x:TypeArguments="x:String">[ocrValues(5)]</InArgument>
                                                                        </Assign.Value>
                                                                      </Assign>
                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_782">
                                                                        <Assign.To>
                                                                          <OutArgument x:TypeArguments="x:String">[vatCode]</OutArgument>
                                                                        </Assign.To>
                                                                        <Assign.Value>
                                                                          <InArgument x:TypeArguments="x:String">[miscValues("vatCodeConfig").ToString]</InArgument>
                                                                        </Assign.Value>
                                                                      </Assign>
                                                                      <If Condition="[miscValues(&quot;vatCodeConfig&quot;).ToString = &quot;NA&quot;]" sap2010:WorkflowViewState.IdRef="If_334">
                                                                        <If.Then>
                                                                          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_18" Response="[resp]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                                            <iai:IONAPIRequestWizard.Headers>
                                                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                  <x:String>Accept</x:String>
                                                                                </scg:List>
                                                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                  <x:String>application/json</x:String>
                                                                                </scg:List>
                                                                              </scg:List>
                                                                            </iai:IONAPIRequestWizard.Headers>
                                                                            <iai:IONAPIRequestWizard.QueryParameters>
                                                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                  <x:String>INBN</x:String>
                                                                                  <x:String>RDTP</x:String>
                                                                                  <x:String>DIVI</x:String>
                                                                                  <x:String>GLAM</x:String>
                                                                                </scg:List>
                                                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                  <x:String>inbnValue</x:String>
                                                                                  <x:String>3</x:String>
                                                                                  <x:String>division</x:String>
                                                                                  <x:String>vat</x:String>
                                                                                </scg:List>
                                                                              </scg:List>
                                                                            </iai:IONAPIRequestWizard.QueryParameters>
                                                                          </iai:IONAPIRequestWizard>
                                                                        </If.Then>
                                                                        <If.Else>
                                                                          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_19" Response="[resp]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                                            <iai:IONAPIRequestWizard.Headers>
                                                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                  <x:String>Accept</x:String>
                                                                                </scg:List>
                                                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                  <x:String>application/json</x:String>
                                                                                </scg:List>
                                                                              </scg:List>
                                                                            </iai:IONAPIRequestWizard.Headers>
                                                                            <iai:IONAPIRequestWizard.QueryParameters>
                                                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                  <x:String>INBN</x:String>
                                                                                  <x:String>RDTP</x:String>
                                                                                  <x:String>DIVI</x:String>
                                                                                  <x:String>VTA1</x:String>
                                                                                  <x:String>VTCD</x:String>
                                                                                </scg:List>
                                                                                <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                  <x:String>inbnValue</x:String>
                                                                                  <x:String>3</x:String>
                                                                                  <x:String>division</x:String>
                                                                                  <x:String>vat</x:String>
                                                                                  <x:String>vatCode</x:String>
                                                                                </scg:List>
                                                                              </scg:List>
                                                                            </iai:IONAPIRequestWizard.QueryParameters>
                                                                          </iai:IONAPIRequestWizard>
                                                                        </If.Else>
                                                                      </If>
                                                                    </Sequence>
                                                                  </If.Then>
                                                                </If>
                                                                <If Condition="[AddLineOcrWorkflowStatus= 200]" sap2010:WorkflowViewState.IdRef="If_323">
                                                                  <If.Then>
                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_394">
                                                                      <Sequence.Variables>
                                                                        <Variable x:TypeArguments="iru:ResponseObject" Name="resp1" />
                                                                        <Variable x:TypeArguments="x:String" Name="inyr" />
                                                                      </Sequence.Variables>
                                                                      <If Condition="[CInt(ocrValues(6)) = 0 AND NOT chargeExists]" sap2010:WorkflowViewState.IdRef="If_367">
                                                                        <If.Then>
                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_435">
                                                                            <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;M3TotalTableRows&quot;,M3TotalTableRowsAndColeman},{&quot;division&quot;,division},{&quot;inbnValue&quot;,inbnValue},{&quot;charge&quot;,ocrvalues(6)},{&quot;chargeCode&quot;,chargeCode},{&quot;emptyCharge&quot;,True}}]" ContinueOnError="True" DisplayName="Add Charges Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_72" OutputArguments="[AddLineOcrWorkflowOutput]" ResponseCode="[AddLineOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\AddCharge.xaml&quot;]" />
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_862">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="x:String">[chargeDiff]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="x:String">[Ctype(AddLineOcrWorkflowOutput("chargeDiff"),String)]</InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                          </Sequence>
                                                                        </If.Then>
                                                                      </If>
                                                                      <If Condition="[Math.abs(Convert.ToDecimal(diffamt)*Convert.ToDecimal(qty) + Convert.ToDecimal(chargeDiff)) &gt; 0]" sap2010:WorkflowViewState.IdRef="If_335">
                                                                        <If.Then>
                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_397">
                                                                            <Sequence.Variables>
                                                                              <Variable x:TypeArguments="x:String" Name="diffAmt1" />
                                                                            </Sequence.Variables>
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_785">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="x:String">[diffAmt1]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="x:String">[(Convert.ToDecimal(diffamt)*Convert.ToDecimal(qty) + Convert.ToDecimal(chargeDiff)).ToString]</InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for adding AddInfo" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_20" Response="[resp1]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddAddInfo&quot;]">
                                                                              <iai:IONAPIRequestWizard.Headers>
                                                                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                    <x:String>Accept</x:String>
                                                                                  </scg:List>
                                                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                    <x:String>application/json</x:String>
                                                                                  </scg:List>
                                                                                </scg:List>
                                                                              </iai:IONAPIRequestWizard.Headers>
                                                                              <iai:IONAPIRequestWizard.QueryParameters>
                                                                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                    <x:String>INBN</x:String>
                                                                                    <x:String>PEXN</x:String>
                                                                                    <x:String>PEXI</x:String>
                                                                                    <x:String>DIVI</x:String>
                                                                                  </scg:List>
                                                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                    <x:String>inbnValue</x:String>
                                                                                    <x:String>414</x:String>
                                                                                    <x:String>diffAmt1</x:String>
                                                                                    <x:String>division</x:String>
                                                                                  </scg:List>
                                                                                </scg:List>
                                                                              </iai:IONAPIRequestWizard.QueryParameters>
                                                                            </iai:IONAPIRequestWizard>
                                                                          </Sequence>
                                                                        </If.Then>
                                                                      </If>
                                                                      <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_8">
                                                                        <TryCatch.Try>
                                                                          <If Condition="[NOT String.IsNullOrEmpty(miscValues(&quot;addInfo&quot;).ToString())]" sap2010:WorkflowViewState.IdRef="If_368">
                                                                            <If.Then>
                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_436">
                                                                                <Sequence.Variables>
                                                                                  <Variable x:TypeArguments="x:String" Name="userInput" />
                                                                                </Sequence.Variables>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_864">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[userInput]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">[miscValues("addInfo").ToString()]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <If Condition="[userInput.Length &gt; 45]" sap2010:WorkflowViewState.IdRef="If_370">
                                                                                  <If.Then>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_866">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:String">[userInput]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:String">[userInput.Substring(0, 45)]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                  </If.Then>
                                                                                </If>
                                                                                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for adding AddInfo" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_22" Response="[resp1]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddAddInfo&quot;]">
                                                                                  <iai:IONAPIRequestWizard.Headers>
                                                                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                        <x:String>Accept</x:String>
                                                                                      </scg:List>
                                                                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                        <x:String>application/json</x:String>
                                                                                      </scg:List>
                                                                                    </scg:List>
                                                                                  </iai:IONAPIRequestWizard.Headers>
                                                                                  <iai:IONAPIRequestWizard.QueryParameters>
                                                                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                        <x:String>INBN</x:String>
                                                                                        <x:String>PEXN</x:String>
                                                                                        <x:String>PEXI</x:String>
                                                                                        <x:String>DIVI</x:String>
                                                                                      </scg:List>
                                                                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                        <x:String>inbnValue</x:String>
                                                                                        <x:String>413</x:String>
                                                                                        <x:String>userInput</x:String>
                                                                                        <x:String>division</x:String>
                                                                                      </scg:List>
                                                                                    </scg:List>
                                                                                  </iai:IONAPIRequestWizard.QueryParameters>
                                                                                </iai:IONAPIRequestWizard>
                                                                              </Sequence>
                                                                            </If.Then>
                                                                          </If>
                                                                        </TryCatch.Try>
                                                                        <TryCatch.Catches>
                                                                          <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_8">
                                                                            <ActivityAction x:TypeArguments="s:Exception">
                                                                              <ActivityAction.Argument>
                                                                                <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                              </ActivityAction.Argument>
                                                                            </ActivityAction>
                                                                          </Catch>
                                                                        </TryCatch.Catches>
                                                                      </TryCatch>
                                                                      <If Condition="[miscValues(&quot;AutomateValidation&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_333">
                                                                        <If.Then>
                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_374">
                                                                            <Sequence.Variables>
                                                                              <Variable x:TypeArguments="iru:ResponseObject" Name="ValidateRespCode" />
                                                                              <Variable x:TypeArguments="x:Int32" Name="loopBreak" />
                                                                            </Sequence.Variables>
                                                                            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Validate IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_9" Response="[ValidateRespCode]" StatusCode="[ValidateStatus]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS455MI/ValidByBatchNo&quot;]">
                                                                              <iai:IONAPIRequestWizard.Headers>
                                                                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                    <x:String>Accept</x:String>
                                                                                  </scg:List>
                                                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                    <x:String>application/json</x:String>
                                                                                  </scg:List>
                                                                                </scg:List>
                                                                              </iai:IONAPIRequestWizard.Headers>
                                                                              <iai:IONAPIRequestWizard.QueryParameters>
                                                                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                    <x:String>INBN</x:String>
                                                                                    <x:String>DIVI</x:String>
                                                                                  </scg:List>
                                                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                    <x:String>inbnValue</x:String>
                                                                                    <x:String>division</x:String>
                                                                                  </scg:List>
                                                                                </scg:List>
                                                                              </iai:IONAPIRequestWizard.QueryParameters>
                                                                            </iai:IONAPIRequestWizard>
                                                                            <Delay Duration="00:00:02" sap2010:WorkflowViewState.IdRef="Delay_4" />
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_741">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="x:String">Invoice is successfully created and validated</InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_789">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="x:String">[sino]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="x:String">[ocrValues(0)]</InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_790">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="x:String">[inyr]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="x:String">[ivdate.Substring(0,4)]</InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                            <If Condition="[validateStatus = 200 AND NOT httpOut.ToString.Contains(&quot;Not Allocated&quot;)]" sap2010:WorkflowViewState.IdRef="If_322">
                                                                              <If.Then>
                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_398">
                                                                                  <If Condition="[miscValues(&quot;AutomateApproval&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_337">
                                                                                    <If.Then>
                                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_399">
                                                                                        <InvokeMethod DisplayName="inbn value InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_20" MethodName="Add">
                                                                                          <InvokeMethod.TargetObject>
                                                                                            <InArgument x:TypeArguments="scg:List(x:String)">[approvalList]</InArgument>
                                                                                          </InvokeMethod.TargetObject>
                                                                                          <InArgument x:TypeArguments="x:String">[InbnValue]</InArgument>
                                                                                        </InvokeMethod>
                                                                                        <InvokeMethod DisplayName="sino InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_21" MethodName="Add">
                                                                                          <InvokeMethod.TargetObject>
                                                                                            <InArgument x:TypeArguments="scg:List(x:String)">[approvalList]</InArgument>
                                                                                          </InvokeMethod.TargetObject>
                                                                                          <InArgument x:TypeArguments="x:String">[sino]</InArgument>
                                                                                        </InvokeMethod>
                                                                                        <InvokeMethod DisplayName="vendorID InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_22" MethodName="Add">
                                                                                          <InvokeMethod.TargetObject>
                                                                                            <InArgument x:TypeArguments="scg:List(x:String)">[approvalList]</InArgument>
                                                                                          </InvokeMethod.TargetObject>
                                                                                          <InArgument x:TypeArguments="x:String">[vendorID]</InArgument>
                                                                                        </InvokeMethod>
                                                                                        <InvokeMethod DisplayName="year InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_23" MethodName="Add">
                                                                                          <InvokeMethod.TargetObject>
                                                                                            <InArgument x:TypeArguments="scg:List(x:String)">[approvalList]</InArgument>
                                                                                          </InvokeMethod.TargetObject>
                                                                                          <InArgument x:TypeArguments="x:String">[inyr]</InArgument>
                                                                                        </InvokeMethod>
                                                                                        <InvokeMethod DisplayName="divi InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_24" MethodName="Add">
                                                                                          <InvokeMethod.TargetObject>
                                                                                            <InArgument x:TypeArguments="scg:List(x:String)">[approvalList]</InArgument>
                                                                                          </InvokeMethod.TargetObject>
                                                                                          <InArgument x:TypeArguments="x:String">[division]</InArgument>
                                                                                        </InvokeMethod>
                                                                                      </Sequence>
                                                                                    </If.Then>
                                                                                  </If>
                                                                                  <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_2">
                                                                                    <iad:CommentOut.Activities>
                                                                                      <If Condition="[miscValues(&quot;AutomateApproval&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_336">
                                                                                        <If.Then>
                                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_370">
                                                                                            <Sequence.Variables>
                                                                                              <Variable x:TypeArguments="iru:ResponseObject" Name="paymentRespCode" />
                                                                                              <Variable x:TypeArguments="x:Int32" Name="paymentStatus" />
                                                                                              <Variable x:TypeArguments="x:Boolean" Name="KeepLooping" />
                                                                                            </Sequence.Variables>
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_788">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="x:Int32">[loopBreak]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="x:Int32">1</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                            <DoWhile sap2010:WorkflowViewState.IdRef="DoWhile_3">
                                                                                              <DoWhile.Variables>
                                                                                                <Variable x:TypeArguments="iru:ResponseObject" Name="GetHeadRespObj" />
                                                                                                <Variable x:TypeArguments="x:String" Name="bist" />
                                                                                              </DoWhile.Variables>
                                                                                              <DoWhile.Condition>[bist &lt;&gt; "0" AND supa = "50" AND loopBreak &lt;10]</DoWhile.Condition>
                                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_386">
                                                                                                <Sequence.Variables>
                                                                                                  <Variable x:TypeArguments="x:Int32" Name="loopBreak1" />
                                                                                                </Sequence.Variables>
                                                                                                <Delay Duration="00:00:02" sap2010:WorkflowViewState.IdRef="Delay_7" />
                                                                                                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Check the satus IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_14" Response="[GetHeadRespObj]" StatusCode="[ValidateStatus]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/GetHead&quot;]">
                                                                                                  <iai:IONAPIRequestWizard.Headers>
                                                                                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                        <x:String>Accept</x:String>
                                                                                                      </scg:List>
                                                                                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                        <x:String>application/json</x:String>
                                                                                                      </scg:List>
                                                                                                    </scg:List>
                                                                                                  </iai:IONAPIRequestWizard.Headers>
                                                                                                  <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                        <x:String>INBN</x:String>
                                                                                                        <x:String>DIVI</x:String>
                                                                                                      </scg:List>
                                                                                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                        <x:String>inbnValue</x:String>
                                                                                                        <x:String>division</x:String>
                                                                                                      </scg:List>
                                                                                                    </scg:List>
                                                                                                  </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                </iai:IONAPIRequestWizard>
                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_765">
                                                                                                  <Assign.To>
                                                                                                    <OutArgument x:TypeArguments="x:String">[supa]</OutArgument>
                                                                                                  </Assign.To>
                                                                                                  <Assign.Value>
                                                                                                    <InArgument x:TypeArguments="x:String">[GetHeadRespObj.ReadAsJson("results")(0)("records")(0)("SUPA").ToString]</InArgument>
                                                                                                  </Assign.Value>
                                                                                                </Assign>
                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_779">
                                                                                                  <Assign.To>
                                                                                                    <OutArgument x:TypeArguments="x:Int32">[loopBreak1]</OutArgument>
                                                                                                  </Assign.To>
                                                                                                  <Assign.Value>
                                                                                                    <InArgument x:TypeArguments="x:Int32">1</InArgument>
                                                                                                  </Assign.Value>
                                                                                                </Assign>
                                                                                                <While sap2010:WorkflowViewState.IdRef="While_1" Condition="[supa = &quot;10&quot; And loopbreak1 &lt; 5]">
                                                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_393">
                                                                                                    <Delay Duration="00:00:02" sap2010:WorkflowViewState.IdRef="Delay_8" />
                                                                                                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Check the satus IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_17" Response="[GetHeadRespObj]" StatusCode="[ValidateStatus]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/GetHead&quot;]">
                                                                                                      <iai:IONAPIRequestWizard.Headers>
                                                                                                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                            <x:String>Accept</x:String>
                                                                                                          </scg:List>
                                                                                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                            <x:String>application/json</x:String>
                                                                                                          </scg:List>
                                                                                                        </scg:List>
                                                                                                      </iai:IONAPIRequestWizard.Headers>
                                                                                                      <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                            <x:String>INBN</x:String>
                                                                                                            <x:String>DIVI</x:String>
                                                                                                          </scg:List>
                                                                                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                            <x:String>inbnValue</x:String>
                                                                                                            <x:String>division</x:String>
                                                                                                          </scg:List>
                                                                                                        </scg:List>
                                                                                                      </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                    </iai:IONAPIRequestWizard>
                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_777">
                                                                                                      <Assign.To>
                                                                                                        <OutArgument x:TypeArguments="x:String">[supa]</OutArgument>
                                                                                                      </Assign.To>
                                                                                                      <Assign.Value>
                                                                                                        <InArgument x:TypeArguments="x:String">[GetHeadRespObj.ReadAsJson("results")(0)("records")(0)("SUPA").ToString]</InArgument>
                                                                                                      </Assign.Value>
                                                                                                    </Assign>
                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_778">
                                                                                                      <Assign.To>
                                                                                                        <OutArgument x:TypeArguments="x:Int32">[loopBreak1]</OutArgument>
                                                                                                      </Assign.To>
                                                                                                      <Assign.Value>
                                                                                                        <InArgument x:TypeArguments="x:Int32">[loopBreak1 + 1]</InArgument>
                                                                                                      </Assign.Value>
                                                                                                    </Assign>
                                                                                                  </Sequence>
                                                                                                </While>
                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_766">
                                                                                                  <Assign.To>
                                                                                                    <OutArgument x:TypeArguments="x:String">[bist]</OutArgument>
                                                                                                  </Assign.To>
                                                                                                  <Assign.Value>
                                                                                                    <InArgument x:TypeArguments="x:String">[GetHeadRespObj.ReadAsJson("results")(0)("records")(0)("BIST").ToString]</InArgument>
                                                                                                  </Assign.Value>
                                                                                                </Assign>
                                                                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_107" Line="[&quot;Validation status iterations: &quot; + loopbreak.ToString + &quot;  bist: &quot; + bist + &quot; supa: &quot; + supa]" Source="[logfile]" />
                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_767">
                                                                                                  <Assign.To>
                                                                                                    <OutArgument x:TypeArguments="x:Int32">[loopBreak]</OutArgument>
                                                                                                  </Assign.To>
                                                                                                  <Assign.Value>
                                                                                                    <InArgument x:TypeArguments="x:Int32">[loopBreak + 1]</InArgument>
                                                                                                  </Assign.Value>
                                                                                                </Assign>
                                                                                              </Sequence>
                                                                                            </DoWhile>
                                                                                            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_10" Response="[paymentRespCode]" StatusCode="[paymentStatus]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS110MI/ApproveInvoice&quot;]">
                                                                                              <iai:IONAPIRequestWizard.Headers>
                                                                                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                    <x:String>Accept</x:String>
                                                                                                  </scg:List>
                                                                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                    <x:String>application/json</x:String>
                                                                                                  </scg:List>
                                                                                                </scg:List>
                                                                                              </iai:IONAPIRequestWizard.Headers>
                                                                                              <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                    <x:String>SUNO</x:String>
                                                                                                    <x:String>DIVI</x:String>
                                                                                                    <x:String>SINO</x:String>
                                                                                                    <x:String>INYR</x:String>
                                                                                                  </scg:List>
                                                                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                    <x:String>vendorId</x:String>
                                                                                                    <x:String>division</x:String>
                                                                                                    <x:String>sino</x:String>
                                                                                                    <x:String>inyr</x:String>
                                                                                                  </scg:List>
                                                                                                </scg:List>
                                                                                              </iai:IONAPIRequestWizard.QueryParameters>
                                                                                            </iai:IONAPIRequestWizard>
                                                                                            <If Condition="[paymentStatus = 200]" sap2010:WorkflowViewState.IdRef="If_319">
                                                                                              <If.Then>
                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_369">
                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_764">
                                                                                                    <Assign.To>
                                                                                                      <OutArgument x:TypeArguments="x:Boolean">[KeepLooping]</OutArgument>
                                                                                                    </Assign.To>
                                                                                                    <Assign.Value>
                                                                                                      <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                                                                                    </Assign.Value>
                                                                                                  </Assign>
                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_761">
                                                                                                    <Assign.To>
                                                                                                      <OutArgument x:TypeArguments="x:Int32">[loopBreak]</OutArgument>
                                                                                                    </Assign.To>
                                                                                                    <Assign.Value>
                                                                                                      <InArgument x:TypeArguments="x:Int32">0</InArgument>
                                                                                                    </Assign.Value>
                                                                                                  </Assign>
                                                                                                  <If Condition="[supa = &quot;90&quot;]" sap2010:WorkflowViewState.IdRef="If_330">
                                                                                                    <If.Then>
                                                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_389">
                                                                                                        <DoWhile sap2010:WorkflowViewState.IdRef="DoWhile_2" Condition="[KeepLooping and LoopBreak &lt; 10]">
                                                                                                          <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_5">
                                                                                                            <TryCatch.Variables>
                                                                                                              <Variable x:TypeArguments="x:String" Name="errorMsg" />
                                                                                                            </TryCatch.Variables>
                                                                                                            <TryCatch.Try>
                                                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_368">
                                                                                                                <Delay Duration="00:00:01" sap2010:WorkflowViewState.IdRef="Delay_6" />
                                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_744">
                                                                                                                  <Assign.To>
                                                                                                                    <OutArgument x:TypeArguments="x:String">[errorMsg]</OutArgument>
                                                                                                                  </Assign.To>
                                                                                                                  <Assign.Value>
                                                                                                                    <InArgument x:TypeArguments="x:String" xml:space="preserve">[paymentRespCode.ReadAsJson("results")(0)("errorMessage").ToString + "  in APS110."]</InArgument>
                                                                                                                  </Assign.Value>
                                                                                                                </Assign>
                                                                                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_99" Line="[errorMsg]" Source="[logfile]" />
                                                                                                                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_13" Response="[paymentRespCode]" StatusCode="[paymentStatus]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS110MI/ApproveInvoice&quot;]">
                                                                                                                  <iai:IONAPIRequestWizard.Headers>
                                                                                                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                        <x:String>Accept</x:String>
                                                                                                                      </scg:List>
                                                                                                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                        <x:String>application/json</x:String>
                                                                                                                      </scg:List>
                                                                                                                    </scg:List>
                                                                                                                  </iai:IONAPIRequestWizard.Headers>
                                                                                                                  <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                        <x:String>SUNO</x:String>
                                                                                                                        <x:String>DIVI</x:String>
                                                                                                                        <x:String>SINO</x:String>
                                                                                                                        <x:String>INYR</x:String>
                                                                                                                      </scg:List>
                                                                                                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                        <x:String>vendorId</x:String>
                                                                                                                        <x:String>division</x:String>
                                                                                                                        <x:String>sino</x:String>
                                                                                                                        <x:String>inyr</x:String>
                                                                                                                      </scg:List>
                                                                                                                    </scg:List>
                                                                                                                  </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                </iai:IONAPIRequestWizard>
                                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_762">
                                                                                                                  <Assign.To>
                                                                                                                    <OutArgument x:TypeArguments="x:Int32">[loopBreak]</OutArgument>
                                                                                                                  </Assign.To>
                                                                                                                  <Assign.Value>
                                                                                                                    <InArgument x:TypeArguments="x:Int32">[loopBreak + 1]</InArgument>
                                                                                                                  </Assign.Value>
                                                                                                                </Assign>
                                                                                                              </Sequence>
                                                                                                            </TryCatch.Try>
                                                                                                            <TryCatch.Catches>
                                                                                                              <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_5">
                                                                                                                <ActivityAction x:TypeArguments="s:Exception">
                                                                                                                  <ActivityAction.Argument>
                                                                                                                    <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                                                  </ActivityAction.Argument>
                                                                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_385">
                                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_763">
                                                                                                                      <Assign.To>
                                                                                                                        <OutArgument x:TypeArguments="x:Boolean">[KeepLooping]</OutArgument>
                                                                                                                      </Assign.To>
                                                                                                                      <Assign.Value>
                                                                                                                        <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                                                                                                      </Assign.Value>
                                                                                                                    </Assign>
                                                                                                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_100" Line="Approved for payment" Source="[logfile]" />
                                                                                                                  </Sequence>
                                                                                                                </ActivityAction>
                                                                                                              </Catch>
                                                                                                            </TryCatch.Catches>
                                                                                                          </TryCatch>
                                                                                                        </DoWhile>
                                                                                                        <If Condition="[LoopBreak &gt;= 10 AND KeepLooping]" sap2010:WorkflowViewState.IdRef="If_329">
                                                                                                          <If.Then>
                                                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_388">
                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_770">
                                                                                                                <Assign.To>
                                                                                                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                                </Assign.To>
                                                                                                                <Assign.Value>
                                                                                                                  <InArgument x:TypeArguments="x:String">["Invoice created and validated in the M3 but not approved for payment due to load on the system. Please approve them mannually."]</InArgument>
                                                                                                                </Assign.Value>
                                                                                                              </Assign>
                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_771">
                                                                                                                <Assign.To>
                                                                                                                  <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                                                </Assign.To>
                                                                                                                <Assign.Value>
                                                                                                                  <InArgument x:TypeArguments="x:String">SUCCESS</InArgument>
                                                                                                                </Assign.Value>
                                                                                                              </Assign>
                                                                                                            </Sequence>
                                                                                                          </If.Then>
                                                                                                        </If>
                                                                                                      </Sequence>
                                                                                                    </If.Then>
                                                                                                    <If.Else>
                                                                                                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_108" Line="[&quot;Invoice status is: &quot;+ supa]" Source="[logfile]" />
                                                                                                    </If.Else>
                                                                                                  </If>
                                                                                                </Sequence>
                                                                                              </If.Then>
                                                                                              <If.Else>
                                                                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_101" Line="Error while approving the payment" Source="[logfile]" />
                                                                                              </If.Else>
                                                                                            </If>
                                                                                          </Sequence>
                                                                                        </If.Then>
                                                                                        <If.Else>
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_786">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:String">SUCCESS</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                        </If.Else>
                                                                                      </If>
                                                                                    </iad:CommentOut.Activities>
                                                                                  </iad:CommentOut>
                                                                                </Sequence>
                                                                              </If.Then>
                                                                              <If.Else>
                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_373">
                                                                                  <If Condition="[httpOut.ToString.Contains(&quot;Not Allocated&quot;)]" sap2010:WorkflowViewState.IdRef="If_321">
                                                                                    <If.Then>
                                                                                      <If Condition="[validateStatus = 200]" sap2010:WorkflowViewState.IdRef="If_320">
                                                                                        <If.Then>
                                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_371">
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_745">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="x:String">["Invoice created and validated in the M3 but amount in invoice is not matched with M3. please verify the amounts."]</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_746">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="x:String">SUCCESS</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                          </Sequence>
                                                                                        </If.Then>
                                                                                        <If.Else>
                                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_372">
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_747">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="x:String">Invoice created in the M3 but amount in invoice is not matched with M3. please verify the amounts. Error occurred while validating the invoice.</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_748">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="x:String">SUCCESS</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                          </Sequence>
                                                                                        </If.Else>
                                                                                      </If>
                                                                                    </If.Then>
                                                                                    <If.Else>
                                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_387">
                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_768">
                                                                                          <Assign.To>
                                                                                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                          </Assign.To>
                                                                                          <Assign.Value>
                                                                                            <InArgument x:TypeArguments="x:String">Invoice Header and lines are created. Error occurred while validating the invoice.</InArgument>
                                                                                          </Assign.Value>
                                                                                        </Assign>
                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_769">
                                                                                          <Assign.To>
                                                                                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                          </Assign.To>
                                                                                          <Assign.Value>
                                                                                            <InArgument x:TypeArguments="x:String">SUCCESS</InArgument>
                                                                                          </Assign.Value>
                                                                                        </Assign>
                                                                                      </Sequence>
                                                                                    </If.Else>
                                                                                  </If>
                                                                                </Sequence>
                                                                              </If.Else>
                                                                            </If>
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_749">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="x:String">SUCCESS</InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_102" Line="[commentStatus]" Source="[logfile]" />
                                                                          </Sequence>
                                                                        </If.Then>
                                                                        <If.Else>
                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_395">
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_780">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="x:String">Invoice and Lines created</InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_781">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="x:String">SUCCESS</InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_109" Line="[commentStatus]" Source="[logfile]" />
                                                                          </Sequence>
                                                                        </If.Else>
                                                                      </If>
                                                                    </Sequence>
                                                                  </If.Then>
                                                                  <If.Else>
                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_375">
                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_750">
                                                                        <Assign.To>
                                                                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                        </Assign.To>
                                                                        <Assign.Value>
                                                                          <InArgument x:TypeArguments="x:String">Error while adding the lines</InArgument>
                                                                        </Assign.Value>
                                                                      </Assign>
                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_751">
                                                                        <Assign.To>
                                                                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                        </Assign.To>
                                                                        <Assign.Value>
                                                                          <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                        </Assign.Value>
                                                                      </Assign>
                                                                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_103" Line="[commentStatus]" Source="[logfile]" />
                                                                    </Sequence>
                                                                  </If.Else>
                                                                </If>
                                                                <If Condition="[approvalRequired]" sap2010:WorkflowViewState.IdRef="If_326">
                                                                  <If.Then>
                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_378">
                                                                      <Sequence.Variables>
                                                                        <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="ApprovalOcrWorkflowOutput" />
                                                                        <Variable x:TypeArguments="x:Int32" Name="ApprovalOcrWorkflowStatus" />
                                                                        <Variable x:TypeArguments="x:String" Name="GUID" />
                                                                      </Sequence.Variables>
                                                                      <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;VendorID&quot;,vendorId},{&quot;division&quot;,division},{&quot;businessRule&quot;,miscValues(&quot;businessRuleForGUID&quot;).ToString}}]" ContinueOnError="True" DisplayName="Approval Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_64" OutputArguments="[ApprovalOcrWorkflowOutput]" ResponseCode="[ApprovalOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\ApprovalGUID.xaml&quot;]" />
                                                                      <If Condition="[ApprovalOcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_325">
                                                                        <If.Then>
                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_377">
                                                                            <Sequence.Variables>
                                                                              <Variable x:TypeArguments="x:Int32" Name="sendApprovalResponseCode" />
                                                                              <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="sendApprovalResponseDictionary" />
                                                                            </Sequence.Variables>
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_752">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="x:String">[GUID]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="x:String">[CType(ApprovalOcrWorkflowOutput("GUID"), String)]</InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                            <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;amount&quot;,ocrValues(2)},{&quot;tenantID&quot;,tenantID},{&quot;division&quot;,DIVISION},{&quot;inbn&quot;,inbnValue},{&quot;invoiceNumber&quot;,ocrValues(0)},{&quot;poNumber&quot;,ocrValues(4)},{&quot;vendorName&quot;,vendorId},{&quot;approvalWorkflow&quot;,approvalWorkflow}}]" ContinueOnError="True" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_65" OutputArguments="[sendApprovalResponseDictionary]" ResponseCode="[sendApprovalResponseCode]" WorkflowFile="[projectPath+&quot;\SendtoApprovalWorkflow.xaml&quot;]" />
                                                                            <If Condition="[sendApprovalResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_324">
                                                                              <If.Then>
                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_376">
                                                                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_104" Line="Supllier invoice is sent for approval since the invoice amount is large" Source="[logfile]" />
                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_753">
                                                                                    <Assign.To>
                                                                                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                    </Assign.To>
                                                                                    <Assign.Value>
                                                                                      <InArgument x:TypeArguments="x:String">["Invoice created and Sent for approval"]</InArgument>
                                                                                    </Assign.Value>
                                                                                  </Assign>
                                                                                </Sequence>
                                                                              </If.Then>
                                                                            </If>
                                                                          </Sequence>
                                                                        </If.Then>
                                                                      </If>
                                                                    </Sequence>
                                                                  </If.Then>
                                                                </If>
                                                              </Sequence>
                                                            </If.Then>
                                                          </If>
                                                        </Sequence>
                                                      </If.Then>
                                                    </If>
                                                  </Sequence>
                                                </If.Then>
                                                <If.Else>
                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_326">
                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_410">
                                                      <If Condition="[miscValues(&quot;createInvoiceIrrespectiveTolerance&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_344">
                                                        <If.Then>
                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_408">
                                                            <Assign DisplayName="Assign ivdate" sap2010:WorkflowViewState.IdRef="Assign_795">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="x:String">[ivdate]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="x:String">[DateTime.ParseExact(ocrValues(1),"dd/MM/yyyy",Nothing).ToString("yyyyMMdd")]</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                            <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_796">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="x:String">[System.DateTime.Now.ToString("yyyyMMddHHmmssfff").tostring + " -"+ ocrValues(0) + "-" + division + "-" + vendorID]</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                            <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_6">
                                                              <TryCatch.Try>
                                                                <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_797">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:String">[correlationID.Substring(0,35)]</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                              </TryCatch.Try>
                                                              <TryCatch.Catches>
                                                                <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_6">
                                                                  <ActivityAction x:TypeArguments="s:Exception">
                                                                    <ActivityAction.Argument>
                                                                      <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                    </ActivityAction.Argument>
                                                                    <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_798">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">[correlationID]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                  </ActivityAction>
                                                                </Catch>
                                                              </TryCatch.Catches>
                                                            </TryCatch>
                                                            <If Condition="[authUser.ToUpper() = &quot;SUPPLIERAUTHUSER&quot;]" sap2010:WorkflowViewState.IdRef="If_348">
                                                              <If.Then>
                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_412">
                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_814">
                                                                    <Assign.To>
                                                                      <OutArgument x:TypeArguments="x:String">[authUser]</OutArgument>
                                                                    </Assign.To>
                                                                    <Assign.Value>
                                                                      <InArgument x:TypeArguments="x:String">[vendorResult(1)]</InArgument>
                                                                    </Assign.Value>
                                                                  </Assign>
                                                                </Sequence>
                                                              </If.Then>
                                                            </If>
                                                            <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;bkid&quot;,bkid},{&quot;authUser&quot;,authUser},{&quot;cuam&quot;,ocrValues(2).Replace(&quot;,&quot;,&quot;&quot;)},{&quot;pyme&quot;,pyme},{&quot;tepy&quot;,tepy},{&quot;cucd&quot;,cucd},{&quot;sino&quot;,ocrValues(0)},{&quot;division&quot;,division},{&quot;ivdate&quot;,ivdate},{&quot;SupplierNo&quot;,VendorID},{&quot;correlationID&quot;,correlationID},{&quot;discountTerms&quot;,discountTerms},{&quot;miscValues&quot;,miscValues},{&quot;payee&quot;,payee}}]" ContinueOnError="True" DisplayName="Add Head Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_66" OutputArguments="[AddHeadOcrWorkflowOutput]" ResponseCode="[AddHeadOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\AddHead.xaml&quot;]" />
                                                            <If Condition="[AddHeadOcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_343">
                                                              <If.Then>
                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_406">
                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_799">
                                                                    <Assign.To>
                                                                      <OutArgument x:TypeArguments="x:String">[inbnValue]</OutArgument>
                                                                    </Assign.To>
                                                                    <Assign.Value>
                                                                      <InArgument x:TypeArguments="x:String">[CType(AddHeadOcrWorkflowOutput("inbnValue"), String)]</InArgument>
                                                                    </Assign.Value>
                                                                  </Assign>
                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_800">
                                                                    <Assign.To>
                                                                      <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                    </Assign.To>
                                                                    <Assign.Value>
                                                                      <InArgument x:TypeArguments="x:String">[CType(AddHeadOcrWorkflowOutput("Status"), String)]</InArgument>
                                                                    </Assign.Value>
                                                                  </Assign>
                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_801">
                                                                    <Assign.To>
                                                                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                    </Assign.To>
                                                                    <Assign.Value>
                                                                      <InArgument x:TypeArguments="x:String">[CType(AddHeadOcrWorkflowOutput("commentStatus"), String)]</InArgument>
                                                                    </Assign.Value>
                                                                  </Assign>
                                                                  <If Condition="[inbnValue &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_342">
                                                                    <If.Then>
                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_405">
                                                                        <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;M3TotalTableRows&quot;,M3TotalTableRows},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;division&quot;,division},{&quot;inbnValue&quot;,inbnValue}}]" ContinueOnError="True" DisplayName="Add Lines Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_67" OutputArguments="[AddLineOcrWorkflowOutput]" ResponseCode="[AddLineOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\AddLine.xaml&quot;]" />
                                                                        <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_9">
                                                                          <TryCatch.Try>
                                                                            <If Condition="[NOT String.IsNullOrEmpty(miscValues(&quot;addInfo&quot;).ToString())]" sap2010:WorkflowViewState.IdRef="If_369">
                                                                              <If.Then>
                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_437">
                                                                                  <Sequence.Variables>
                                                                                    <Variable x:TypeArguments="x:String" Name="userInput" />
                                                                                    <Variable x:TypeArguments="iru:ResponseObject" Name="resp1" />
                                                                                  </Sequence.Variables>
                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_865">
                                                                                    <Assign.To>
                                                                                      <OutArgument x:TypeArguments="x:String">[userInput]</OutArgument>
                                                                                    </Assign.To>
                                                                                    <Assign.Value>
                                                                                      <InArgument x:TypeArguments="x:String">[miscValues("addInfo").ToString()]</InArgument>
                                                                                    </Assign.Value>
                                                                                  </Assign>
                                                                                  <If Condition="[userInput.Length &gt; 45]" sap2010:WorkflowViewState.IdRef="If_371">
                                                                                    <If.Then>
                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_867">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:String">[userInput]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:String">[userInput.Substring(0, 45)]</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                    </If.Then>
                                                                                  </If>
                                                                                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for adding AddInfo" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_23" Response="[resp1]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddAddInfo&quot;]">
                                                                                    <iai:IONAPIRequestWizard.Headers>
                                                                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                          <x:String>Accept</x:String>
                                                                                        </scg:List>
                                                                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                          <x:String>application/json</x:String>
                                                                                        </scg:List>
                                                                                      </scg:List>
                                                                                    </iai:IONAPIRequestWizard.Headers>
                                                                                    <iai:IONAPIRequestWizard.QueryParameters>
                                                                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                          <x:String>INBN</x:String>
                                                                                          <x:String>PEXN</x:String>
                                                                                          <x:String>PEXI</x:String>
                                                                                          <x:String>DIVI</x:String>
                                                                                        </scg:List>
                                                                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                          <x:String>inbnValue</x:String>
                                                                                          <x:String>413</x:String>
                                                                                          <x:String>userInput</x:String>
                                                                                          <x:String>division</x:String>
                                                                                        </scg:List>
                                                                                      </scg:List>
                                                                                    </iai:IONAPIRequestWizard.QueryParameters>
                                                                                  </iai:IONAPIRequestWizard>
                                                                                </Sequence>
                                                                              </If.Then>
                                                                            </If>
                                                                          </TryCatch.Try>
                                                                          <TryCatch.Catches>
                                                                            <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_9">
                                                                              <ActivityAction x:TypeArguments="s:Exception">
                                                                                <ActivityAction.Argument>
                                                                                  <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                </ActivityAction.Argument>
                                                                              </ActivityAction>
                                                                            </Catch>
                                                                          </TryCatch.Catches>
                                                                        </TryCatch>
                                                                        <If Condition="[AddLineOcrWorkflowStatus= 200]" sap2010:WorkflowViewState.IdRef="If_341">
                                                                          <If.Then>
                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_403">
                                                                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" Response="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_21" StatusCode="[validateStatus]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS455MI/ValidByBatchNo&quot;]">
                                                                                <iai:IONAPIRequestWizard.Headers>
                                                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                      <x:String>Accept</x:String>
                                                                                    </scg:List>
                                                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                      <x:String>application/json</x:String>
                                                                                    </scg:List>
                                                                                  </scg:List>
                                                                                </iai:IONAPIRequestWizard.Headers>
                                                                                <iai:IONAPIRequestWizard.QueryParameters>
                                                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                      <x:String>INBN</x:String>
                                                                                      <x:String>DIVI</x:String>
                                                                                    </scg:List>
                                                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                      <x:String>inbnValue</x:String>
                                                                                      <x:String>division</x:String>
                                                                                    </scg:List>
                                                                                  </scg:List>
                                                                                </iai:IONAPIRequestWizard.QueryParameters>
                                                                              </iai:IONAPIRequestWizard>
                                                                              <If Condition="[validateStatus = 200]" sap2010:WorkflowViewState.IdRef="If_340">
                                                                                <If.Then>
                                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_401">
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_802">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:String">Invoice created in the M3 but amount in invoice is not matched with M3. please verify the amounts.</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_803">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:String">SUCCESS</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                  </Sequence>
                                                                                </If.Then>
                                                                                <If.Else>
                                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_402">
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_804">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:String">["Invoice Header and lines are created. Error occurred while validating the invoice."]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_805">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                  </Sequence>
                                                                                </If.Else>
                                                                              </If>
                                                                            </Sequence>
                                                                          </If.Then>
                                                                          <If.Else>
                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_404">
                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_806">
                                                                                <Assign.To>
                                                                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                </Assign.To>
                                                                                <Assign.Value>
                                                                                  <InArgument x:TypeArguments="x:String">["Invoice header added and error occurred while adding the lines."]</InArgument>
                                                                                </Assign.Value>
                                                                              </Assign>
                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_807">
                                                                                <Assign.To>
                                                                                  <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                </Assign.To>
                                                                                <Assign.Value>
                                                                                  <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                                </Assign.Value>
                                                                              </Assign>
                                                                            </Sequence>
                                                                          </If.Else>
                                                                        </If>
                                                                      </Sequence>
                                                                    </If.Then>
                                                                  </If>
                                                                </Sequence>
                                                              </If.Then>
                                                              <If.Else>
                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_407">
                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_808">
                                                                    <Assign.To>
                                                                      <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                    </Assign.To>
                                                                    <Assign.Value>
                                                                      <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                    </Assign.Value>
                                                                  </Assign>
                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_809">
                                                                    <Assign.To>
                                                                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                    </Assign.To>
                                                                    <Assign.Value>
                                                                      <InArgument x:TypeArguments="x:String">Error occurred while adding the lines</InArgument>
                                                                    </Assign.Value>
                                                                  </Assign>
                                                                </Sequence>
                                                              </If.Else>
                                                            </If>
                                                          </Sequence>
                                                        </If.Then>
                                                        <If.Else>
                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_409">
                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_810">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="x:String">["Amount is not allocated with the amount in M3. Please check the delivery note numbers."]</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_811">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="x:String">NEEDSVERIFICATION</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                          </Sequence>
                                                        </If.Else>
                                                      </If>
                                                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_110" Line="[commentStatus]" Source="[logfile]" />
                                                    </Sequence>
                                                  </Sequence>
                                                </If.Else>
                                              </If>
                                            </Sequence>
                                          </If.Then>
                                        </If>
                                      </Sequence>
                                    </If.Then>
                                  </If>
                                </Sequence>
                              </If.Then>
                            </If>
                          </Sequence>
                        </ActivityAction>
                      </ForEach>
                    </Sequence>
                  </If.Then>
                </If>
              </Sequence>
            </If.Then>
          </If>
        </Sequence>
      </If.Then>
      <If.Else>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_222">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_418">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["Invoice Number " + ocrValues(0) + " already exists."]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_419">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
            </Assign.Value>
          </Assign>
        </Sequence>
      </If.Else>
    </If>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_791" sap:VirtualizedContainerService.HintSize="3515.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_535" sap:VirtualizedContainerService.HintSize="3515.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_536" sap:VirtualizedContainerService.HintSize="3515.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_772" sap:VirtualizedContainerService.HintSize="680,62" />
      <sap2010:ViewStateData Id="Assign_776" sap:VirtualizedContainerService.HintSize="680,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_16" sap:VirtualizedContainerService.HintSize="680,22" />
      <sap2010:ViewStateData Id="Assign_775" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_392" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_774" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_390" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_331" sap:VirtualizedContainerService.HintSize="554,340" />
      <sap2010:ViewStateData Id="If_332" sap:VirtualizedContainerService.HintSize="680,494" />
      <sap2010:ViewStateData Id="Sequence_391" sap:VirtualizedContainerService.HintSize="3515.33333333333,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_27" sap:VirtualizedContainerService.HintSize="3515.33333333333,22" />
      <sap2010:ViewStateData Id="Assign_388" sap:VirtualizedContainerService.HintSize="3515.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_437" sap:VirtualizedContainerService.HintSize="3267.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_300" sap:VirtualizedContainerService.HintSize="3267.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_787" sap:VirtualizedContainerService.HintSize="3267.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_370" sap:VirtualizedContainerService.HintSize="3267.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_371" sap:VirtualizedContainerService.HintSize="3267.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_373" sap:VirtualizedContainerService.HintSize="3267.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_372" sap:VirtualizedContainerService.HintSize="3267.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_111" sap:VirtualizedContainerService.HintSize="3267.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_292" sap:VirtualizedContainerService.HintSize="3267.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_294" sap:VirtualizedContainerService.HintSize="3267.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_420" sap:VirtualizedContainerService.HintSize="3267.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_426" sap:VirtualizedContainerService.HintSize="3267.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_556" sap:VirtualizedContainerService.HintSize="3267.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_454" sap:VirtualizedContainerService.HintSize="3267.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_397" sap:VirtualizedContainerService.HintSize="1918,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_30" sap:VirtualizedContainerService.HintSize="1918,22" />
      <sap2010:ViewStateData Id="Assign_398" sap:VirtualizedContainerService.HintSize="1670,62" />
      <sap2010:ViewStateData Id="Assign_456" sap:VirtualizedContainerService.HintSize="1670,62" />
      <sap2010:ViewStateData Id="Append_Line_76" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="InvokeMethod_4" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="Sequence_235" sap:VirtualizedContainerService.HintSize="240,252">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_23" sap:VirtualizedContainerService.HintSize="287,400" />
      <sap2010:ViewStateData Id="Sequence_238" sap:VirtualizedContainerService.HintSize="309,524">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_200" sap:VirtualizedContainerService.HintSize="464,672" />
      <sap2010:ViewStateData Id="Sequence_212" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_453" sap:VirtualizedContainerService.HintSize="1422,62" />
      <sap2010:ViewStateData Id="Assign_399" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_459" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_83" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_423" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_239" sap:VirtualizedContainerService.HintSize="264,452">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_47" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_518" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_522" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_555" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_568" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_521" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_420" sap:VirtualizedContainerService.HintSize="264,656">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_828" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_829" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_830" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_831" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_832" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_421" sap:VirtualizedContainerService.HintSize="264,594">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_355" sap:VirtualizedContainerService.HintSize="554,810">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_268" sap:VirtualizedContainerService.HintSize="576,934">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_817" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_70" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_818" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_819" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_820" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_821" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_111" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_414" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_823" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_824" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_112" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_415" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_349" sap:VirtualizedContainerService.HintSize="553,494" />
      <sap2010:ViewStateData Id="If_350" sap:VirtualizedContainerService.HintSize="242,52.6666666666667" />
      <sap2010:ViewStateData Id="Sequence_416" sap:VirtualizedContainerService.HintSize="264,380.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_351" sap:VirtualizedContainerService.HintSize="464,534.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_417" sap:VirtualizedContainerService.HintSize="486,822.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_529" sap:VirtualizedContainerService.HintSize="486,62" />
      <sap2010:ViewStateData Id="Sequence_269" sap:VirtualizedContainerService.HintSize="508,1048.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_226" sap:VirtualizedContainerService.HintSize="1110,1202.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_270" sap:VirtualizedContainerService.HintSize="1132,1326.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_201" sap:VirtualizedContainerService.HintSize="1422,1480.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_211" sap:VirtualizedContainerService.HintSize="1444,1706.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_181" sap:VirtualizedContainerService.HintSize="1670,1860.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_168" sap:VirtualizedContainerService.HintSize="1692,2188.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_296" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_297" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_424" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_169" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_146" sap:VirtualizedContainerService.HintSize="1918,2342.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_170" sap:VirtualizedContainerService.HintSize="1940,2630.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_204" sap:VirtualizedContainerService.HintSize="1962,2754.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_20" sap:VirtualizedContainerService.HintSize="3267.33333333333,2907.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_480" sap:VirtualizedContainerService.HintSize="3119.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_523" sap:VirtualizedContainerService.HintSize="612,62" />
      <sap2010:ViewStateData Id="Assign_524" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_271" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_31" sap:VirtualizedContainerService.HintSize="612,52.6666666666667" />
      <sap2010:ViewStateData Id="Assign_525" sap:VirtualizedContainerService.HintSize="612,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_48" sap:VirtualizedContainerService.HintSize="612,22" />
      <sap2010:ViewStateData Id="Assign_526" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_527" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_272" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_228" sap:VirtualizedContainerService.HintSize="464,340" />
      <sap2010:ViewStateData Id="Sequence_273" sap:VirtualizedContainerService.HintSize="486,566">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_229" sap:VirtualizedContainerService.HintSize="612,720" />
      <sap2010:ViewStateData Id="Sequence_274" sap:VirtualizedContainerService.HintSize="634,1202.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_528" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_230" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="Sequence_275" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_231" sap:VirtualizedContainerService.HintSize="3119.33333333333,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_486" sap:VirtualizedContainerService.HintSize="3119.33333333333,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_49" sap:VirtualizedContainerService.HintSize="554,22" />
      <sap2010:ViewStateData Id="Assign_560" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_561" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_284" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_562" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_563" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_88" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_285" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_244" sap:VirtualizedContainerService.HintSize="554,504" />
      <sap2010:ViewStateData Id="Sequence_286" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_564" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_565" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_287" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_245" sap:VirtualizedContainerService.HintSize="3119.33333333333,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_858" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_859" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_429" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_366" sap:VirtualizedContainerService.HintSize="464,442" />
      <sap2010:ViewStateData Id="Assign_860" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_861" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_430" sap:VirtualizedContainerService.HintSize="264,278">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Catch`1_7" sap:VirtualizedContainerService.HintSize="468,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_7" sap:VirtualizedContainerService.HintSize="3119.33333333333,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_569" sap:VirtualizedContainerService.HintSize="3119.33333333333,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_50" sap:VirtualizedContainerService.HintSize="3119.33333333333,22" />
      <sap2010:ViewStateData Id="Assign_570" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_571" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_572" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_573" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_574" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_575" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_576" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_248" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Sequence_289" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_249" sap:VirtualizedContainerService.HintSize="3119.33333333333,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_577" sap:VirtualizedContainerService.HintSize="3119.33333333333,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_51" sap:VirtualizedContainerService.HintSize="3119.33333333333,22" />
      <sap2010:ViewStateData Id="Assign_578" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_579" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_290" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_580" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_581" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_583" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_863" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_868" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_292" sap:VirtualizedContainerService.HintSize="264,686.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_251" sap:VirtualizedContainerService.HintSize="3119.33333333333,840.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_567" sap:VirtualizedContainerService.HintSize="3119.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_653" sap:VirtualizedContainerService.HintSize="3119.33333333333,62" />
      <sap2010:ViewStateData Id="InvokeMethod_10" sap:VirtualizedContainerService.HintSize="3119.33333333333,134" />
      <sap2010:ViewStateData Id="InvokeMethod_5" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="If_246" sap:VirtualizedContainerService.HintSize="464,288" />
      <sap2010:ViewStateData Id="InvokeMethod_6" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="If_247" sap:VirtualizedContainerService.HintSize="707.333333333333,442" />
      <sap2010:ViewStateData Id="Sequence_288" sap:VirtualizedContainerService.HintSize="729.333333333333,566">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_34" sap:VirtualizedContainerService.HintSize="760,718.666666666667" />
      <sap2010:ViewStateData Id="InvokeMethod_8" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="If_283" sap:VirtualizedContainerService.HintSize="200,52">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_1" sap:VirtualizedContainerService.HintSize="3119.33333333333,58" />
      <sap2010:ViewStateData Id="Assign_585" sap:VirtualizedContainerService.HintSize="2971.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_584" sap:VirtualizedContainerService.HintSize="2918.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_586" sap:VirtualizedContainerService.HintSize="2770.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_587" sap:VirtualizedContainerService.HintSize="2770.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_842" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_843" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_426" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_48" sap:VirtualizedContainerService.HintSize="2770.66666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_849" sap:VirtualizedContainerService.HintSize="2770.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_850" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_362" sap:VirtualizedContainerService.HintSize="2770.66666666667,52.6666666666667" />
      <sap2010:ViewStateData Id="Assign_852" sap:VirtualizedContainerService.HintSize="2770.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_853" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_851" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_427" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_364" sap:VirtualizedContainerService.HintSize="200,52.6666666666667" />
      <sap2010:ViewStateData Id="If_363" sap:VirtualizedContainerService.HintSize="2770.66666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_854" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_588" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_590" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_591" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_296" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_592" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_593" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_297" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_254" sap:VirtualizedContainerService.HintSize="553,432">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_594" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Sequence_298" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_38" sap:VirtualizedContainerService.HintSize="2770.66666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_595" sap:VirtualizedContainerService.HintSize="2770.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_836" sap:VirtualizedContainerService.HintSize="2770.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_596" sap:VirtualizedContainerService.HintSize="511,60" />
      <sap2010:ViewStateData Id="Assign_597" sap:VirtualizedContainerService.HintSize="511,60" />
      <sap2010:ViewStateData Id="Assign_600" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_601" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_602" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_603" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_604" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_605" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_299" sap:VirtualizedContainerService.HintSize="264,684">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_52" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_300" sap:VirtualizedContainerService.HintSize="222,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_256" sap:VirtualizedContainerService.HintSize="511,832" />
      <sap2010:ViewStateData Id="Sequence_301" sap:VirtualizedContainerService.HintSize="2770.66666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_607" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_608" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_302" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_257" sap:VirtualizedContainerService.HintSize="2622.66666666667,52.6666666666667" />
      <sap2010:ViewStateData Id="Assign_609" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_610" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_258" sap:VirtualizedContainerService.HintSize="1134,216" />
      <sap2010:ViewStateData Id="Assign_611" sap:VirtualizedContainerService.HintSize="1134,62" />
      <sap2010:ViewStateData Id="Assign_612" sap:VirtualizedContainerService.HintSize="738,62" />
      <sap2010:ViewStateData Id="Assign_613" sap:VirtualizedContainerService.HintSize="738,62" />
      <sap2010:ViewStateData Id="Assign_614" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_615" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_303" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_616" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_304" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_259" sap:VirtualizedContainerService.HintSize="554,340">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_617" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Sequence_305" sap:VirtualizedContainerService.HintSize="576,668">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_39" sap:VirtualizedContainerService.HintSize="738,820.666666666667" />
      <sap2010:ViewStateData Id="InvokeWorkflow_53" sap:VirtualizedContainerService.HintSize="738,22" />
      <sap2010:ViewStateData Id="Assign_618" sap:VirtualizedContainerService.HintSize="590,62" />
      <sap2010:ViewStateData Id="Assign_619" sap:VirtualizedContainerService.HintSize="590,62" />
      <sap2010:ViewStateData Id="Assign_620" sap:VirtualizedContainerService.HintSize="590,62" />
      <sap2010:ViewStateData Id="Assign_621" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_622" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_306" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_260" sap:VirtualizedContainerService.HintSize="464,442" />
      <sap2010:ViewStateData Id="If_261" sap:VirtualizedContainerService.HintSize="590,596" />
      <sap2010:ViewStateData Id="Sequence_307" sap:VirtualizedContainerService.HintSize="612,1026">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_262" sap:VirtualizedContainerService.HintSize="738,1180" />
      <sap2010:ViewStateData Id="Sequence_308" sap:VirtualizedContainerService.HintSize="760,2430.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_263" sap:VirtualizedContainerService.HintSize="886,2584.66666666667" />
      <sap2010:ViewStateData Id="Sequence_309" sap:VirtualizedContainerService.HintSize="908,2708.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_310" sap:VirtualizedContainerService.HintSize="200,100.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_264" sap:VirtualizedContainerService.HintSize="1134,2862.66666666667" />
      <sap2010:ViewStateData Id="Sequence_311" sap:VirtualizedContainerService.HintSize="1156,3344.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_265" sap:VirtualizedContainerService.HintSize="2622.66666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_623" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_624" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_266" sap:VirtualizedContainerService.HintSize="2474.66666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_715" sap:VirtualizedContainerService.HintSize="1026.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_716" sap:VirtualizedContainerService.HintSize="1026.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_717" sap:VirtualizedContainerService.HintSize="1026.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_718" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_719" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Catch`1_4" sap:VirtualizedContainerService.HintSize="404,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_4" sap:VirtualizedContainerService.HintSize="1026.66666666667,52.6666666666667" />
      <sap2010:ViewStateData Id="InvokeWorkflow_71" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_825" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_352" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Assign_826" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Sequence_418" sap:VirtualizedContainerService.HintSize="486,494">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_827" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_419" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_353" sap:VirtualizedContainerService.HintSize="464,332" />
      <sap2010:ViewStateData Id="If_354" sap:VirtualizedContainerService.HintSize="1026.66666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_61" sap:VirtualizedContainerService.HintSize="1026.66666666667,22" />
      <sap2010:ViewStateData Id="Assign_720" sap:VirtualizedContainerService.HintSize="878.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_721" sap:VirtualizedContainerService.HintSize="878.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_722" sap:VirtualizedContainerService.HintSize="878.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_723" sap:VirtualizedContainerService.HintSize="730.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_724" sap:VirtualizedContainerService.HintSize="730.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_725" sap:VirtualizedContainerService.HintSize="730.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_726" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_727" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_13" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Sequence_359" sap:VirtualizedContainerService.HintSize="264,462">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_44" sap:VirtualizedContainerService.HintSize="294.666666666667,614.666666666667" />
      <sap2010:ViewStateData Id="Assign_728" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_729" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_14" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Sequence_360" sap:VirtualizedContainerService.HintSize="264,462">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_308" sap:VirtualizedContainerService.HintSize="464,616" />
      <sap2010:ViewStateData Id="ForEach`1_45" sap:VirtualizedContainerService.HintSize="494.666666666667,768.666666666667" />
      <sap2010:ViewStateData Id="ForEach`1_46" sap:VirtualizedContainerService.HintSize="525.333333333333,921.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_309" sap:VirtualizedContainerService.HintSize="730.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_730" sap:VirtualizedContainerService.HintSize="730.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_731" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_361" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_310" sap:VirtualizedContainerService.HintSize="464,340" />
      <sap2010:ViewStateData Id="Sequence_362" sap:VirtualizedContainerService.HintSize="486,464">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_732" sap:VirtualizedContainerService.HintSize="590,62" />
      <sap2010:ViewStateData Id="Assign_733" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_734" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_363" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_311" sap:VirtualizedContainerService.HintSize="464,442" />
      <sap2010:ViewStateData Id="If_312" sap:VirtualizedContainerService.HintSize="590,596" />
      <sap2010:ViewStateData Id="Sequence_364" sap:VirtualizedContainerService.HintSize="612,822">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_313" sap:VirtualizedContainerService.HintSize="730.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_735" sap:VirtualizedContainerService.HintSize="730.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_736" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_737" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_314" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="Sequence_365" sap:VirtualizedContainerService.HintSize="486,442">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_315" sap:VirtualizedContainerService.HintSize="730.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_738" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_366" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_47" sap:VirtualizedContainerService.HintSize="294.666666666667,338.666666666667" />
      <sap2010:ViewStateData Id="If_316" sap:VirtualizedContainerService.HintSize="730.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_62" sap:VirtualizedContainerService.HintSize="730.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_784" sap:VirtualizedContainerService.HintSize="730.666666666667,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_63" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_783" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_396" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_317" sap:VirtualizedContainerService.HintSize="730.666666666667,52.6666666666667" />
      <sap2010:ViewStateData Id="Assign_739" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_782" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_18" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_19" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_334" sap:VirtualizedContainerService.HintSize="464,214" />
      <sap2010:ViewStateData Id="Sequence_367" sap:VirtualizedContainerService.HintSize="486,542">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_318" sap:VirtualizedContainerService.HintSize="730.666666666667,52.6666666666667" />
      <sap2010:ViewStateData Id="InvokeWorkflow_72" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_862" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_435" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_367" sap:VirtualizedContainerService.HintSize="482.666666666667,52.6666666666667" />
      <sap2010:ViewStateData Id="Assign_785" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_20" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_397" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_335" sap:VirtualizedContainerService.HintSize="482.666666666667,402">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_864" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_866" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_370" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_22" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_436" sap:VirtualizedContainerService.HintSize="264,340.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_368" sap:VirtualizedContainerService.HintSize="464,494.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Catch`1_8" sap:VirtualizedContainerService.HintSize="468.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_8" sap:VirtualizedContainerService.HintSize="482.666666666667,732.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_9" sap:VirtualizedContainerService.HintSize="1125,22" />
      <sap2010:ViewStateData Id="Delay_4" sap:VirtualizedContainerService.HintSize="1125,22" />
      <sap2010:ViewStateData Id="Assign_741" sap:VirtualizedContainerService.HintSize="1125,60" />
      <sap2010:ViewStateData Id="Assign_789" sap:VirtualizedContainerService.HintSize="1125,60" />
      <sap2010:ViewStateData Id="Assign_790" sap:VirtualizedContainerService.HintSize="1125,60" />
      <sap2010:ViewStateData Id="InvokeMethod_20" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="InvokeMethod_21" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="InvokeMethod_22" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="InvokeMethod_23" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="InvokeMethod_24" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="Sequence_399" sap:VirtualizedContainerService.HintSize="240,942">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_337" sap:VirtualizedContainerService.HintSize="214,52">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_788" sap:VirtualizedContainerService.HintSize="958,60" />
      <sap2010:ViewStateData Id="Delay_7" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_14" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_765" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_779" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Delay_8" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_17" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_777" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_778" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_393" sap:VirtualizedContainerService.HintSize="264,408">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="While_1" sap:VirtualizedContainerService.HintSize="464,566" />
      <sap2010:ViewStateData Id="Assign_766" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Append_Line_107" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_767" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Sequence_386" sap:VirtualizedContainerService.HintSize="486,1276">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="DoWhile_3" sap:VirtualizedContainerService.HintSize="958,1438">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_10" sap:VirtualizedContainerService.HintSize="958,22" />
      <sap2010:ViewStateData Id="Assign_764" sap:VirtualizedContainerService.HintSize="711,60" />
      <sap2010:ViewStateData Id="Assign_761" sap:VirtualizedContainerService.HintSize="711,60" />
      <sap2010:ViewStateData Id="Delay_6" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_744" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_99" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_13" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_762" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_368" sap:VirtualizedContainerService.HintSize="264,470">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_763" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_100" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_385" sap:VirtualizedContainerService.HintSize="264,243">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Catch`1_5" sap:VirtualizedContainerService.HintSize="404,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_5" sap:VirtualizedContainerService.HintSize="418,697" />
      <sap2010:ViewStateData Id="DoWhile_2" sap:VirtualizedContainerService.HintSize="464,859">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_770" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_771" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_388" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_329" sap:VirtualizedContainerService.HintSize="464,432" />
      <sap2010:ViewStateData Id="Sequence_389" sap:VirtualizedContainerService.HintSize="486,1455">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_108" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_330" sap:VirtualizedContainerService.HintSize="711,1603" />
      <sap2010:ViewStateData Id="Sequence_369" sap:VirtualizedContainerService.HintSize="733,1927">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_101" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_319" sap:VirtualizedContainerService.HintSize="958,2075">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_370" sap:VirtualizedContainerService.HintSize="980,3839">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_786" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_336" sap:VirtualizedContainerService.HintSize="200,52">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_2" sap:VirtualizedContainerService.HintSize="214,148" />
      <sap2010:ViewStateData Id="Sequence_398" sap:VirtualizedContainerService.HintSize="236,364">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_745" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_746" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_371" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_747" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_748" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_372" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_320" sap:VirtualizedContainerService.HintSize="553,434" />
      <sap2010:ViewStateData Id="Assign_768" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_769" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_387" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_321" sap:VirtualizedContainerService.HintSize="842,584" />
      <sap2010:ViewStateData Id="Sequence_373" sap:VirtualizedContainerService.HintSize="864,708">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_322" sap:VirtualizedContainerService.HintSize="1125,858" />
      <sap2010:ViewStateData Id="Assign_749" sap:VirtualizedContainerService.HintSize="1125,60" />
      <sap2010:ViewStateData Id="Append_Line_102" sap:VirtualizedContainerService.HintSize="1125,22" />
      <sap2010:ViewStateData Id="Sequence_374" sap:VirtualizedContainerService.HintSize="1147,1568">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_780" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_781" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_109" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_395" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_333" sap:VirtualizedContainerService.HintSize="482.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_394" sap:VirtualizedContainerService.HintSize="504.666666666667,1484">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_750" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_751" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_103" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_375" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_323" sap:VirtualizedContainerService.HintSize="730.666666666667,1638">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_64" sap:VirtualizedContainerService.HintSize="611,22" />
      <sap2010:ViewStateData Id="Assign_752" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_65" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Append_Line_104" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_753" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_376" sap:VirtualizedContainerService.HintSize="264,246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_324" sap:VirtualizedContainerService.HintSize="464,394" />
      <sap2010:ViewStateData Id="Sequence_377" sap:VirtualizedContainerService.HintSize="486,680">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_325" sap:VirtualizedContainerService.HintSize="611,828" />
      <sap2010:ViewStateData Id="Sequence_378" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_326" sap:VirtualizedContainerService.HintSize="712,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_379" sap:VirtualizedContainerService.HintSize="752.666666666667,3084.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_327" sap:VirtualizedContainerService.HintSize="878.666666666667,3238.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_380" sap:VirtualizedContainerService.HintSize="900.666666666667,3668.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_328" sap:VirtualizedContainerService.HintSize="1026.66666666667,3822.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_381" sap:VirtualizedContainerService.HintSize="1048.66666666667,4500">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_795" sap:VirtualizedContainerService.HintSize="1108,62" />
      <sap2010:ViewStateData Id="Assign_796" sap:VirtualizedContainerService.HintSize="1108,62" />
      <sap2010:ViewStateData Id="Assign_797" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_798" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Catch`1_6" sap:VirtualizedContainerService.HintSize="404,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_6" sap:VirtualizedContainerService.HintSize="1108,52.6666666666667" />
      <sap2010:ViewStateData Id="Assign_814" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_412" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_348" sap:VirtualizedContainerService.HintSize="1108,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_66" sap:VirtualizedContainerService.HintSize="1108,22" />
      <sap2010:ViewStateData Id="Assign_799" sap:VirtualizedContainerService.HintSize="860,62" />
      <sap2010:ViewStateData Id="Assign_800" sap:VirtualizedContainerService.HintSize="860,62" />
      <sap2010:ViewStateData Id="Assign_801" sap:VirtualizedContainerService.HintSize="860,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_67" sap:VirtualizedContainerService.HintSize="712,22" />
      <sap2010:ViewStateData Id="Assign_865" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_867" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_371" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_23" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_437" sap:VirtualizedContainerService.HintSize="264,340.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_369" sap:VirtualizedContainerService.HintSize="464,494.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Catch`1_9" sap:VirtualizedContainerService.HintSize="468.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_9" sap:VirtualizedContainerService.HintSize="712,732.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_21" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_802" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_803" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_401" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_804" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_805" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_402" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_340" sap:VirtualizedContainerService.HintSize="464,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_403" sap:VirtualizedContainerService.HintSize="486,400">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_806" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_807" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_404" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_341" sap:VirtualizedContainerService.HintSize="712,554">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_405" sap:VirtualizedContainerService.HintSize="734,1512.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_342" sap:VirtualizedContainerService.HintSize="860,1666.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_406" sap:VirtualizedContainerService.HintSize="882,2096.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_808" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_809" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_407" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_343" sap:VirtualizedContainerService.HintSize="1108,2250.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_408" sap:VirtualizedContainerService.HintSize="1130,2987.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_810" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_811" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_409" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_344" sap:VirtualizedContainerService.HintSize="1356,3141.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_110" sap:VirtualizedContainerService.HintSize="1356,22" />
      <sap2010:ViewStateData Id="Sequence_410" sap:VirtualizedContainerService.HintSize="1378,3327.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_326" sap:VirtualizedContainerService.HintSize="1400,3451.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_280" sap:VirtualizedContainerService.HintSize="2474.66666666667,4654">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_327" sap:VirtualizedContainerService.HintSize="2496.66666666667,4870.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_281" sap:VirtualizedContainerService.HintSize="2622.66666666667,5024.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_328" sap:VirtualizedContainerService.HintSize="2644.66666666667,5334">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_282" sap:VirtualizedContainerService.HintSize="2770.66666666667,5488">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_295" sap:VirtualizedContainerService.HintSize="2792.66666666667,6687.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_253" sap:VirtualizedContainerService.HintSize="2918.66666666667,6841.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_294" sap:VirtualizedContainerService.HintSize="2940.66666666667,7067.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_37" sap:VirtualizedContainerService.HintSize="2971.33333333333,7220">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_293" sap:VirtualizedContainerService.HintSize="2993.33333333333,7446">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_252" sap:VirtualizedContainerService.HintSize="3119.33333333333,7600">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_207" sap:VirtualizedContainerService.HintSize="3141.33333333333,9983.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_177" sap:VirtualizedContainerService.HintSize="3267.33333333333,10137.3333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_157" sap:VirtualizedContainerService.HintSize="3289.33333333333,14636.6666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_418" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_419" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_222" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_178" sap:VirtualizedContainerService.HintSize="3515.33333333333,14790.6666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="3537.33333333333,15477.3333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="3577.33333333333,15557.3333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="ShouldCollapseAll">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>