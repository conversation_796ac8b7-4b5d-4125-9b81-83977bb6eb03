Argument Name,Data Type,Category,Description
configurationFolder,String,Configuration & Paths,Base configuration folder path
invoiceSource,String,Email Processing,Source of invoices (e.g. OutlookClientEmail)
emailAccount,String,Email Processing,Email account to process
emailFolder,String,Email Processing,Email folder name
numberOfEmails,Int32,Email Processing,Number of emails to process
directoriesNames,String,Configuration & Paths,Pipe-separated directory names
logFolderName,String,Configuration & Paths,Name of the logs folder
invoiceFolderPath,String,Configuration & Paths,Path to invoice folder
colemanAPI,String,API & Integration,Coleman API endpoint URL
tenantID,String,API & Integration,Tenant ID for API calls
userIdentifier,String,User & Distribution,User identifier for processing
distributionType,String,User & Distribution,Type of distribution
projectPath,String,Configuration & Paths,Project path location
maxNotReceivedCount,Int32,Filters & Limits,Maximum not received count
enableMessageBoxes,Boolean,Processing Flags,Enable message boxes for debugging
poFilterCondition,String,Filters & Limits,Purchase order filter condition
poFilterValues,String,Filters & Limits,Purchase order filter values
datalakeAPILogicalId,String,API & Integration,Data lake API logical identifier
chargeCode,String,Codes & Configuration,Charge code for processing
discountCode,String,Codes & Configuration,Discount code configuration
authUser,String,User & Distribution,Authentication user
imsAPIUrl,String,API & Integration,IMS API URL endpoint
extractNumericFromPO,Boolean,Processing Flags,Extract numeric values from PO
vatCodeConfig,String,Codes & Configuration,VAT code configuration
poDiscountsHandlingConfig,Boolean,Processing Flags,PO discounts handling configuration
matchVendorItemCode,Boolean,Processing Flags,Match vendor item codes
extractFromWidgetDatalake,Boolean,Processing Flags,Extract data from widget datalake
approvalRequired,Boolean,Processing Flags,Whether approval is required
checkAmountBussinessRule,String,Business Logic & Rules,Business rule for amount checking
approvalWorkflow,String,Codes & Configuration,Approval workflow name
processExpenseInvoice,Boolean,Processing Flags,Process expense invoices
ERP,String,Codes & Configuration,ERP system identifier
useGenAIExtraction,Boolean,Processing Flags,Use GenAI for extraction
processDeliveryNote,Boolean,Processing Flags,Process delivery notes
GenAIPromptFileURL,String,GenAI Configuration,GenAI prompt file URL
processEmails,Boolean,Email Processing,Process emails flag
processFolders,Boolean,Email Processing,Process folders flag
businessRuleForTolerance,String,Business Logic & Rules,Business rule for tolerance
businessRuleForGUID,String,Business Logic & Rules,Business rule for GUID
division,String,Company & Division,Division identifier
company,String,Company & Division,Company identifier
IONAPI,String,API & Integration,ION API endpoint
includeDistribution,Boolean,Processing Flags,Include distribution in processing
businessRuleForDivisionGLCode,String,Business Logic & Rules,Business rule for division GL code
useBusinessRuleForTelerance,Boolean,Processing Flags,Use business rule for tolerance
enableStagingForExpenseInException,Boolean,Processing Flags,Enable staging for expense in exception
mandateGEOC,Boolean,Processing Flags,Mandate GEOC
SplittingDoc,Boolean,Processing Flags,Document splitting enabled
groupByTransDate,Boolean,Processing Flags,Group by transaction date
GenAIPromptFileURLForSplit,String,GenAI Configuration,GenAI prompt file URL for splitting
GenAIPromptFileURLForSplit2,String,GenAI Configuration,GenAI prompt file URL for splitting (second)
createInvoiceIrrespectiveTolerance,Boolean,Processing Flags,Create invoice irrespective of tolerance
autoAllocateOpenLines,Boolean,Processing Flags,Auto allocate open lines
reprocess,Boolean,Processing Flags,Reprocess flag
numberOfParts,Int32,Filters & Limits,Number of parts for processing
AutomateValidation,Boolean,Processing Flags,Automate validation process
AutomateApproval,Boolean,Processing Flags,Automate approval process
SendEmail,Boolean,Processing Flags,Send email notifications
BusinessRuleAPResp,String,Business Logic & Rules,Business rule for AP response
genAiVersion,String,GenAI Configuration,GenAI version identifier
genAiModel,String,GenAI Configuration,GenAI model name
handleCashDiscount,Boolean,Processing Flags,Handle cash discount
MandateDeliveryNoteWhenPo,Boolean,Processing Flags,Mandate delivery note when PO exists
