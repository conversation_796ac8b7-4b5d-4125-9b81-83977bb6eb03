Argument Name,Data Type,Category,Default Value,Description
configurationFolder,String,Configuration & Paths,C:\M3VendorInvoiceProcessing,Base configuration folder path(Existing folder from the server)
invoiceSource,String,Email Processing,OutlookClientEmail,Source of invoices (e.g. OutlookClientEmail/OutlookGraphEmail)
emailAccount,String,Email Processing,<EMAIL>,Email account to process(Email should be logged in Outlook on the server)
emailFolder,String,Email Processing,M3,Email folder name
numberOfEmails,Int32,Email Processing,30,Number of emails to process in a run
directoriesNames,String,Configuration & Paths,OutlookDownloads|Failure|InProgress|Success|Logs|Reprocess,Pipe-separated directory names
logFolderName,String,Configuration & Paths,Logs,Name of the logs folder
invoiceFolderPath,String,Configuration & Paths,C:\M3VendorInvoiceProcessingGenAI,Path to invoices folder 
colemanAPI,String,API & Integration,https://mingle-ionapi.inforcloudsuite.com/OLDCASTLE_PRD/APIFLOWS/Invoice_Matching_val_percentage/Invoice_Matching_val_percentage?ionapiRespStyle=sync,Coleman API endpoint URL
tenantID,String,API & Integration,https://mingle-ionapi.inforcloudsuite.com/OLDCASTLE_PRD/,Tenant ID for API calls
userIdentifier,String,User & Distribution,TO_APRESP,User identifier for Notifications(If it is set to TO_APRESP then the AP responsible person for that vendor will get notification)
distributionType,String,User & Distribution,USER,Type of distribution
projectPath,String,Configuration & Paths,"[Environment.GetFolderPath(Environment.SpecialFolder.UserProfile) + ""\AppData\Local\InforRPA\M3InvoiceProcessingGenAIV2""]",Project path location
maxNotReceivedCount,Int32,Filters & Limits,10,Maximum not received count for PO not received case
enableMessageBoxes,Boolean,Processing Flags,TRUE,Enable message boxes for debugging
poFilterCondition,String,Filters & Limits,NA,Purchase order filter condition
poFilterValues,String,Filters & Limits,1|4,Purchase order filter values
datalakeAPILogicalId,String,API & Integration,infor.ims.imsfromrpastudio,Data lake API logical identifier(Studio and Assistant are having different IDs)
chargeCode,String,Codes & Configuration,POPPA,Charge code for processing
discountCode,String,Codes & Configuration,110,Discount code configuration
authUser,String,User & Distribution,SVCDEM3WFL,Authentication user for creating Invoice
imsAPIUrl,String,API & Integration,https://mingle-ionapi.inforcloudsuite.com/OLDCASTLE_PRD/IONSERVICES/api/ion/messaging/service/v2/message,IMS API URL endpoint
extractNumericFromPO,Boolean,Processing Flags,FALSE,Extract only numeric values from PO
vatCodeConfig,String,Codes & Configuration,01,"VAT code configuration(If it set to NA, M3 specific values will be taken)"
poDiscountsHandlingConfig,Boolean,Processing Flags,FALSE,PO discounts handling configuration
matchVendorItemCode,Boolean,Processing Flags,FALSE,Match vendor item codes
extractFromWidgetDatalake,Boolean,Processing Flags,TRUE,Extract data from widget datalake(If user saves the data in exception handling)
approvalRequired,Boolean,Processing Flags,FALSE,
checkAmountBussinessRule,String,Business Logic & Rules,Amount_Check,Business rule for amount checking
approvalWorkflow,String,Codes & Configuration,M3UserApproval,Approval workflow name
processExpenseInvoice,Boolean,Processing Flags,TRUE,Process expense invoices
ERP,String,Codes & Configuration,M3,ERP system identifier
useGenAIExtraction,Boolean,Processing Flags,TRUE,Use GenAI for extraction
processDeliveryNote,Boolean,Processing Flags,TRUE,Process delivery notes
GenAIPromptFileURL,String,GenAI Configuration,https://mingle-ionapi.inforcloudsuite.com/OLDCASTLE_PRD/IDM/api/items/search/item/resource?%24query=%2FGenAIPrompt%5B%40RESOURCENAME%20%3D%20%22GenAIPrompt.txt%22%5D%20SORTBY(%40LASTCHANGEDTS%20DESCENDING),GenAI prompt file URL
processEmails,Boolean,Email Processing,TRUE,Process emails flag
processFolders,Boolean,Email Processing,FALSE,Process folders flag
businessRuleForTolerance,String,Business Logic & Rules,DivisionVendorTolerance,Business rule for tolerance
businessRuleForGUID,String,Business Logic & Rules,VendorDivisionGUID,Business rule for GUID
division,String,Company & Division,670,Division identifier(If set to FROM_BILL_TO then company and division will be extracted from bill to address)
company,String,Company & Division,999,Company identifier
IONAPI,String,API & Integration,https://mingle-ionapi.inforcloudsuite.com/OLDCASTLE_PRD/ifsservice/usermgt/v2/users/me,ION API endpoint
includeDistribution,Boolean,Processing Flags,TRUE,Include distribution in processing(Distribute variance)
businessRuleForDivisionGLCode,String,Business Logic & Rules,NA,Business rule for division GL code and 7 dimensions
useBusinessRuleForTelerance,Boolean,Processing Flags,FALSE,Use business rule for tolerance
enableStagingForExpenseInException,Boolean,Processing Flags,TRUE,Enable staging to exception handling for expense in exception
mandateGEOC,Boolean,Processing Flags,FALSE,Mandate GEOC(GEOC is mandated for some tenants while adding header)
SplittingDoc,Boolean,Processing Flags,TRUE,Document splitting enabled
groupByTransDate,Boolean,Processing Flags,FALSE,Group by transaction date for receipt lines
GenAIPromptFileURLForSplit,String,GenAI Configuration,https://mingle-ionapi.inforcloudsuite.com/OLDCASTLE_PRD/IDM/api/items/search/item/resource?%24query=%2FGenAIPrompt%5B%40RESOURCENAME%20%3D%20%22GenAISplit1.txt%22%5D%20SORTBY(%40LASTCHANGEDTS%20DESCENDING),GenAI prompt file URL for splitting
GenAIPromptFileURLForSplit2,String,GenAI Configuration,https://mingle-ionapi.inforcloudsuite.com/OLDCASTLE_PRD/IDM/api/items/search/item/resource?%24query=%2FGenAIPrompt%5B%40RESOURCENAME%20%3D%20%22GenAISplit2.txt%22%5D%20SORTBY(%40LASTCHANGEDTS%20DESCENDING),GenAI prompt file URL for splitting (second)
createInvoiceIrrespectiveTolerance,Boolean,Processing Flags,FALSE,Create invoice irrespective of tolerance
autoAllocateOpenLines,Boolean,Processing Flags,FALSE,Auto allocate open lines for that Vendor
reprocess,Boolean,Processing Flags,TRUE,Reprocess the record from exception
numberOfParts,Int32,Filters & Limits,5,Number of parts for processing
AutomateValidation,Boolean,Processing Flags,FALSE,Automate validation process
AutomateApproval,Boolean,Processing Flags,FALSE,Automate approval process
SendEmail,Boolean,Processing Flags,FALSE,Send email notifications
BusinessRuleAPResp,String,Business Logic & Rules,NA,Business rule for AP response
genAiVersion,String,GenAI Configuration,claude-3-7-sonnet-20250219-v1:0,GenAI version identifier
genAiModel,String,GenAI Configuration,CLAUDE,GenAI model name
handleCashDiscount,Boolean,Processing Flags,TRUE,Handle cash discount
MandateDeliveryNoteWhenPo,Boolean,Processing Flags,FALSE,Mandate delivery note when PO exists
