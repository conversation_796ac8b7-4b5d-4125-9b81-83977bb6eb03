Argument Name,Data Type,Category,Default Value,Description
configurationFolder,String,Configuration & Paths,C:\M3VendorInvoiceProcessing,Base configuration folder path
invoiceSource,String,Email Processing,OutlookClientEmail,Source of invoices (e.g. OutlookClientEmail)
emailAccount,String,Email Processing,<EMAIL>,Email account to process
emailFolder,String,Email Processing,M3,Email folder name
numberOfEmails,Int32,Email Processing,30,Number of emails to process
directoriesNames,String,Configuration & Paths,OutlookDownloads|Failure|InProgress|Success|Logs|Reprocess,Pipe-separated directory names
logFolderName,String,Configuration & Paths,Logs,Name of the logs folder
invoiceFolderPath,String,Configuration & Paths,C:\M3VendorInvoiceProcessingGenAI,Path to invoice folder
colemanAPI,String,API & Integration,https://mingle-ionapi.inforcloudsuite.com/OLDCASTLE_PRD/APIFLOWS/Invoice_Matching_val_percentage/Invoice_Matching_val_percentage?ionapiRespStyle=sync,Coleman API endpoint URL
tenantID,String,API & Integration,https://mingle-ionapi.inforcloudsuite.com/OLDCASTLE_PRD/,Tenant ID for API calls
userIdentifier,String,User & Distribution,TO_APRESP,User identifier for processing
distributionType,String,User & Distribution,USER,Type of distribution
projectPath,String,Configuration & Paths,[Environment.GetFolderPath(Environment.SpecialFolder.UserProfile) + "\AppData\Local\InforRPA\M3InvoiceProcessingGenAIV2"],Project path location
maxNotReceivedCount,Int32,Filters & Limits,10,Maximum not received count
enableMessageBoxes,Boolean,Processing Flags,True,Enable message boxes for debugging
poFilterCondition,String,Filters & Limits,NA,Purchase order filter condition
poFilterValues,String,Filters & Limits,1|4,Purchase order filter values
datalakeAPILogicalId,String,API & Integration,infor.ims.imsfromrpastudio,Data lake API logical identifier
chargeCode,String,Codes & Configuration,POPPA,Charge code for processing
discountCode,String,Codes & Configuration,110,Discount code configuration
authUser,String,User & Distribution,SVCDEM3WFL,Authentication user
imsAPIUrl,String,API & Integration,https://mingle-ionapi.inforcloudsuite.com/OLDCASTLE_PRD/IONSERVICES/api/ion/messaging/service/v2/message,IMS API URL endpoint
extractNumericFromPO,Boolean,Processing Flags,False,Extract numeric values from PO
vatCodeConfig,String,Codes & Configuration,01,VAT code configuration
poDiscountsHandlingConfig,Boolean,Processing Flags,False,PO discounts handling configuration
matchVendorItemCode,Boolean,Processing Flags,False,Match vendor item codes
extractFromWidgetDatalake,Boolean,Processing Flags,True,Extract data from widget datalake
approvalRequired,Boolean,Processing Flags,False,Whether approval is required
checkAmountBussinessRule,String,Business Logic & Rules,Amount_Check,Business rule for amount checking
approvalWorkflow,String,Codes & Configuration,M3UserApproval,Approval workflow name
processExpenseInvoice,Boolean,Processing Flags,True,Process expense invoices
ERP,String,Codes & Configuration,M3,ERP system identifier
useGenAIExtraction,Boolean,Processing Flags,True,Use GenAI for extraction
processDeliveryNote,Boolean,Processing Flags,True,Process delivery notes
GenAIPromptFileURL,String,GenAI Configuration,https://mingle-ionapi.inforcloudsuite.com/OLDCASTLE_PRD/IDM/api/items/search/item/resource?%24query=%2FGenAIPrompt%5B%40RESOURCENAME%20%3D%20%22GenAIPrompt.txt%22%5D%20SORTBY(%40LASTCHANGEDTS%20DESCENDING),GenAI prompt file URL
processEmails,Boolean,Email Processing,True,Process emails flag
processFolders,Boolean,Email Processing,False,Process folders flag
businessRuleForTolerance,String,Business Logic & Rules,DivisionVendorTolerance,Business rule for tolerance
businessRuleForGUID,String,Business Logic & Rules,VendorDivisionGUID,Business rule for GUID
division,String,Company & Division,670,Division identifier
company,String,Company & Division,999,Company identifier
IONAPI,String,API & Integration,https://mingle-ionapi.inforcloudsuite.com/OLDCASTLE_PRD/ifsservice/usermgt/v2/users/me,ION API endpoint
includeDistribution,Boolean,Processing Flags,True,Include distribution in processing
businessRuleForDivisionGLCode,String,Business Logic & Rules,NA,Business rule for division GL code
useBusinessRuleForTelerance,Boolean,Processing Flags,False,Use business rule for tolerance
enableStagingForExpenseInException,Boolean,Processing Flags,True,Enable staging for expense in exception
mandateGEOC,Boolean,Processing Flags,False,Mandate GEOC
SplittingDoc,Boolean,Processing Flags,True,Document splitting enabled
groupByTransDate,Boolean,Processing Flags,False,Group by transaction date
GenAIPromptFileURLForSplit,String,GenAI Configuration,https://mingle-ionapi.inforcloudsuite.com/OLDCASTLE_PRD/IDM/api/items/search/item/resource?%24query=%2FGenAIPrompt%5B%40RESOURCENAME%20%3D%20%22GenAISplit1.txt%22%5D%20SORTBY(%40LASTCHANGEDTS%20DESCENDING),GenAI prompt file URL for splitting
GenAIPromptFileURLForSplit2,String,GenAI Configuration,https://mingle-ionapi.inforcloudsuite.com/OLDCASTLE_PRD/IDM/api/items/search/item/resource?%24query=%2FGenAIPrompt%5B%40RESOURCENAME%20%3D%20%22GenAISplit2.txt%22%5D%20SORTBY(%40LASTCHANGEDTS%20DESCENDING),GenAI prompt file URL for splitting (second)
createInvoiceIrrespectiveTolerance,Boolean,Processing Flags,False,Create invoice irrespective of tolerance
autoAllocateOpenLines,Boolean,Processing Flags,False,Auto allocate open lines
reprocess,Boolean,Processing Flags,True,Reprocess flag
numberOfParts,Int32,Filters & Limits,5,Number of parts for processing
AutomateValidation,Boolean,Processing Flags,False,Automate validation process
AutomateApproval,Boolean,Processing Flags,False,Automate approval process
SendEmail,Boolean,Processing Flags,False,Send email notifications
BusinessRuleAPResp,String,Business Logic & Rules,NA,Business rule for AP response
genAiVersion,String,GenAI Configuration,claude-3-7-sonnet-20250219-v1:0,GenAI version identifier
genAiModel,String,GenAI Configuration,CLAUDE,GenAI model name
handleCashDiscount,Boolean,Processing Flags,True,Handle cash discount
MandateDeliveryNoteWhenPo,Boolean,Processing Flags,False,Mandate delivery note when PO exists
