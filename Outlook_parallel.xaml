﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:iae="clr-namespace:Infor.Activities.Email;assembly=Infor.Activities.Email"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="emailAccount" Type="InArgument(x:String)" />
    <x:Property Name="emailFolder" Type="InArgument(x:String)" />
    <x:Property Name="numberOfEmails" Type="InArgument(x:Int32)" />
    <x:Property Name="enableMessageBoxes" Type="InArgument(x:Boolean)" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
    <x:Property Name="invoiceSource" Type="InArgument(x:String)" />
    <x:Property Name="configurationFolder" Type="InArgument(x:String)" />
    <x:Property Name="logFolderName" Type="InArgument(x:String)" />
    <x:Property Name="directoriesNames" Type="InArgument(x:String)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="ocrKeysDictionary" Type="InArgument(scg:Dictionary(x:String, scg:List(x:String)))" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="colemanAPI" Type="InArgument(x:String)" />
    <x:Property Name="userIdentifier" Type="InArgument(x:String)" />
    <x:Property Name="distributionType" Type="InArgument(x:String)" />
    <x:Property Name="poFilterValues" Type="InArgument(x:String)" />
    <x:Property Name="poFilterCondition" Type="InArgument(x:String)" />
    <x:Property Name="datalakeAPILogicalId" Type="InArgument(x:String)" />
    <x:Property Name="chargeCode" Type="InArgument(x:String)" />
    <x:Property Name="discountCode" Type="InArgument(x:String)" />
    <x:Property Name="authUser" Type="InArgument(x:String)" />
    <x:Property Name="imsAPIUrl" Type="InArgument(x:String)" />
    <x:Property Name="poDiscountsHandlingConfig" Type="InArgument(x:Boolean)" />
    <x:Property Name="vatCodeConfig" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="processExpenseInvoice" Type="InArgument(x:Boolean)" />
    <x:Property Name="approvalRequired" Type="InArgument(x:Boolean)" />
    <x:Property Name="matchVendorItemCode" Type="InArgument(x:Boolean)" />
    <x:Property Name="approvalWorkflow" Type="InArgument(x:String)" />
    <x:Property Name="extractNumericFromPO" Type="InArgument(x:Boolean)" />
    <x:Property Name="extractFromWidgetDatalake" Type="InArgument(x:Boolean)" />
    <x:Property Name="checkAmountBussinessRule" Type="InArgument(x:String)" />
    <x:Property Name="InProgressFolder" Type="InArgument(x:String)" />
    <x:Property Name="approvalList" Type="InArgument(scg:List(x:String))" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>System</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.IO</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <TryCatch DisplayName="TryCatch - Process Outlook Emails" sap2010:WorkflowViewState.IdRef="TryCatch_5">
    <TryCatch.Variables>
      <Variable x:TypeArguments="scg:List(x:String)" Name="inProgressFiles" />
      <Variable x:TypeArguments="scg:List(scg:List(x:String))" Name="approvalLists" />
      <Variable x:TypeArguments="x:String" Name="movedFile" />
    </TryCatch.Variables>
    <TryCatch.Try>
      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_72">
        <Assign sap2010:WorkflowViewState.IdRef="Assign_84">
          <Assign.To>
            <OutArgument x:TypeArguments="scg:List(scg:List(x:String))">[approvalLists]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[New List(Of List(Of String))]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_86">
          <Assign.To>
            <OutArgument x:TypeArguments="scg:List(x:String)">[approvalList]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="scg:List(x:String)">[new list(Of String)]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_82">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[InProgressFolder]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[configurationFolder+ "\InProgress"]</InArgument>
          </Assign.Value>
        </Assign>
        <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="True" DirPath="[inProgressFolder]" DisplayName="Get Files in Directory" FileType="All" Files="[inProgressFiles]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_2" IncludeSubDir="True" />
        <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_7" Values="[inProgressFiles]">
          <ActivityAction x:TypeArguments="x:String">
            <ActivityAction.Argument>
              <DelegateInArgument x:TypeArguments="x:String" Name="file" />
            </ActivityAction.Argument>
            <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_1" Source="[file]" />
          </ActivityAction>
        </ForEach>
        <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_8" Values="[Directory.GetDirectories(inProgressFolder)]">
          <ActivityAction x:TypeArguments="x:String">
            <ActivityAction.Argument>
              <DelegateInArgument x:TypeArguments="x:String" Name="folder" />
            </ActivityAction.Argument>
            <ias:Directory_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete Directory" sap2010:WorkflowViewState.IdRef="Directory_Delete_1" Source="[folder]" />
          </ActivityAction>
        </ForEach>
        <Sequence DisplayName="Process Outlook Emails" sap2010:WorkflowViewState.IdRef="Sequence_67">
          <Sequence.Variables>
            <Variable x:TypeArguments="scg:List(iae:Mail)" Default="[new List( Of Mail)]" Name="ListAllemails" />
            <Variable x:TypeArguments="x:Int32" Name="ln" />
            <Variable x:TypeArguments="scg:List(iae:Mail)" Name="SM1" />
            <Variable x:TypeArguments="scg:List(iae:Mail)" Name="SM2" />
            <Variable x:TypeArguments="scg:List(iae:Mail)" Name="SM3" />
            <Variable x:TypeArguments="scg:List(iae:Mail)" Name="SM4" />
            <Variable x:TypeArguments="scg:List(iae:Mail)" Name="SM5" />
            <Variable x:TypeArguments="scg:List(x:String)" Default="[new list ( of string)]" Name="tsubjects2" />
            <Variable x:TypeArguments="scg:List(x:String)" Default="[new list ( of string)]" Name="tsubjects3" />
            <Variable x:TypeArguments="scg:List(x:String)" Default="[new list ( of string)]" Name="tsubjects4" />
            <Variable x:TypeArguments="scg:List(x:String)" Default="[new list ( of string)]" Name="tsubjects5" />
            <Variable x:TypeArguments="scg:List(x:String)" Default="[new list ( of string)]" Name="tsubjects1" />
            <Variable x:TypeArguments="x:String" Name="p1_time" />
            <Variable x:TypeArguments="x:String" Name="p2_time" />
            <Variable x:TypeArguments="x:String" Name="p3_time" />
            <Variable x:TypeArguments="x:String" Name="p4_time" />
            <Variable x:TypeArguments="x:String" Name="p5_time" />
            <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="emailsubjects1" />
            <Variable x:TypeArguments="x:Int32" Default="5" Name="numberOfParts" />
            <Variable x:TypeArguments="x:Int32" Name="emailResponseCode" />
            <Variable x:TypeArguments="scg:List(x:String)" Name="listInReprocessFiles" />
            <Variable x:TypeArguments="x:String" Name="reprocessFolder" />
            <Variable x:TypeArguments="x:Boolean" Name="reprocessExists" />
            <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="emailsubjects2" />
            <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="emailsubjects3" />
            <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="emailsubjects4" />
            <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="emailsubjects5" />
          </Sequence.Variables>
          <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_11">
            <iad:CommentOut.Activities>
              <DoWhile DisplayName="Read all Outlook unread emails" sap2010:WorkflowViewState.IdRef="DoWhile_3">
                <DoWhile.Variables>
                  <Variable x:TypeArguments="iae:Mail" Name="mail" />
                  <Variable x:TypeArguments="x:Int32" Name="TotalNoOfEmails" />
                </DoWhile.Variables>
                <DoWhile.Condition>[emails.count&gt;0]</DoWhile.Condition>
                <Sequence DisplayName="Outlook Sequence" sap2010:WorkflowViewState.IdRef="Sequence_60">
                  <Switch x:TypeArguments="x:Boolean" DisplayName="If Emails count is &gt;0" Expression="[emails.count&gt;0]" sap2010:WorkflowViewState.IdRef="Switch`1_5">
                    <ForEach x:TypeArguments="iae:Mail" x:Key="True" DisplayName="ForEach&lt;Mail&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_5" Values="[emails]">
                      <ActivityAction x:TypeArguments="iae:Mail">
                        <ActivityAction.Argument>
                          <DelegateInArgument x:TypeArguments="iae:Mail" Name="mail" />
                        </ActivityAction.Argument>
                        <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_4" MethodName="Add">
                          <InvokeMethod.TargetObject>
                            <InArgument x:TypeArguments="scg:List(iae:Mail)">[ListAllemails]</InArgument>
                          </InvokeMethod.TargetObject>
                          <InArgument x:TypeArguments="iae:Mail">[mail]</InArgument>
                        </InvokeMethod>
                      </ActivityAction>
                    </ForEach>
                  </Switch>
                </Sequence>
              </DoWhile>
            </iad:CommentOut.Activities>
          </iad:CommentOut>
          <Switch x:TypeArguments="x:String" DisplayName="Read Outlook Emails as Per Source" Expression="[invoiceSource]" sap2010:WorkflowViewState.IdRef="Switch`1_4">
            <iae:GetOutlookEmails Filter="{x:Null}" x:Key="OutlookClientEmail" Account="[emailAccount.Trim]" ContinueOnError="False" DisplayName="Get Outlook Emails" Emails="[ListAllemails]" ErrorCode="[emailResponseCode]" Folder="[emailFolder.Trim]" sap2010:WorkflowViewState.IdRef="GetOutlookEmails_5" MarkAsRead="True" OnlyUnread="True" TopEmails="[numberOfEmails]" />
            <Sequence x:Key="OutlookGraphEmail" DisplayName="Graph Sequence" sap2010:WorkflowViewState.IdRef="Sequence_59">
              <If Condition="[enableMessageBoxes]" DisplayName="Check if enable message boxes" sap2010:WorkflowViewState.IdRef="If_14">
                <If.Then>
                  <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_13" Selection="OK" Text="[&quot;Accessing Emails via Graph Activities from Folder: &quot;+emailFolder]" />
                </If.Then>
              </If>
              <TryCatch DisplayName="TryCatch - Get Email messages Graph" sap2010:WorkflowViewState.IdRef="TryCatch_4">
                <TryCatch.Try>
                  <Sequence DisplayName="Sequence (Get Emails via Graph API)" sap2010:WorkflowViewState.IdRef="Sequence_58">
                    <Sequence.Variables>
                      <Variable x:TypeArguments="x:Int32" Name="activityResponseCode_Graph" />
                    </Sequence.Variables>
                  </Sequence>
                </TryCatch.Try>
                <TryCatch.Catches>
                  <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_4">
                    <ActivityAction x:TypeArguments="s:Exception">
                      <ActivityAction.Argument>
                        <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                      </ActivityAction.Argument>
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line - Catch Get Email messages Graph" sap2010:WorkflowViewState.IdRef="Append_Line_41" Line="[Exception.message]" Source="[logFile]" />
                    </ActivityAction>
                  </Catch>
                </TryCatch.Catches>
              </TryCatch>
            </Sequence>
          </Switch>
          <ias:Append_Line ContinueOnError="False" DisplayName="Append Line" ErrorCode="[emailResponseCode]" sap2010:WorkflowViewState.IdRef="Append_Line_42" Line="[Environment.newLine+&quot;Total number of emails read is &quot;+ListAllemails.count.tostring+Environment.newLine+&quot;Emails Response Status code : &quot;+emailResponseCode.tostring]" Source="[logfile]" />
          <Sequence DisplayName="Create Lists for 5 sub processes" sap2010:WorkflowViewState.IdRef="Sequence_61">
            <Assign sap2010:WorkflowViewState.IdRef="Assign_66">
              <Assign.To>
                <OutArgument x:TypeArguments="x:Int32">[ln]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:Int32">[cint(ListAllemails.Count/ numberOfParts)]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_67">
              <Assign.To>
                <OutArgument x:TypeArguments="scg:List(iae:Mail)">[SM1]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="scg:List(iae:Mail)">[ListAllemails.Skip(1 - 1).Take(ln).ToList()]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_68">
              <Assign.To>
                <OutArgument x:TypeArguments="scg:List(iae:Mail)">[SM2]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="scg:List(iae:Mail)">[ListAllemails.Skip(1*ln).Take(ln).ToList()]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_69">
              <Assign.To>
                <OutArgument x:TypeArguments="scg:List(iae:Mail)">[SM3]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="scg:List(iae:Mail)">[ListAllemails.Skip(2*ln).Take(ln).ToList()]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_70">
              <Assign.To>
                <OutArgument x:TypeArguments="scg:List(iae:Mail)">[SM4]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="scg:List(iae:Mail)">[ListAllemails.Skip(3*ln).Take(ln).ToList()]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_71">
              <Assign.To>
                <OutArgument x:TypeArguments="scg:List(iae:Mail)">[SM5]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="scg:List(iae:Mail)">[ListAllemails.Skip(4*ln).ToList()]</InArgument>
              </Assign.Value>
            </Assign>
          </Sequence>
          <Switch x:TypeArguments="x:Boolean" DisplayName="Initiate Parallel Processing if Mails Available" Expression="[ListAllemails.count&gt;0]" sap2010:WorkflowViewState.IdRef="Switch`1_6">
            <Parallel x:Key="True" sap2010:WorkflowViewState.IdRef="Parallel_5">
              <Sequence DisplayName="Sequence--1" sap2010:WorkflowViewState.IdRef="Sequence_62">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_72">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[p1_time]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["start time: "+Environment.newLine+System.DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")]</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[SM1.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_19">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_77">
                      <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;submails&quot;,SM1},{&quot;configurationFolder&quot;,configurationFolder},{&quot;logfile&quot;,logfile},{&quot;ocrKeysDictionary&quot;,ocrKeysDictionary},{&quot;tenantID&quot;,tenantID},{&quot;colemanAPI&quot;,colemanAPI},{&quot;userIdentifier&quot;,userIdentifier},{&quot;distributionType&quot;,distributionType},{&quot;projectPath&quot;,projectPath},{&quot;poFilterValues&quot;,poFilterValues},{&quot;poFilterCondition&quot;,poFilterCondition},{&quot;datalakeAPILogicalId&quot;,datalakeAPILogicalId},{&quot;chargeCode&quot;,chargeCode},{&quot;discountCode&quot;,discountCode},{&quot;authUser&quot;,authUser},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;extractNumericFromPO&quot;,extractNumericFromPO},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;poDiscountsHandlingConfig&quot;,poDiscountsHandlingConfig},{&quot;matchVendorItemCode&quot;,matchVendorItemCode},{&quot;extractFromWidgetDatalake&quot;,extractFromWidgetDatalake},{&quot;approvalRequired&quot;,approvalRequired},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;processExpenseInvoice&quot;,processExpenseInvoice},{&quot;miscValues&quot;,miscValues},{&quot;invoiceSource&quot;,invoiceSource},{&quot;enableMessageBoxes&quot;,enableMessageBoxes},{&quot;seqnumber&quot;,&quot;1&quot;},{&quot;DownloadTo&quot;,&quot;DownloadedFiles_&quot;},{&quot;FileRenameFormat&quot;,&quot;InforOS_A&quot;}}]" ContinueOnError="False" DisplayName="Invoke preprocess" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_32" OutputArguments="[emailsubjects1]" WorkflowFile="[projectPath+&quot;\preprocess.xaml&quot;]" />
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_73">
                        <Assign.To>
                          <OutArgument x:TypeArguments="scg:List(x:String)">[tsubjects1]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="scg:List(x:String)">[TryCast(emailsubjects1("emailsubjects"),List(Of String))]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                  </If.Then>
                </If>
              </Sequence>
              <Sequence DisplayName="Sequence--2" sap2010:WorkflowViewState.IdRef="Sequence_63">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_74">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[p2_time]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["start time: "+Environment.newLine+System.DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")]</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[SM2.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_18">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_76">
                      <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;submails&quot;,SM2},{&quot;configurationFolder&quot;,configurationFolder},{&quot;logfile&quot;,logfile},{&quot;ocrKeysDictionary&quot;,ocrKeysDictionary},{&quot;tenantID&quot;,tenantID},{&quot;colemanAPI&quot;,colemanAPI},{&quot;userIdentifier&quot;,userIdentifier},{&quot;distributionType&quot;,distributionType},{&quot;projectPath&quot;,projectPath},{&quot;poFilterValues&quot;,poFilterValues},{&quot;poFilterCondition&quot;,poFilterCondition},{&quot;datalakeAPILogicalId&quot;,datalakeAPILogicalId},{&quot;chargeCode&quot;,chargeCode},{&quot;discountCode&quot;,discountCode},{&quot;authUser&quot;,authUser},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;extractNumericFromPO&quot;,extractNumericFromPO},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;poDiscountsHandlingConfig&quot;,poDiscountsHandlingConfig},{&quot;matchVendorItemCode&quot;,matchVendorItemCode},{&quot;extractFromWidgetDatalake&quot;,extractFromWidgetDatalake},{&quot;approvalRequired&quot;,approvalRequired},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;processExpenseInvoice&quot;,processExpenseInvoice},{&quot;miscValues&quot;,miscValues},{&quot;invoiceSource&quot;,invoiceSource},{&quot;enableMessageBoxes&quot;,enableMessageBoxes},{&quot;seqnumber&quot;,&quot;2&quot;},{&quot;DownloadTo&quot;,&quot;DownloadedFiles_&quot;},{&quot;FileRenameFormat&quot;,&quot;InforOS_B&quot;}}]" ContinueOnError="False" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_33" OutputArguments="[emailsubjects2]" WorkflowFile="[projectPath+&quot;\preprocess.xaml&quot;]" />
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_75">
                        <Assign.To>
                          <OutArgument x:TypeArguments="scg:List(x:String)">[tsubjects2]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="scg:List(x:String)">[TryCast(emailsubjects2("emailsubjects"),List(Of String))]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                  </If.Then>
                </If>
              </Sequence>
              <Sequence DisplayName="Sequence--3" sap2010:WorkflowViewState.IdRef="Sequence_64">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_76">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[p3_time]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["start time: "+Environment.newLine+System.DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")]</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[SM3.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_20">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_75">
                      <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;submails&quot;,SM3},{&quot;configurationFolder&quot;,configurationFolder},{&quot;logfile&quot;,logfile},{&quot;ocrKeysDictionary&quot;,ocrKeysDictionary},{&quot;tenantID&quot;,tenantID},{&quot;colemanAPI&quot;,colemanAPI},{&quot;userIdentifier&quot;,userIdentifier},{&quot;distributionType&quot;,distributionType},{&quot;projectPath&quot;,projectPath},{&quot;poFilterValues&quot;,poFilterValues},{&quot;poFilterCondition&quot;,poFilterCondition},{&quot;datalakeAPILogicalId&quot;,datalakeAPILogicalId},{&quot;chargeCode&quot;,chargeCode},{&quot;discountCode&quot;,discountCode},{&quot;authUser&quot;,authUser},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;extractNumericFromPO&quot;,extractNumericFromPO},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;poDiscountsHandlingConfig&quot;,poDiscountsHandlingConfig},{&quot;matchVendorItemCode&quot;,matchVendorItemCode},{&quot;extractFromWidgetDatalake&quot;,extractFromWidgetDatalake},{&quot;approvalRequired&quot;,approvalRequired},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;processExpenseInvoice&quot;,processExpenseInvoice},{&quot;miscValues&quot;,miscValues},{&quot;invoiceSource&quot;,invoiceSource},{&quot;enableMessageBoxes&quot;,enableMessageBoxes},{&quot;seqnumber&quot;,&quot;3&quot;},{&quot;DownloadTo&quot;,&quot;DownloadedFiles_&quot;},{&quot;FileRenameFormat&quot;,&quot;InforOS_C&quot;}}]" ContinueOnError="False" DisplayName="Invoke Workflow delay" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_34" OutputArguments="[emailsubjects3]" WorkflowFile="[projectPath+&quot;\preprocess.xaml&quot;]" />
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_77">
                        <Assign.To>
                          <OutArgument x:TypeArguments="scg:List(x:String)">[tsubjects3]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="scg:List(x:String)">[TryCast(emailsubjects3("emailsubjects"),List(Of String))]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                  </If.Then>
                </If>
              </Sequence>
              <Sequence DisplayName="Sequence--4" sap2010:WorkflowViewState.IdRef="Sequence_65">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_78">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[p4_time]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["start time: "+Environment.newLine+System.DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")]</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[SM4.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_21">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_74">
                      <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;submails&quot;,SM4},{&quot;configurationFolder&quot;,configurationFolder},{&quot;logfile&quot;,logfile},{&quot;ocrKeysDictionary&quot;,ocrKeysDictionary},{&quot;tenantID&quot;,tenantID},{&quot;colemanAPI&quot;,colemanAPI},{&quot;userIdentifier&quot;,userIdentifier},{&quot;distributionType&quot;,distributionType},{&quot;projectPath&quot;,projectPath},{&quot;poFilterValues&quot;,poFilterValues},{&quot;poFilterCondition&quot;,poFilterCondition},{&quot;datalakeAPILogicalId&quot;,datalakeAPILogicalId},{&quot;chargeCode&quot;,chargeCode},{&quot;discountCode&quot;,discountCode},{&quot;authUser&quot;,authUser},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;extractNumericFromPO&quot;,extractNumericFromPO},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;poDiscountsHandlingConfig&quot;,poDiscountsHandlingConfig},{&quot;matchVendorItemCode&quot;,matchVendorItemCode},{&quot;extractFromWidgetDatalake&quot;,extractFromWidgetDatalake},{&quot;approvalRequired&quot;,approvalRequired},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;processExpenseInvoice&quot;,processExpenseInvoice},{&quot;miscValues&quot;,miscValues},{&quot;invoiceSource&quot;,invoiceSource},{&quot;enableMessageBoxes&quot;,enableMessageBoxes},{&quot;seqnumber&quot;,&quot;4&quot;},{&quot;DownloadTo&quot;,&quot;DownloadedFiles_&quot;},{&quot;FileRenameFormat&quot;,&quot;InforOS_D&quot;}}]" ContinueOnError="False" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_35" OutputArguments="[emailsubjects4]" WorkflowFile="[projectPath+&quot;\preprocess.xaml&quot;]" />
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_79">
                        <Assign.To>
                          <OutArgument x:TypeArguments="scg:List(x:String)">[tsubjects4]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="scg:List(x:String)">[TryCast(emailsubjects4("emailsubjects"),List(Of String))]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                  </If.Then>
                </If>
              </Sequence>
              <Sequence DisplayName="Sequence--5" sap2010:WorkflowViewState.IdRef="Sequence_66">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_80">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[p5_time]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["start time: "+Environment.newLine+System.DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")]</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[SM5.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_22">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_73">
                      <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;submails&quot;,SM5},{&quot;configurationFolder&quot;,configurationFolder},{&quot;logfile&quot;,logfile},{&quot;ocrKeysDictionary&quot;,ocrKeysDictionary},{&quot;tenantID&quot;,tenantID},{&quot;colemanAPI&quot;,colemanAPI},{&quot;userIdentifier&quot;,userIdentifier},{&quot;distributionType&quot;,distributionType},{&quot;projectPath&quot;,projectPath},{&quot;poFilterValues&quot;,poFilterValues},{&quot;poFilterCondition&quot;,poFilterCondition},{&quot;datalakeAPILogicalId&quot;,datalakeAPILogicalId},{&quot;chargeCode&quot;,chargeCode},{&quot;discountCode&quot;,discountCode},{&quot;authUser&quot;,authUser},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;extractNumericFromPO&quot;,extractNumericFromPO},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;poDiscountsHandlingConfig&quot;,poDiscountsHandlingConfig},{&quot;matchVendorItemCode&quot;,matchVendorItemCode},{&quot;extractFromWidgetDatalake&quot;,extractFromWidgetDatalake},{&quot;approvalRequired&quot;,approvalRequired},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;processExpenseInvoice&quot;,processExpenseInvoice},{&quot;miscValues&quot;,miscValues},{&quot;invoiceSource&quot;,invoiceSource},{&quot;enableMessageBoxes&quot;,enableMessageBoxes},{&quot;seqnumber&quot;,&quot;5&quot;},{&quot;DownloadTo&quot;,&quot;DownloadedFiles_&quot;},{&quot;FileRenameFormat&quot;,&quot;InforOS_E&quot;}}]" ContinueOnError="False" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_36" OutputArguments="[emailsubjects5]" WorkflowFile="[projectPath+&quot;\preprocess.xaml&quot;]" />
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_81">
                        <Assign.To>
                          <OutArgument x:TypeArguments="scg:List(x:String)">[tsubjects5]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="scg:List(x:String)">[TryCast(emailsubjects5("emailsubjects"),List(Of String))]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                  </If.Then>
                </If>
              </Sequence>
            </Parallel>
            <ias:Append_Line ErrorCode="{x:Null}" x:Key="False" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_43" Line="No emails available to process" Source="[logFile]" />
          </Switch>
          <If Condition="[(&#xA;  emailsubjects1 IsNot Nothing AndAlso emailsubjects1(&quot;blnFailureExists&quot;) IsNot Nothing AndAlso CType(emailsubjects1(&quot;blnFailureExists&quot;), Boolean)&#xA;) OrElse (&#xA;  emailsubjects2 IsNot Nothing AndAlso emailsubjects2(&quot;blnFailureExists&quot;) IsNot Nothing AndAlso CType(emailsubjects2(&quot;blnFailureExists&quot;), Boolean)&#xA;) OrElse (&#xA;  emailsubjects3 IsNot Nothing AndAlso emailsubjects3(&quot;blnFailureExists&quot;) IsNot Nothing AndAlso CType(emailsubjects3(&quot;blnFailureExists&quot;), Boolean)&#xA;) OrElse (&#xA;  emailsubjects4 IsNot Nothing AndAlso emailsubjects4(&quot;blnFailureExists&quot;) IsNot Nothing AndAlso CType(emailsubjects4(&quot;blnFailureExists&quot;), Boolean)&#xA;) OrElse (&#xA;  emailsubjects5 IsNot Nothing AndAlso emailsubjects5(&quot;blnFailureExists&quot;) IsNot Nothing AndAlso CType(emailsubjects5(&quot;blnFailureExists&quot;), Boolean)&#xA;)]" DisplayName="Notify if any Failure emails" sap2010:WorkflowViewState.IdRef="If_34">
            <If.Then>
              <Sequence DisplayName="Failure Mails Notification Sequence" sap2010:WorkflowViewState.IdRef="Sequence_92">
                <Sequence.Variables>
                  <Variable x:TypeArguments="x:String" Name="notificationString" />
                  <Variable x:TypeArguments="njl:JToken" Name="notificationToken" />
                  <Variable x:TypeArguments="iru:ResponseObject" Name="notificationResponse" />
                  <Variable x:TypeArguments="x:String" Name="strMessageTitle" />
                  <Variable x:TypeArguments="x:String" Name="strUserIdentifier" />
                  <Variable x:TypeArguments="x:String" Name="strDistributionType" />
                  <Variable x:TypeArguments="x:String" Name="strMasterFailureData" />
                  <Variable x:TypeArguments="x:Int32" Name="int1" />
                </Sequence.Variables>
                <Assign DisplayName="Assign UserIdentifier" sap2010:WorkflowViewState.IdRef="Assign_110">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[strUserIdentifier]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[miscValues("userIdentifier").Tostring]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="Assign strDistributionType" sap2010:WorkflowViewState.IdRef="Assign_111">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[strDistributionType]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[miscValues("distributionType").Tostring]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_112">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[strMessageTitle]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["📣 M3 Invoice Processing Failure Summary " &amp; miscValues("StartTime").Tostring]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_113">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[strMasterFailureData]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String" xml:space="preserve">[String.Join(","c,
    {
        If(emailsubjects1 IsNot Nothing AndAlso emailsubjects1.ContainsKey("strJsonString"), CStr(emailsubjects1("strJsonString")), ""),
        If(emailsubjects2 IsNot Nothing AndAlso emailsubjects2.ContainsKey("strJsonString"), CStr(emailsubjects2("strJsonString")), ""),
        If(emailsubjects3 IsNot Nothing AndAlso emailsubjects3.ContainsKey("strJsonString"), CStr(emailsubjects3("strJsonString")), ""),
        If(emailsubjects4 IsNot Nothing AndAlso emailsubjects4.ContainsKey("strJsonString"), CStr(emailsubjects4("strJsonString")), ""),
        If(emailsubjects5 IsNot Nothing AndAlso emailsubjects5.ContainsKey("strJsonString"), CStr(emailsubjects5("strJsonString")), "")
    }.Where(Function(x) Not String.IsNullOrWhiteSpace(x)).ToArray())]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_2" Template="{}{ &#xA;  'message': '{{%strMessageTitle%}}',&#xA;  'category': 'RPA', &#xA;  'parameters': [&#xA;    {{%strMasterFailureData%}}&#xA;  ], &#xA;  'distribution': [&#xA;    {&#xA;      'identifier': '{{%userIdentifier%}}',&#xA;      'type': '{{%distributionType%}}',&#xA;      'sendMail': '{{%SendEmail%}}' &#xA;    }&#xA;  ]&#xA;}" Text="[notificationString]">
                  <ias:Template_Apply.Values>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="8">
                        <x:String>strMasterFailureData</x:String>
                        <x:String>userIdentifier</x:String>
                        <x:String>distributionType</x:String>
                        <x:String>SendEmail</x:String>
                        <x:String>strMessageTitle</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="8">
                        <x:String>strMasterFailureData</x:String>
                        <x:String>strUserIdentifier</x:String>
                        <x:String>strDistributionType</x:String>
                        <x:String>TRUE</x:String>
                        <x:String>strMessageTitle</x:String>
                      </scg:List>
                    </scg:List>
                  </ias:Template_Apply.Values>
                </ias:Template_Apply>
                <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_2" JTokenObject="[notificationToken]" JTokenString="[notificationString]" />
                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_3" PostData="[notificationToken.tostring]" Response="[notificationResponse]" ResponseCode="[int1]" Url="[tenantID+ &quot;IONSERVICES/process/application/v1/pulse/notification/create&quot;]">
                  <iai:IONAPIRequestWizard.Headers>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>Accept</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>application/json</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.Headers>
                  <iai:IONAPIRequestWizard.QueryParameters>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>logicalId</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>lid://infor.rpa.1</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.QueryParameters>
                </iai:IONAPIRequestWizard>
              </Sequence>
            </If.Then>
          </If>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_54" Line="[&quot;process-1&quot;+Environment.newline+p1_time+Environment.newline+String.Join(&quot;, &quot;,tsubjects1)+Environment.newline+&#xA;&quot;process-2&quot;+Environment.newline+p2_time+Environment.newline+String.Join(&quot;, &quot;,tsubjects2)+Environment.newline+&#xA;&quot;process-3&quot;+Environment.newline+p3_time+Environment.newline+String.Join(&quot;, &quot;,tsubjects3)+Environment.newline+&#xA;&quot;process-4&quot;+Environment.newline+p4_time+Environment.newline+String.Join(&quot;, &quot;,tsubjects4)+Environment.newline+&#xA;&quot;process-5&quot;+Environment.newline+p5_time+Environment.newline+String.Join(&quot;, &quot;,tsubjects5)]" Source="[logfile]" />
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_55" Line="------------------------------------------------" Source="[logfile]" />
          <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_1" IsValid="[reprocessExists]" Path="[configurationFolder + &quot;\Reprocess&quot;]" />
          <If Condition="[reprocessExists]" sap2010:WorkflowViewState.IdRef="If_29">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_83">
                <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="True" DirPath="[configurationFolder + &quot;\Reprocess&quot;]" DisplayName="Get Files in Directory" FileType="PDF" Files="[listInReprocessFiles]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_3" IncludeSubDir="True" />
                <If Condition="[listInReprocessFiles.count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_28">
                  <If.Then>
                    <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_10" Values="[listInReprocessFiles]">
                      <ActivityAction x:TypeArguments="x:String">
                        <ActivityAction.Argument>
                          <DelegateInArgument x:TypeArguments="x:String" Name="repitem" />
                        </ActivityAction.Argument>
                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_84">
                          <ias:File_Move ErrorCode="{x:Null}" targetName="{x:Null}" ContinueOnError="True" DisplayName="Move File" sap2010:WorkflowViewState.IdRef="File_Move_2" OutputFile="[movedFile]" OverwriteFile="False" Source="[repitem]" Target="[inProgressFolder]" />
                        </Sequence>
                      </ActivityAction>
                    </ForEach>
                  </If.Then>
                </If>
              </Sequence>
            </If.Then>
          </If>
          <Sequence DisplayName="OCR Sequence" sap2010:WorkflowViewState.IdRef="Sequence_69">
            <Sequence.Variables>
              <Variable x:TypeArguments="scg:List(x:String)" Name="listInProgressFiles" />
            </Sequence.Variables>
            <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="True" DirPath="[inProgressFolder]" DisplayName="Get Files in Directory" FileType="PDF" Files="[listInProgressFiles]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_1" IncludeSubDir="True" />
            <If Condition="[listInProgressFiles.count&gt;0]" DisplayName="Check if any files in In progress folder" sap2010:WorkflowViewState.IdRef="If_33">
              <If.Then>
                <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_6" Values="[listInProgressFiles]">
                  <ActivityAction x:TypeArguments="x:String">
                    <ActivityAction.Argument>
                      <DelegateInArgument x:TypeArguments="x:String" Name="item2" />
                    </ActivityAction.Argument>
                    <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_7">
                      <TryCatch.Try>
                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_71">
                          <Sequence.Variables>
                            <Variable x:TypeArguments="x:Int32" Name="processPoRespCode" />
                            <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="processPoRespDict" />
                          </Sequence.Variables>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_87">
                            <Assign.To>
                              <OutArgument x:TypeArguments="scg:List(x:String)">[approvalList]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="scg:List(x:String)">[new list(Of String)]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_51" Source="[logFile]">
                            <ias:Append_Line.Line>
                              <InArgument x:TypeArguments="x:String">
                                <Literal x:TypeArguments="x:String" Value="" />
                              </InArgument>
                            </ias:Append_Line.Line>
                          </ias:Append_Line>
                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_52" Line="[&quot;File Name : &quot;+item2.Substring(item2.LastIndexOf(&quot;\&quot;c)+1,(item2.Length()-item2.LastIndexOf(&quot;\&quot;c))-1)]" Source="[logFile]" />
                          <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;documentPath&quot;,item2},{&quot;logFile&quot;,logFile},{&quot;ocrKeysDictionary&quot;,ocrKeysDictionary},{&quot;configurationFolder&quot;,configurationFolder},{&quot;manualEntry&quot;,false},{&quot;emailSubject&quot;,&quot;NA&quot;},{&quot;emailReceivedTime&quot;,&quot;NA&quot;},{&quot;tenantID&quot;,tenantID},{&quot;colemanAPI&quot;,colemanAPI},{&quot;userIdentifier&quot;,userIdentifier},{&quot;distributionType&quot;,distributionType},{&quot;projectPath&quot;,projectPath},{&quot;enableMessageBoxes&quot;,enableMessageBoxes},{&quot;poFilterValues&quot;,poFilterValues},{&quot;poFilterCondition&quot;,poFilterCondition},{&quot;datalakeAPILogicalId&quot;,datalakeAPILogicalId},{&quot;chargeCode&quot;,chargeCode},{&quot;discountCode&quot;,discountCode},{&quot;authUser&quot;,authUser},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;extractNumericFromPO&quot;,extractNumericFromPO},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;poDiscountsHandlingConfig&quot;,poDiscountsHandlingConfig},{&quot;matchVendorItemCode&quot;,matchVendorItemCode},{&quot;extractFromWidgetDatalake&quot;,extractFromWidgetDatalake},{&quot;approvalRequired&quot;,approvalRequired},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;miscValues&quot;,miscValues},{&quot;processExpenseInvoice&quot;,processExpenseInvoice}}]" ContinueOnError="False" DisplayName="Get OCR from emails Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_39" OutputArguments="[processPoRespDict]" ResponseCode="[processPoRespCode]" WorkflowFile="[projectPath+&quot;\GetOCRValues.xaml&quot;]" />
                          <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_10">
                            <iad:CommentOut.Activities>
                              <If Condition="[processPoRespCode &lt;&gt; 200]" DisplayName="If Error while processing" sap2010:WorkflowViewState.IdRef="If_17">
                                <If.Then>
                                  <iae:MarkOutlookAsRead ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Mark Outlook Emails as Read/Unread" sap2010:WorkflowViewState.IdRef="MarkOutlookAsRead_3" Mail="[email]" MarkAsRead="False" />
                                </If.Then>
                              </If>
                            </iad:CommentOut.Activities>
                          </iad:CommentOut>
                          <If Condition="[miscValues(&quot;AutomateApproval&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_25">
                            <If.Then>
                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_79">
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_83">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="scg:List(x:String)">[approvalList]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="scg:List(x:String)">[CType(processPoRespDict("approvalList"), List(Of String))]</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <If Condition="[approvalList.count&gt;0]" sap2010:WorkflowViewState.IdRef="If_24">
                                  <If.Then>
                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_81">
                                      <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_5" MethodName="Add">
                                        <InvokeMethod.TargetObject>
                                          <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[approvalLists]</InArgument>
                                        </InvokeMethod.TargetObject>
                                        <InArgument x:TypeArguments="scg:List(x:String)">[approvalList]</InArgument>
                                      </InvokeMethod>
                                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_56" Line="[&quot;Updated the approval list with the invoice number &quot; + approvalList(1).ToString]" Source="[Logfile]" />
                                    </Sequence>
                                  </If.Then>
                                </If>
                              </Sequence>
                            </If.Then>
                          </If>
                        </Sequence>
                      </TryCatch.Try>
                      <TryCatch.Catches>
                        <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_6">
                          <ActivityAction x:TypeArguments="s:Exception">
                            <ActivityAction.Argument>
                              <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                            </ActivityAction.Argument>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_82">
                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_53" Line="[exception.message + &quot;  ocr exception occurred at file&quot; +path.getFileName(item2)]" Source="[logfile]" />
                              <ias:File_Move ErrorCode="{x:Null}" targetName="{x:Null}" ContinueOnError="True" DisplayName="Move File" sap2010:WorkflowViewState.IdRef="File_Move_1" OutputFile="[movedFile]" OverwriteFile="False" Source="[item2]" Target="[configurationFolder + &quot;\Reprocess&quot;]" />
                            </Sequence>
                          </ActivityAction>
                        </Catch>
                      </TryCatch.Catches>
                    </TryCatch>
                  </ActivityAction>
                </ForEach>
              </If.Then>
              <If.Else>
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="No files in inprogress  Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_60" Line="No files available to process in In Progress folder." Source="[logfile]" />
              </If.Else>
            </If>
            <If Condition="[miscValues(&quot;AutomateApproval&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_27">
              <If.Then>
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_80">
                  <If Condition="[approvalLists.count&gt;0]" sap2010:WorkflowViewState.IdRef="If_26">
                    <If.Then>
                      <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;approvalLists&quot;,approvalLists},{&quot;logFile&quot;,logFile},{&quot;tenantID&quot;,tenantID}}]" ContinueOnError="True" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_41" WorkflowFile="[projectPath+&quot;\approval.xaml&quot;]" />
                    </If.Then>
                  </If>
                </Sequence>
              </If.Then>
            </If>
          </Sequence>
        </Sequence>
      </Sequence>
    </TryCatch.Try>
    <TryCatch.Catches>
      <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_5">
        <ActivityAction x:TypeArguments="s:Exception">
          <ActivityAction.Argument>
            <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
          </ActivityAction.Argument>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_46" Line="[&quot;Below Error Occurred while executing Process Outlook Emails sequence.&quot;+environment.newline+exception.message]" Source="[logFile]" />
        </ActivityAction>
      </Catch>
    </TryCatch.Catches>
    <sads:DebugSymbol.Symbol>d1xDOlxVc2Vyc1xkbWl0dGFwYWxsaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYyXE91dGxvb2tfcGFyYWxsZWwueGFtbIMCYwOTBQ4CAQFqB4YFEgIBBo4FC44FqgICAQJrCXISAwHxAnMJehIDAe0CewmCARIDAegCgwEJgwGGAgMB4wKEAQmLARMDAd0CjAEJkwETAwHYApQBCYUFFAIBB44FmQGOBZQCAgEFjgWcAo4FpwICAQNwSHBmAwH0Am1JbVgDAfICeD54UwMB8AJ1P3VNAwHuAoABNIABWAMB6wJ9NX1HAwHpAoMBpAGDAbcBAwHmAoMBVYMBaQMB5AKEAYYBhAGZAQMB4QKJAQ2JAaYBAwHeAowBhgGMAbQBAwHcApEBDZEBtwEDAdkClgFElgFaAgEOnQFEnQFdAgENngFEngFdAgEMnwFEnwFdAgELoAFEoAFdAgEKoQFEoQFdAgEJqAE5qAE8AgEIsgELzQEcAwHXAs4BC+oBFAMBvALrAQvrAfQCAwG2AuwBC50CFgMBogKeAgufAxQDAcEBoAMLgQQQAwGLAYIEC4IEtwYDAYcBgwQLgwThAQMBgwGEBAuEBOoBAgF+hQQLmQQQAgFnmgQLhAUWAgEPiQGbAYkBowEDAd8CkQGqAZEBtAEDAdoCzgFpzgF6AwG9As8BDc8B9AIDAc0C0AEN6QEYAwG/AusBpAHrAd4CAwG7AusB5gLrAfECAwG5AusBWOsBbQMBtwLtAQ30ARYDAbIC9QEN/AEWAwGvAv0BDYQCFgMBrAKFAg2MAhYDAakCjQINlAIWAwGmApUCDZwCFgMBowKeAnieApQBAwHCAZ8CDZ0DGAMBxwGeAw2eA94BAwHDAaADGaADtwcDAYwBogMP/wMaAwGcAYIEmQGCBKEGAwGKAYIEqQaCBLQGAwGIAYMEmQGDBMsBAwGGAYME0wGDBN4BAwGEAYQEngGEBLEBAwGBAYQEtwGEBOcBAgF/hQQZhQQsAgFohwQPlwQaAgFqngQNngSOAgIBYp8EDfgEEgIBG/kEDYMFEgIBEM8BrgHPAb8BAwHWAs8BWM8BbQMB1ALPAd8CzwHxAgMB0gLPAcoBzwHfAQMB0ALPAecBzwH7AQMBzgLRAQ/VARQDAccC1gEP6AEaAwHAAvIBN/IBYQMBtQLvATjvATwDAbMC+gFC+gFvAwGxAvcBQ/cBSAMBsAKCAkKCAm4DAa4C/wFD/wFIAwGtAooCQooCbgMBqwKHAkOHAkgDAaoCkgJCkgJuAwGoAo8CQ48CSAMBpwKaAkKaAmUDAaUClwJDlwJIAwGkAqACD7gCGgMBkAK5Ag/RAhoDAf4B0gIP6gIaAwHsAesCD4MDGgMB2gGEAw+cAxoDAcgBngOoAZ4DyAEDAcYBngPQAZ4D2wEDAcQBrQMRtAMaAwG8AbUDEbwDGgMBtwG9AxHEAxoDAbIBxQMR0wMaAwGuAdQDEecDJgMBqgHoAxHoA+4BAwGlAekDEf4DKwMBnQGIBBGIBK8CAgF5iQQRlgQWAgFrngSoAZ4EvwECAWWeBFmeBG0CAWOfBBufBD0CARyhBBHzBBsCASL2BBH2BIICAgEe+QQb+QRrAgER+wQRgQUcAgET0QEd0QEzAwHIAtMBE9MBqwIDAcoC2AET3AEeAwHGAuQBF+QB8AEDAcECoQIRqAIaAwGeAqkCEbcCFgMBkQK6AhHBAhoDAYwCwgIR0AIWAwH/AdMCEdoCGgMB+gHbAhHpAhYDAe0B7AIR8wIaAwHoAfQCEYIDFgMB2wGFAxGMAxoDAdYBjQMRmwMWAwHJAbIDPLIDYwMBvwGvAz2vA1ADAb0BugM8ugNlAwG6AbcDPbcDUgMBuAHCAzzCA5ABAwG1Ab8DPb8DTgMBswHHAz3HA1MDAa8B1AOBBNQDlwQDAawB1AOvAdQD+wMDAasB6APVAegD6wEDAagB6AOyAegDxwEDAaYB6QOBAukDnwIDAaQB6QPPAukD1wIDAaIB6QOpAukDwQIDAaAB6QPcAukDsgMDAZ4BiATIAYgE4AECAXyIBF2IBI0BAgF6iQQfiQREAgFsiwQVlAQfAgFuoQSOAaEEpQECAWCmBBXxBCACASP2BLYB9gTsAQIBIfYE9AH2BP8BAgEf/AQTgAUYAgEU0wHZAdMBqAIDAcsC5AHFAeQB2gEDAcQC5AHiAeQB7QEDAcICpgI8pgKUAQMBoQKjAj2jAkYDAZ8CqQIfqQIzAwGSAqsCFbUCIAMBkwK/Ajy/ApQBAwGPArwCPbwCRgMBjQLCAh/CAjMDAYACxAIVzgIgAwGBAtgCPNgClAEDAf0B1QI91QJGAwH7AdsCH9sCMwMB7gHdAhXnAiADAe8B8QI88QKUAQMB6wHuAj3uAkYDAekB9AIf9AIzAwHcAfYCFYADIAMB3QGKAzyKA5QBAwHZAYcDPYcDRgMB1wGNAx+NAzMDAcoBjwMVmQMgAwHLAYsEkwGLBKsBAgF3kAQZkgQkAgFvqAQZ4gQkAgEw6gQd7QQoAgEk/AQh/AQ9AgEV/gQX/gSgAwIBF6wCF6wC2Q0DAZgCrQIXtAIgAwGUAsUCF8UC1w0DAYYCxgIXzQIgAwGCAt4CF94C3Q0DAfQB3wIX5gIgAwHwAfcCF/cC1w0DAeIB+AIX/wIgAwHeAZADF5AD1w0DAdABkQMXmAMgAwHMAZEEG5EEkgICAXCtBBu0BCQCAVy1BBu7BC0CAVi8BBu8BMQCAgFQvQQbvQTDDQIBSL4EG8YELAIBR8cEG+EEIAIBMesEH+sEoAICASzsBB/sBLACAgEl/gRo/gT8AQIBGv4E8wL+BJ0DAgEYrAKKDawCnA0DAZwCrAJNrAKNDAMBmwKsAqoNrALWDQMBmQKyAkyyAoYBAwGXAq8CTa8CWQMBlQLFAogNxQKaDQMBigLFAk3FAo0MAwGJAsUCqA3FAtQNAwGHAssCTMsChgEDAYUCyAJNyAJZAwGDAt4Cjg3eAqANAwH4Ad4CTd4CjQwDAfcB3gKuDd4C2g0DAfUB5AJM5AKGAQMB8wHhAk3hAlkDAfEB9wKIDfcCmg0DAeYB9wJN9wKNDAMB5QH3AqgN9wLUDQMB4wH9Akz9AoYBAwHhAfoCTfoCWQMB3wGQA4gNkAOaDQMB1AGQA02QA40MAwHTAZADqA2QA9QNAwHRAZYDTJYDhgEDAc8BkwNNkwNZAwHNAZEEvQGRBMoBAgF1kQT7AZEEjwICAXORBOgBkQTzAQIBcbIEULIEZQIBX68EUa8EXwIBXbgEIbgEUAIBW7UEqgG1BLUBAgFZvASoAbwErgICAVO8BLYCvATBAgIBUb0E7wy9BIQNAgFOvQTMDL0E4QwCAUy9BDm9BL0LAgFLvQSSDb0EwA0CAUnHBCnHBHkCATLJBB/fBCoCATTrBK0B6wSKAgIBL+sEkgLrBJ0CAgEt7ATBAewEzgECASrsBP0B7AStAgIBKOwE7AHsBPUBAgEmygQh0QQqAgFD0gQh3gQmAgE1zwRWzwSRAQIBRswEV8wEZQIBRNIEL9IESgIBNtQEJdwEMAIBONUEJ9oENgIBPtsEJ9sEpwICATnXBGbXBHUCAUHZBFrZBGgCAT/bBLQB2wSRAgIBPNsEmQLbBKQCAgE6</sads:DebugSymbol.Symbol>
  </TryCatch>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_84" sap:VirtualizedContainerService.HintSize="655.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_86" sap:VirtualizedContainerService.HintSize="655.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_82" sap:VirtualizedContainerService.HintSize="655.333333333333,62" />
      <sap2010:ViewStateData Id="Directory_GetFiles_2" sap:VirtualizedContainerService.HintSize="655.333333333333,22" />
      <sap2010:ViewStateData Id="File_Delete_1" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="ForEach`1_7" sap:VirtualizedContainerService.HintSize="655.333333333333,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Directory_Delete_1" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="ForEach`1_8" sap:VirtualizedContainerService.HintSize="655.333333333333,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeMethod_4" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="ForEach`1_5" sap:VirtualizedContainerService.HintSize="287,276">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_5" sap:VirtualizedContainerService.HintSize="476,138">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_60" sap:VirtualizedContainerService.HintSize="498,262">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="DoWhile_3" sap:VirtualizedContainerService.HintSize="524,424">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_11" sap:VirtualizedContainerService.HintSize="633.333333333333,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="GetOutlookEmails_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="MessageBox_13" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_14" sap:VirtualizedContainerService.HintSize="464,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_58" sap:VirtualizedContainerService.HintSize="200,100.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_41" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_4" sap:VirtualizedContainerService.HintSize="404.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_4" sap:VirtualizedContainerService.HintSize="464,338.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_59" sap:VirtualizedContainerService.HintSize="486,716.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_4" sap:VirtualizedContainerService.HintSize="633.333333333333,931.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_42" sap:VirtualizedContainerService.HintSize="633.333333333333,22" />
      <sap2010:ViewStateData Id="Assign_66" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_67" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_68" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_69" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_70" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_71" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_61" sap:VirtualizedContainerService.HintSize="633.333333333333,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_72" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_32" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_73" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_77" sap:VirtualizedContainerService.HintSize="264,246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_19" sap:VirtualizedContainerService.HintSize="464,394" />
      <sap2010:ViewStateData Id="Sequence_62" sap:VirtualizedContainerService.HintSize="200,618">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_74" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_33" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_75" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_76" sap:VirtualizedContainerService.HintSize="264,246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_18" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_63" sap:VirtualizedContainerService.HintSize="200,618">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_76" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_34" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_77" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_75" sap:VirtualizedContainerService.HintSize="264,246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_20" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_64" sap:VirtualizedContainerService.HintSize="200,618">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_78" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_35" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_79" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_74" sap:VirtualizedContainerService.HintSize="264,246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_21" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_65" sap:VirtualizedContainerService.HintSize="200,618">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_80" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_36" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_81" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_73" sap:VirtualizedContainerService.HintSize="264,246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_22" sap:VirtualizedContainerService.HintSize="464,394">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_66" sap:VirtualizedContainerService.HintSize="486,618">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Parallel_5" sap:VirtualizedContainerService.HintSize="1910,664">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_43" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Switch`1_6" sap:VirtualizedContainerService.HintSize="633.333333333333,168.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_110" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_111" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_112" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_113" sap:VirtualizedContainerService.HintSize="242,64" />
      <sap2010:ViewStateData Id="Template_Apply_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_92" sap:VirtualizedContainerService.HintSize="264,674">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_34" sap:VirtualizedContainerService.HintSize="633.333333333333,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_54" sap:VirtualizedContainerService.HintSize="633.333333333333,22" />
      <sap2010:ViewStateData Id="Append_Line_55" sap:VirtualizedContainerService.HintSize="633,22" />
      <sap2010:ViewStateData Id="Path_Validate_1" sap:VirtualizedContainerService.HintSize="633,22" />
      <sap2010:ViewStateData Id="Directory_GetFiles_3" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="File_Move_2" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_84" sap:VirtualizedContainerService.HintSize="222,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_10" sap:VirtualizedContainerService.HintSize="284.666666666667,298.666666666667" />
      <sap2010:ViewStateData Id="If_28" sap:VirtualizedContainerService.HintSize="464,447" />
      <sap2010:ViewStateData Id="Sequence_83" sap:VirtualizedContainerService.HintSize="486,633">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_29" sap:VirtualizedContainerService.HintSize="633,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Directory_GetFiles_1" sap:VirtualizedContainerService.HintSize="611,22" />
      <sap2010:ViewStateData Id="Assign_87" sap:VirtualizedContainerService.HintSize="611,60" />
      <sap2010:ViewStateData Id="Append_Line_51" sap:VirtualizedContainerService.HintSize="611,22" />
      <sap2010:ViewStateData Id="Append_Line_52" sap:VirtualizedContainerService.HintSize="611,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_39" sap:VirtualizedContainerService.HintSize="611,22" />
      <sap2010:ViewStateData Id="MarkOutlookAsRead_3" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_17" sap:VirtualizedContainerService.HintSize="464,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_10" sap:VirtualizedContainerService.HintSize="611,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_83" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="InvokeMethod_5" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="Append_Line_56" sap:VirtualizedContainerService.HintSize="218,22" />
      <sap2010:ViewStateData Id="Sequence_81" sap:VirtualizedContainerService.HintSize="240,314">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_24" sap:VirtualizedContainerService.HintSize="464,462" />
      <sap2010:ViewStateData Id="Sequence_79" sap:VirtualizedContainerService.HintSize="486,686">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_25" sap:VirtualizedContainerService.HintSize="611,834" />
      <sap2010:ViewStateData Id="Sequence_71" sap:VirtualizedContainerService.HintSize="633,1342">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_53" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="File_Move_1" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_82" sap:VirtualizedContainerService.HintSize="222,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Catch`1_6" sap:VirtualizedContainerService.HintSize="637,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_7" sap:VirtualizedContainerService.HintSize="651,1569">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_6" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_60" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_33" sap:VirtualizedContainerService.HintSize="611,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_41" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_26" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Sequence_80" sap:VirtualizedContainerService.HintSize="486,332">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_27" sap:VirtualizedContainerService.HintSize="611,480">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_69" sap:VirtualizedContainerService.HintSize="633,914">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_67" sap:VirtualizedContainerService.HintSize="655.333333333333,2840">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_72" sap:VirtualizedContainerService.HintSize="677.333333333333,3517.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_46" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_5" sap:VirtualizedContainerService.HintSize="1628,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_5" sap:VirtualizedContainerService.HintSize="1642,3834.66666666667" />
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="1682,3994.66666666667" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>