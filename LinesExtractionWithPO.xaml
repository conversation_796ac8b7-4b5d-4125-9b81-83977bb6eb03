﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="M3TotalTableRows" Type="OutArgument(scg:List(s:String[]))" />
    <x:Property Name="logfile" Type="InArgument(x:String)" />
    <x:Property Name="Status" Type="OutArgument(x:String)" />
    <x:Property Name="commentStatus" Type="OutArgument(x:String)" />
    <x:Property Name="req2" Type="InArgument(x:String)" />
    <x:Property Name="division" Type="OutArgument(x:String)" />
    <x:Property Name="VendorID" Type="OutArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="DNinM3exists" Type="OutArgument(x:Boolean)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_10">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:Int32" Name="StatusCode2" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj2" />
      <Variable x:TypeArguments="njl:JToken" Name="out2" />
      <Variable x:TypeArguments="x:String" Name="pono" />
    </Sequence.Variables>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_31">
      <Assign.To>
        <OutArgument x:TypeArguments="x:Boolean">[DNinM3exists]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:Boolean">False</InArgument>
      </Assign.Value>
    </Assign>
    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Export MI lines IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_1" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/EXPORTMI/Select?HDRS=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
      <iai:IONAPIRequestWizard.Headers>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>Accept</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>application/json</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.Headers>
      <iai:IONAPIRequestWizard.QueryParameters>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>QERY</x:String>
            <x:String>SEPC</x:String>
            <x:String>maxrecs</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>req2</x:String>
            <x:String>~</x:String>
            <x:String>1000</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.QueryParameters>
    </iai:IONAPIRequestWizard>
    <If Condition="[StatusCode2 = 200]" sap2010:WorkflowViewState.IdRef="If_6">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_8">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_2">
            <Assign.To>
              <OutArgument x:TypeArguments="njl:JToken">[out2]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_8">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(s:String[])">[new list(OF string())]</InArgument>
            </Assign.Value>
          </Assign>
          <If Condition="[out2(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_5">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_1">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_3">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">PO lines not received</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_4">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">PONOTRECEIVED</InArgument>
                  </Assign.Value>
                </Assign>
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_7">
                <Sequence.Variables>
                  <Variable x:TypeArguments="x:Int32" Name="x" />
                  <Variable x:TypeArguments="s:String[]" Name="M3Values" />
                  <Variable x:TypeArguments="x:String" Name="cono" />
                </Sequence.Variables>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_9">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:Int32">[x]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:Int32">1</InArgument>
                  </Assign.Value>
                </Assign>
                <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_1" Values="[out2(&quot;results&quot;)(0)(&quot;records&quot;)]">
                  <ActivityAction x:TypeArguments="njl:JToken">
                    <ActivityAction.Argument>
                      <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
                    </ActivityAction.Argument>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_6">
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_5">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_32">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Boolean">[DNinM3exists]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_10">
                          <Assign.To>
                            <OutArgument x:TypeArguments="s:String[]">[M3Values]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="s:String[]">[New String(13) {}]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_33">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[m3Values(3)]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[(Convert.toDecimal(item("REPL").ToString().split("~"C)(14).ToString()) - Convert.toDecimal(item("REPL").ToString().split("~"C)(17).ToString())).ToString()]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_1">
                          <iad:CommentOut.Activities>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_11">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[m3Values(3)]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(3).ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <If Condition="[m3Values(3).Contains(&quot;.&quot;)]" sap2010:WorkflowViewState.IdRef="If_9">
                              <If.Then>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_34">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[m3Values(3)]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[m3Values(3).SubString(0,m3Values(3).IndexOf("."))]</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </If.Then>
                            </If>
                          </iad:CommentOut.Activities>
                        </iad:CommentOut>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_36">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[vendorID]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(9).ToString()]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <If Condition="[CInt(m3Values(3)) &gt; 0]" sap2010:WorkflowViewState.IdRef="If_4">
                          <If.Then>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_4">
                              <Sequence.Variables>
                                <Variable x:TypeArguments="x:Decimal" Name="calQty" />
                                <Variable x:TypeArguments="x:Decimal" Name="calPrice" />
                                <Variable x:TypeArguments="x:String" Name="req3" />
                                <Variable x:TypeArguments="x:Double" Name="tot" />
                                <Variable x:TypeArguments="x:Double" Name="totqty" />
                              </Sequence.Variables>
                              <Assign DisplayName="pnli Assign" sap2010:WorkflowViewState.IdRef="Assign_12">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[m3Values(0)]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(0).ToString()]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_13">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[m3Values(1)]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(1).ToString()]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_1">
                                <TryCatch.Try>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_47">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[m3Values(2)]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[((Convert.ToDecimal(item("REPL").ToString().split("~"C)(13)) - Convert.ToDecimal(item("REPL").ToString().split("~"C)(18)))/(Convert.ToDecimal(item("REPL").ToString().split("~"C)(3)) - Convert.ToDecimal(item("REPL").ToString().split("~"C)(10)))).ToString]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </TryCatch.Try>
                                <TryCatch.Catches>
                                  <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_1">
                                    <ActivityAction x:TypeArguments="s:Exception">
                                      <ActivityAction.Argument>
                                        <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                      </ActivityAction.Argument>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_48">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[m3Values(2)]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(2)]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                    </ActivityAction>
                                  </Catch>
                                </TryCatch.Catches>
                              </TryCatch>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_15">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[m3Values(4)]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(4).ToString()]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_16">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[m3Values(5)]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(5).ToString()]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign DisplayName="pono Assign" sap2010:WorkflowViewState.IdRef="Assign_28">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[m3Values(6)]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(11).ToString()]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_3">
                                <iad:CommentOut.Activities>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_44">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[m3Values(3)]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[Math.Round((((Convert.ToDecimal(item("REPL").ToString().split("~"C)(13)) -Convert.ToDecimal(item("REPL").ToString().split("~"C)(18)))/(Convert.ToDecimal(item("REPL").ToString().split("~"C)(16))))/(Convert.ToDecimal(item("REPL").ToString().split("~"C)(2)))),4).ToString]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </iad:CommentOut.Activities>
                              </iad:CommentOut>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_45">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[m3Values(3)]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[(Convert.ToDecimal(item("REPL").ToString().split("~"C)(3)) -Convert.ToDecimal(item("REPL").ToString().split("~"C)(10))).ToString]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_17">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[m3Values(7)]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(9).ToString()]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_30">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[m3Values(8)]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(8).ToString()]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_18">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[m3Values(9)]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(6).ToString()]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_43">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[m3Values(10)]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[(Convert.toDecimal(item("REPL").ToString().split("~"C)(3).ToString()) - Convert.toDecimal(item("REPL").ToString().split("~"C)(10).ToString())).ToString()]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_66">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[m3Values(11)]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(19).ToString()]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_67">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[m3Values(12)]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(12).ToString()]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_5">
                                <iad:CommentOut.Activities>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_19">
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_64">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:Double">[tot]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:Double">0</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_59">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[req3]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">["F3RCAC,F3CDSE,F3CEID from FGRPCL where F3PUNO = " + m3Values(6) +" and F3SCOC != 0.000000 and F3PNLI = " + m3Values(0) + " and F3DIVI = " + m3Values(8)]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Export MI lines IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_3" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/EXPORTMI/Select?HDRS=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
                                      <iai:IONAPIRequestWizard.Headers>
                                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                            <x:String>Accept</x:String>
                                          </scg:List>
                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                            <x:String>application/json</x:String>
                                          </scg:List>
                                        </scg:List>
                                      </iai:IONAPIRequestWizard.Headers>
                                      <iai:IONAPIRequestWizard.QueryParameters>
                                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                            <x:String>QERY</x:String>
                                            <x:String>SEPC</x:String>
                                          </scg:List>
                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                            <x:String>req3</x:String>
                                            <x:String>~</x:String>
                                          </scg:List>
                                        </scg:List>
                                      </iai:IONAPIRequestWizard.QueryParameters>
                                    </iai:IONAPIRequestWizard>
                                    <If Condition="[StatusCode2 = 200]" sap2010:WorkflowViewState.IdRef="If_16">
                                      <If.Then>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_17">
                                          <Sequence.Variables>
                                            <Variable x:TypeArguments="njl:JToken" Name="out2" />
                                          </Sequence.Variables>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_60">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="njl:JToken">[out2]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <If Condition="[out2(&quot;results&quot;)(0)(&quot;records&quot;).ToString &lt;&gt; &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_15">
                                            <If.Then>
                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_16">
                                                <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_5" Values="[out2(&quot;results&quot;)(0)(&quot;records&quot;)]">
                                                  <ActivityAction x:TypeArguments="njl:JToken">
                                                    <ActivityAction.Argument>
                                                      <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
                                                    </ActivityAction.Argument>
                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_61">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="x:Double">[tot]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="x:Double">[tot + Convert.toDecimal(item("REPL").ToString.split("~"C)(0))]</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                  </ActivityAction>
                                                </ForEach>
                                              </Sequence>
                                            </If.Then>
                                          </If>
                                        </Sequence>
                                      </If.Then>
                                      <If.Else>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_18">
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_62">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching lines charges for the PO " + m3Values(6)+"."]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_63">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_5" Line="[commentStatus]" Source="[logfile]" />
                                        </Sequence>
                                      </If.Else>
                                    </If>
                                    <If Condition="[tot &lt;&gt; 0]" sap2010:WorkflowViewState.IdRef="If_17">
                                      <If.Then>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_65">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[m3Values(2)]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">[(convert.todecimal(m3values(2)) + (tot/convert.ToDecimal(m3Values(3)))).ToString]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                      </If.Then>
                                    </If>
                                  </Sequence>
                                </iad:CommentOut.Activities>
                              </iad:CommentOut>
                              <If Condition="False" sap2010:WorkflowViewState.IdRef="If_12">
                                <If.Then>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_11">
                                    <If Condition="[item(&quot;REPL&quot;).ToString().split(&quot;~&quot;C)(14).ToString() &lt;&gt; &quot;&quot; AND item(&quot;REPL&quot;).ToString().split(&quot;~&quot;C)(3).ToString() &lt;&gt; &quot;&quot;]" DisplayName="If F2RPQT/F2RPQA" sap2010:WorkflowViewState.IdRef="If_10">
                                      <If.Then>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_37">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:Decimal">[calQty]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:Decimal">[(convert.toDecimal(item("REPL").ToString().split("~"C)(14))/convert.toDecimal(m3Values(3)))]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                      </If.Then>
                                      <If.Else>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_38">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:Decimal">[calQty]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:Decimal">[1]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                      </If.Else>
                                    </If>
                                    <If Condition="[item(&quot;REPL&quot;).ToString().split(&quot;~&quot;C)(15).ToString() &lt;&gt; &quot;&quot; AND item(&quot;REPL&quot;).ToString().split(&quot;~&quot;C)(16).ToString() &lt;&gt; &quot;&quot;]" DisplayName="If F2SCOP/F2SERA" sap2010:WorkflowViewState.IdRef="If_11">
                                      <If.Then>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_39">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:Decimal">[calPrice]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:Decimal">[(convert.toDecimal(item("REPL").ToString().split("~"C)(15))/convert.toDecimal(item("REPL").ToString().split("~"C)(16)))]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                      </If.Then>
                                      <If.Else>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_40">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:Decimal">[calPrice]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:Decimal">[1]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                      </If.Else>
                                    </If>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_41">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[m3Values(3)]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">[Math.Round(calPrice * calqty,4).ToString]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                  </Sequence>
                                </If.Then>
                              </If>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_35">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[vendorID]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[m3Values(7)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_3" MethodName="Add">
                                <InvokeMethod.TargetObject>
                                  <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</InArgument>
                                </InvokeMethod.TargetObject>
                                <InArgument x:TypeArguments="s:String[]">[m3Values]</InArgument>
                              </InvokeMethod>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_23">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:Int32">[x]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:Int32">[x+1]</InArgument>
                                </Assign.Value>
                              </Assign>
                            </Sequence>
                          </If.Then>
                        </If>
                      </Sequence>
                    </Sequence>
                  </ActivityAction>
                </ForEach>
              </Sequence>
            </If.Else>
          </If>
        </Sequence>
      </If.Then>
      <If.Else>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_9">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_24">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching lines for the PO " + pono +"."]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_25">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
            </Assign.Value>
          </Assign>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_3" Line="[commentStatus]" Source="[logfile]" />
        </Sequence>
      </If.Else>
    </If>
    <sads:DebugSymbol.Symbol>d2FDOlxVc2Vyc1xkbWl0dGFwYWxsaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYyXExpbmVzRXh0cmFjdGlvbldpdGhQTy54YW1siAFNA+cEDgIBAVQFWw4DAbIBXAV1HwMBqwF2BeUECgIBAlkxWTYDAbUBVjJWQAMBswFcmgJcpgIDAbABXMYCXPkDAwGuAVyyAlzBAgMBrAF2E3YoAgEDeAnOBBQCARTRBAnjBBQCAQV5C4ABFAMBpwGBAQuIARQDAaMBiQELzQQQAgEV0gQL2QQUAgEP2gQL4QQUAgEL4gQL4gS+AQIBBn44flsDAaoBezl7PwMBqAGGAUKGAVkDAaYBgwFDgwFVAwGkAYkBGYkBaAIBFosBD5wBGgMBmgGfAQ/LBBoCARfXBDbXBH8CARLUBDfUBEYCARDfBDbfBEECAQ7cBDfcBD8CAQziBJcB4gSoAQIBCeIEsAHiBLsBAgEHjAERkwEaAwGfAZQBEZsBGgMBmwGlARGsARoDAZYBrQERygQbAgEYkQE8kQFRAwGiAY4BPY4BTAMBoAGZATyZAUkDAZ4BlgE9lgFFAwGcAaoBO6oBPAMBmQGnATynAT8DAZcBrQGQAa0BxQEDAZUBsgEVyAQgAgEZswEXxwQiAgEatAEZuwEiAwGRAbwBGcMBIgMBjQHEARnLASIDAYkBzAEZ4wEqAwGIAeQBGesBIgMBhAHsARnGBB4CARu5AUW5AUkDAZQBtgFGtgFUAwGSAcEBRsEBWQMBkAG+AUe+AVEDAY4ByQFEyQHgAQMBjAHGAUXGAVIDAYoB6QFE6QF3AwGHAeYBReYBTwMBhQHsASfsAUMCARzuAR3EBCgCAR72AR/9ASgDAYAB/gEfhQIoAgF8hgIfogIqAgFzowIfqgIoAgFvqwIfsgIoAgFrswIfugIoAgFnuwIfxgIwAgFmxwIfzgIoAgFizwIf1gIoAgFe1wIf3gIoAgFa3wIf5gIoAgFW5wIf7gIoAgFS7wIf9gIoAgFO9wIf/gIoAgFK/wIf8wMwAgFJ9AMfrQQkAgEurgQftQQoAgEptgQfuwQuAgEkvAQfwwQoAgEf+wFK+wF9AwGDAfgBS/gBWAMBgQGDAkqDAn0CAX+AAkuAAlgCAX2IAiOPAiwCAXiXAieeAjACAXSoAkqoAn0CAXKlAkulAlgCAXCwAkqwAn0CAW6tAkutAlgCAWy4Akq4An4CAWq1Aku1AlgCAWjMAkrMAswBAgFlyQJLyQJYAgFj1AJK1AJ9AgFh0QJL0QJYAgFf3AJK3AJ9AgFd2QJL2QJYAgFb5AJK5AJ9AgFZ4QJL4QJYAgFX7AJK7ALlAQIBVekCS+kCWQIBU/QCSvQCfgIBUfECS/ECWQIBT/wCSvwCfgIBTfkCS/kCWQIBS/QDLfQDNAIBL/YDI6sELgIBMLMESrMEVwIBLLAES7AEVQIBKrgEVrgEaAIBJ7oESroEVAIBJcEEScEETgIBIr4ESr4ETQIBII0CTo0CzQICAXuKAk+KAlwCAXmcAlKcAnoCAXeZAlOZAmACAXX3AyWMBCoCAT+NBCWiBCoCATWjBCWqBC4CATH3AzP3A/MBAgFA+QMpgAQyAgFFgwQpigQyAgFBjQQzjQT0AQIBNo8EKZYEMgIBO5kEKaAEMgIBN6gEUKgEegIBNKUEUaUEXgIBMv4DVf4DsgECAUj7A1b7A14CAUaIBFWIBFgCAUSFBFaFBF4CAUKUBFWUBM4BAgE+kQRWkQRgAgE8ngRVngRYAgE6mwRWmwRgAgE4</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_31" sap:VirtualizedContainerService.HintSize="1261,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_1" sap:VirtualizedContainerService.HintSize="1261,22" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="950,60" />
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="950,60" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="639,60" />
      <sap2010:ViewStateData Id="Assign_32" sap:VirtualizedContainerService.HintSize="565,60" />
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="565,60" />
      <sap2010:ViewStateData Id="Assign_33" sap:VirtualizedContainerService.HintSize="565,60" />
      <sap2010:ViewStateData Id="Assign_11" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_34" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_9" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="CommentOut_1" sap:VirtualizedContainerService.HintSize="565,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_36" sap:VirtualizedContainerService.HintSize="565,60" />
      <sap2010:ViewStateData Id="Assign_12" sap:VirtualizedContainerService.HintSize="418,60" />
      <sap2010:ViewStateData Id="Assign_13" sap:VirtualizedContainerService.HintSize="418,60" />
      <sap2010:ViewStateData Id="Assign_47" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_48" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Catch`1_1" sap:VirtualizedContainerService.HintSize="404,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_1" sap:VirtualizedContainerService.HintSize="418,287" />
      <sap2010:ViewStateData Id="Assign_15" sap:VirtualizedContainerService.HintSize="418,60" />
      <sap2010:ViewStateData Id="Assign_16" sap:VirtualizedContainerService.HintSize="418,60" />
      <sap2010:ViewStateData Id="Assign_28" sap:VirtualizedContainerService.HintSize="418,60" />
      <sap2010:ViewStateData Id="Assign_44" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="CommentOut_3" sap:VirtualizedContainerService.HintSize="418,156" />
      <sap2010:ViewStateData Id="Assign_45" sap:VirtualizedContainerService.HintSize="418,60" />
      <sap2010:ViewStateData Id="Assign_17" sap:VirtualizedContainerService.HintSize="418,60" />
      <sap2010:ViewStateData Id="Assign_30" sap:VirtualizedContainerService.HintSize="418,60" />
      <sap2010:ViewStateData Id="Assign_18" sap:VirtualizedContainerService.HintSize="418,60" />
      <sap2010:ViewStateData Id="Assign_43" sap:VirtualizedContainerService.HintSize="418,60" />
      <sap2010:ViewStateData Id="Assign_66" sap:VirtualizedContainerService.HintSize="418,60" />
      <sap2010:ViewStateData Id="Assign_67" sap:VirtualizedContainerService.HintSize="418,60" />
      <sap2010:ViewStateData Id="Assign_64" sap:VirtualizedContainerService.HintSize="776,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_59" sap:VirtualizedContainerService.HintSize="776,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_3" sap:VirtualizedContainerService.HintSize="776,22" />
      <sap2010:ViewStateData Id="Assign_60" sap:VirtualizedContainerService.HintSize="464,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_61" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="ForEach`1_5" sap:VirtualizedContainerService.HintSize="286,212.666666666667" />
      <sap2010:ViewStateData Id="Sequence_16" sap:VirtualizedContainerService.HintSize="308,336.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_15" sap:VirtualizedContainerService.HintSize="464,488.666666666667" />
      <sap2010:ViewStateData Id="Sequence_17" sap:VirtualizedContainerService.HintSize="486,714">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_62" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_63" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_5" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_18" sap:VirtualizedContainerService.HintSize="264,348.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_16" sap:VirtualizedContainerService.HintSize="776,866" />
      <sap2010:ViewStateData Id="Assign_65" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="If_17" sap:VirtualizedContainerService.HintSize="776,213.333333333333" />
      <sap2010:ViewStateData Id="Sequence_19" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_5" sap:VirtualizedContainerService.HintSize="418,147" />
      <sap2010:ViewStateData Id="Assign_37" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_38" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_10" sap:VirtualizedContainerService.HintSize="510,216" />
      <sap2010:ViewStateData Id="Assign_39" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_40" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_11" sap:VirtualizedContainerService.HintSize="510,216" />
      <sap2010:ViewStateData Id="Assign_41" sap:VirtualizedContainerService.HintSize="510,62" />
      <sap2010:ViewStateData Id="Sequence_11" sap:VirtualizedContainerService.HintSize="532,698">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_12" sap:VirtualizedContainerService.HintSize="418,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_35" sap:VirtualizedContainerService.HintSize="418,60" />
      <sap2010:ViewStateData Id="InvokeMethod_3" sap:VirtualizedContainerService.HintSize="418,128" />
      <sap2010:ViewStateData Id="Assign_23" sap:VirtualizedContainerService.HintSize="418,60" />
      <sap2010:ViewStateData Id="Sequence_4" sap:VirtualizedContainerService.HintSize="440,2453">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="565,2601" />
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="587,3223">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_6" sap:VirtualizedContainerService.HintSize="609,3347">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_1" sap:VirtualizedContainerService.HintSize="639,3495">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="661,3719">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_5" sap:VirtualizedContainerService.HintSize="950,3867" />
      <sap2010:ViewStateData Id="Sequence_8" sap:VirtualizedContainerService.HintSize="972,4191">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_24" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_25" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_9" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_6" sap:VirtualizedContainerService.HintSize="1261,4339" />
      <sap2010:ViewStateData Id="Sequence_10" sap:VirtualizedContainerService.HintSize="1283,4625">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="1323,4865" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>