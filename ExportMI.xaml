﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="logfile" Type="InArgument(x:String)" />
    <x:Property Name="vendorId" Type="OutArgument(x:String)" />
    <x:Property Name="company" Type="OutArgument(x:String)" />
    <x:Property Name="division" Type="OutArgument(x:String)" />
    <x:Property Name="result" Type="OutArgument(scg:List(x:String))" />
    <x:Property Name="Status" Type="OutArgument(x:String)" />
    <x:Property Name="commentStatus" Type="OutArgument(x:String)" />
    <x:Property Name="pono" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>System.Windows.Controls</x:String>
      <x:String>System.Windows.Documents</x:String>
      <x:String>System.Windows.Shapes</x:String>
      <x:String>System.Windows.Shell</x:String>
      <x:String>System.Windows.Navigation</x:String>
      <x:String>System.Windows.Data</x:String>
      <x:String>System.Windows</x:String>
      <x:String>System.Windows.Controls.Primitives</x:String>
      <x:String>System.Windows.Media.Animation</x:String>
      <x:String>System.Windows.Input</x:String>
      <x:String>System.Windows.Media</x:String>
      <x:String>System.Diagnostics</x:String>
      <x:String>System.Windows.Automation</x:String>
      <x:String>System.Windows.Media.TextFormatting</x:String>
      <x:String>System.Windows.Ink</x:String>
      <x:String>System.Windows.Media.Effects</x:String>
      <x:String>System.Windows.Media.Imaging</x:String>
      <x:String>System.Windows.Media.Media3D</x:String>
      <x:String>System.Windows.Controls.Ribbon</x:String>
      <x:String>System.Windows.Forms.Integration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Windows.Controls.Ribbon</AssemblyReference>
      <AssemblyReference>WindowsFormsIntegration</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_3">
    <Sequence.Variables>
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj1" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode1" />
      <Variable x:TypeArguments="x:String" Name="req1" />
      <Variable x:TypeArguments="x:String" Name="cucd" />
      <Variable x:TypeArguments="x:String" Name="pyme" />
      <Variable x:TypeArguments="x:String" Name="tepy" />
    </Sequence.Variables>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_12">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(x:String)">[result]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)()]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_2">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[req1]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">["IACONO,IADIVI,IASUNO,IACUCD,IATEPY,IAPYME from MPHEAD where IAPUNO = '" + pono + "'"]</InArgument>
      </Assign.Value>
    </Assign>
    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="ExportMI for comp, div IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_1" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/EXPORTMI/Select?SEPC=~&amp;HDRS=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
      <iai:IONAPIRequestWizard.Headers>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>Accept</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>application/json</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.Headers>
      <iai:IONAPIRequestWizard.QueryParameters>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>QERY</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>req1</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.QueryParameters>
    </iai:IONAPIRequestWizard>
    <If Condition="[StatusCode1=200]" sap2010:WorkflowViewState.IdRef="If_4">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_2">
          <Sequence.Variables>
            <Variable x:TypeArguments="njl:JToken" Name="out1" />
          </Sequence.Variables>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_3">
            <Assign.To>
              <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj1.ReadAsText)]</InArgument>
            </Assign.Value>
          </Assign>
          <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_3">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_5">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_15">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">NEEDSVERIFICATION</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_16">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["PO number is not available"]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_4" Line="[commentStatus]" Source="[logFile]" />
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_1">
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="Extracted the company and division." Source="[logfile]" />
                <Assign sap2010:WorkflowViewState.IdRef="Assign_4">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[division]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(1).ToString()]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_5">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[company]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(0).ToString()]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_6">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(2).ToString()]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_2" Line="[&quot;VendorID : &quot; + vendorid]" Source="[logFile]" />
                <Assign sap2010:WorkflowViewState.IdRef="Assign_9">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[tepy]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(4).ToString()]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_10">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[pyme]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(5).ToString()]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_11">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[cucd]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(3).ToString()]</InArgument>
                  </Assign.Value>
                </Assign>
                <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_2" MethodName="Add">
                  <InvokeMethod.TargetObject>
                    <InArgument x:TypeArguments="scg:List(x:String)">[result]</InArgument>
                  </InvokeMethod.TargetObject>
                  <InArgument x:TypeArguments="x:String">[company]</InArgument>
                </InvokeMethod>
                <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_3" MethodName="Add">
                  <InvokeMethod.TargetObject>
                    <InArgument x:TypeArguments="scg:List(x:String)">[result]</InArgument>
                  </InvokeMethod.TargetObject>
                  <InArgument x:TypeArguments="x:String">[division]</InArgument>
                </InvokeMethod>
                <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_4" MethodName="Add">
                  <InvokeMethod.TargetObject>
                    <InArgument x:TypeArguments="scg:List(x:String)">[result]</InArgument>
                  </InvokeMethod.TargetObject>
                  <InArgument x:TypeArguments="x:String">[vendorId]</InArgument>
                </InvokeMethod>
                <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_5" MethodName="Add">
                  <InvokeMethod.TargetObject>
                    <InArgument x:TypeArguments="scg:List(x:String)">[result]</InArgument>
                  </InvokeMethod.TargetObject>
                  <InArgument x:TypeArguments="x:String">[cucd]</InArgument>
                </InvokeMethod>
                <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_6" MethodName="Add">
                  <InvokeMethod.TargetObject>
                    <InArgument x:TypeArguments="scg:List(x:String)">[result]</InArgument>
                  </InvokeMethod.TargetObject>
                  <InArgument x:TypeArguments="x:String">[tepy]</InArgument>
                </InvokeMethod>
                <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_7" MethodName="Add">
                  <InvokeMethod.TargetObject>
                    <InArgument x:TypeArguments="scg:List(x:String)">[result]</InArgument>
                  </InvokeMethod.TargetObject>
                  <InArgument x:TypeArguments="x:String">[pyme]</InArgument>
                </InvokeMethod>
              </Sequence>
            </If.Else>
          </If>
        </Sequence>
      </If.Then>
      <If.Else>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_4">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_13">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching company number and division for the PO " + pono +"."]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_14">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
            </Assign.Value>
          </Assign>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_3" Line="[commentStatus]" Source="[logfile]" />
        </Sequence>
      </If.Else>
    </If>
    <sads:DebugSymbol.Symbol>d1RDOlxVc2Vyc1xkbWl0dGFwYWxsaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYyXEV4cG9ydE1JLnhhbWxSYAOnAg4CAQFpBXAOAgF1cQV4DgIBcHkFjgEfAgFpjwEFpQIKAgECbjpuUQIBeGs7a0MCAXZ2MHaHAQIBc3MxczcCAXF5oQJ5rQICAW55zQJ5iwQCAWx5uQJ5yAICAWqPAROPASYCAQORAQmOAhQCARSRAgmjAhQCAQWVAQucARQCAWWdAQuNAhACARWSAguZAhQCAQ+aAguhAhQCAQuiAguiAr4BAgEGmgE4mgFbAgFolwE5lwE/AgFmnQEZnQFoAgEWnwEPsQEaAgFXtAEPiwIaAgEXlwI2lwKVAQIBEpQCN5QCRgIBEJ8CNp8CQQIBDpwCN5wCPwIBDKIClwGiAqgBAgEJogKwAaICuwECAQegARGnARoCAWGoARGvARoCAV2wARGwAcQBAgFYtQERtQHYAQIBU7YBEb0BGgIBT74BEcUBGgIBS8YBEc0BGgIBR84BEc4B2QECAULPARHWARoCAT7XARHeARoCATrfARHmARoCATbnARHsASACATHtARHyASACASzzARH4ASACASf5ARH+ASACASL/ARGEAiACAR2FAhGKAiACARilATylAU0CAWSiAT2iAUUCAWKtATytAVoCAWCqAT2qAUwCAV6wAZ0BsAGuAQIBW7ABtgGwAcEBAgFZtQGdAbUBwgECAVa1AcoBtQHVAQIBVLsBPLsBiwECAVK4AT24AUcCAVDDATzDAYsBAgFOwAE9wAFGAgFMywE8ywGLAQIBSsgBPcgBRwIBSM4BnQHOAcMBAgFFzgHLAc4B1gECAUPUATzUAYsBAgFB0QE90QFDAgE/3AE83AGLAQIBPdkBPdkBQwIBO+QBPOQBiwECATnhAT3hAUMCATfpAUbpAU4CATTrATrrAUMCATLvAUbvAU4CAS/xATrxAUQCAS31AUb1AU4CASr3ATr3AUQCASj7AUb7AU4CASX9ATr9AUACASOBAkaBAk4CASCDAjqDAkACAR6HAkaHAk4CARuJAjqJAkACARk=</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_12" sap:VirtualizedContainerService.HintSize="866,62" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="866,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_1" sap:VirtualizedContainerService.HintSize="866,22" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_15" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_16" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_11" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_2" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_3" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_4" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_5" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_6" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_7" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="264,1864">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="554,2018" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="576,2244">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_13" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_14" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_4" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="866,2398" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="888,2788">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="928,2908" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>