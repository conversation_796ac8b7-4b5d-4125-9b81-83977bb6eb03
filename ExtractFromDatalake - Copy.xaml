﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="maxNotReceivedCount" Type="InArgument(x:Int32)" />
    <x:Property Name="configurationFolder" Type="InArgument(x:String)" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="colemanAPI" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="distributionType" Type="InArgument(x:String)" />
    <x:Property Name="userIdentifier" Type="InArgument(x:String)" />
    <x:Property Name="imsAPIUrl" Type="InArgument(x:String)" />
    <x:Property Name="discountCode" Type="InArgument(x:String)" />
    <x:Property Name="chargeCode" Type="InArgument(x:String)" />
    <x:Property Name="datalakeAPILogicalId" Type="InArgument(x:String)" />
    <x:Property Name="authUser" Type="InArgument(x:String)" />
    <x:Property Name="extractFromWidgetDatalake" Type="InArgument(x:Boolean)" />
    <x:Property Name="approvalRequired" Type="InArgument(x:Boolean)" />
    <x:Property Name="approvalWorkflow" Type="InArgument(x:String)" />
    <x:Property Name="checkAmountBussinessRule" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="matchVendorItemCode" Type="InArgument(x:Boolean)" />
    <x:Property Name="Attribute_List" Type="InArgument(scg:List(s:String[]))" />
    <x:Property Name="enableMessageBoxes" Type="InArgument(x:Boolean)" />
    <x:Property Name="approvalList" Type="InOutArgument(scg:List(x:String))" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>System.Linq</x:String>
      <x:String>Microsoft.VisualBasic</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
      <AssemblyReference>System.Collections.Immutable</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="ExtractFromDatalakeSequence" sap2010:WorkflowViewState.IdRef="Sequence_3">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="query" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="resp1" />
      <Variable x:TypeArguments="x:Int32" Name="status1" />
      <Variable x:TypeArguments="x:String" Name="jobid" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="resp2" />
      <Variable x:TypeArguments="x:Int32" Name="status2" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="resp3" />
      <Variable x:TypeArguments="x:Int32" Name="status3" />
      <Variable x:TypeArguments="njl:JToken" Name="out1" />
      <Variable x:TypeArguments="s:String[]" Name="ocrValues" />
      <Variable x:TypeArguments="x:Boolean" Name="fileExist" />
      <Variable x:TypeArguments="x:String" Name="vendorId" />
      <Variable x:TypeArguments="scg:List(scg:List(x:String))" Name="approvalLists" />
      <Variable x:TypeArguments="x:String" Name="APResp1" />
    </Sequence.Variables>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_69">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(scg:List(x:String))">[approvalLists]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[New List(Of List(Of String))]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_73">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(x:String)">[approvalList]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(x:String)">[new list(Of String)]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_1">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[query]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">["SELECT H.RPA_Process_ID, H.Status, H.Comments, H.Last_Run_Time, H.Process_Type, H.Name, H.ERP, H.Failure_Count, H.Variation_ID AS Header_Variation_ID, H.Updated_By, H.Additional1, H.Additional2, COALESCE(MAX(CASE WHEN A.AttributeName = 'Additional_Attribute_3' THEN A.AttributeValue END), '') AS Additional_Attribute_3, COALESCE(MAX(CASE WHEN A.AttributeName = 'Additional_Attribute_2' THEN A.AttributeValue END), '') AS Additional_Attribute_2, COALESCE(MAX(CASE WHEN A.AttributeName = 'Additional_Attribute_1' THEN A.AttributeValue END), '') AS Additional_Attribute_1, COALESCE(MAX(CASE WHEN A.AttributeName = 'Comments' THEN A.AttributeValue END), '') AS Attribute_Comments, COALESCE(MAX(CASE WHEN A.AttributeName = 'Vendor_Name' THEN A.AttributeValue END), '') AS Vendor_Name, COALESCE(MAX(CASE WHEN A.AttributeName = 'Vendor_ID' THEN A.AttributeValue END), '') AS Vendor_ID, COALESCE(MAX(CASE WHEN A.AttributeName = 'Company' THEN A.AttributeValue END), '') AS Company, COALESCE(MAX(CASE WHEN A.AttributeName = 'DeliveryNote_Number' THEN A.AttributeValue END), '') AS DeliveryNote_Number, COALESCE(MAX(CASE WHEN A.AttributeName = 'Discount_Amount' THEN A.AttributeValue END), '0') AS Discount_Amount, COALESCE(MAX(CASE WHEN A.AttributeName = 'Shipping_Charges' THEN A.AttributeValue END), '0') AS Shipping_Charges, COALESCE(MAX(CASE WHEN A.AttributeName = 'Tax_Amount' THEN A.AttributeValue END), '0') AS Tax_Amount, COALESCE(MAX(CASE WHEN A.AttributeName = 'Total_Amount' THEN A.AttributeValue END), '0') AS Total_Amount, COALESCE(MAX(CASE WHEN A.AttributeName = 'Subtotal_Amount' THEN A.AttributeValue END), '0') AS Subtotal_Amount, COALESCE(MAX(CASE WHEN A.AttributeName = 'Invoice_Date' THEN A.AttributeValue END), '') AS Invoice_Date, COALESCE(MAX(CASE WHEN A.AttributeName = 'PO_Number' THEN A.AttributeValue END), '') AS PO_Number, COALESCE(MAX(CASE WHEN A.AttributeName = 'Invoice_Number' THEN A.AttributeValue END), '') AS Invoice_Number, COALESCE(MAX(CASE WHEN A.AttributeName = 'Invoice_Type' THEN A.AttributeValue END), '') AS Invoice_Type, COALESCE(MAX(CASE WHEN A.AttributeName = 'File_Name' THEN A.AttributeValue END), '') AS File_Name, COALESCE(MAX(CASE WHEN A.AttributeName = 'GLCode' THEN A.AttributeValue END), '') AS GLCode, COALESCE(MAX(CASE WHEN A.AttributeName = 'Division' THEN A.AttributeValue END), '') AS Division,COALESCE(MAX(CASE WHEN A.AttributeName = 'APResp' THEN A.AttributeValue END), '') AS APResp, COALESCE(MAX(CASE WHEN A.AttributeName = 'Email_Received_Time' THEN A.AttributeValue END), '') AS Email_Received_Time, COALESCE(MAX(CASE WHEN A.AttributeName = 'Email_Subject' THEN A.AttributeValue END), '') AS Email_Subject, COALESCE(MAX(L.Data), '') AS Line_Data FROM RPA_Process_Header H JOIN RPA_Process_Attributes A ON H.RPA_Process_ID = A.Process_ID LEFT JOIN RPA_Process_Lines L ON H.RPA_Process_ID = L.RPA_Process_ID WHERE H.ERP = '"+miscValues("ERP").ToString+"' AND NOT H.Status = 'ARCHIVE' AND H.process_type = 'INVOICE PROCESSING' AND ((H.Status = 'MISSINGINFORMATION' AND H.Updated_By = 'Y') OR (H.Status = 'NEEDSVERIFICATION' AND H.Updated_By = 'Y') OR (H.Status = 'PONOTRECEIVED')) GROUP BY H.RPA_Process_ID, H.Status, H.Comments, H.Last_Run_Time, H.Process_Type, H.Name, H.ERP, H.Failure_Count, H.Variation_ID, H.Updated_By, H.Additional1, H.Additional2 ORDER BY H.Last_Run_Time DESC"]</InArgument>
      </Assign.Value>
    </Assign>
    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" ResponseCode="{x:Null}" ContentType="text_plain" ContinueOnError="True" DisplayName="Job query IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_6" PostData="[query]" Response="[resp1]" StatusCode="[status1]" Url="[tenantID + &quot;DATAFABRIC/compass/v2/jobs/?records=0&quot;]">
      <iai:IONAPIRequestWizard.QueryParameters>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="0" />
          <scg:List x:TypeArguments="x:String" Capacity="0" />
        </scg:List>
      </iai:IONAPIRequestWizard.QueryParameters>
    </iai:IONAPIRequestWizard>
    <If Condition="[status1 = 200 or status1 = 202 OR status1 = 201]" sap2010:WorkflowViewState.IdRef="If_2">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_2">
          <Sequence.Variables>
            <Variable x:TypeArguments="x:String" Name="division" />
            <Variable x:TypeArguments="scg:List(x:String)" Name="accountingDimArr" />
            <Variable x:TypeArguments="scg:List(scg:List(x:String))" Name="ocrLineValues" />
          </Sequence.Variables>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_2">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[jobid]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[JToken.Parse(resp1.ReadAsText)("queryId").ToString]</InArgument>
            </Assign.Value>
          </Assign>
          <Delay Duration="00:00:10" sap2010:WorkflowViewState.IdRef="Delay_1" />
          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Status IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_7" Response="[resp2]" StatusCode="[status2]" Url="[tenantID + &quot;DATAFABRIC/compass/v2/jobs/&quot;+jobId+&quot;/status/?timeout=0&quot;]">
            <iai:IONAPIRequestWizard.Headers>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>Accept</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>application/json</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.Headers>
            <iai:IONAPIRequestWizard.QueryParameters>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>queryid</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>str1</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.QueryParameters>
          </iai:IONAPIRequestWizard>
          <If Condition="[status2 = 200 OR status2 = 202 OR status2 = 201]" sap2010:WorkflowViewState.IdRef="If_1">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_1">
                <Sequence.Variables>
                  <Variable x:TypeArguments="x:String" Name="variationID" />
                  <Variable x:TypeArguments="x:String" Name="statusComments" />
                  <Variable x:TypeArguments="x:String" Name="status" />
                  <Variable x:TypeArguments="x:String" Name="failureCountStr" />
                </Sequence.Variables>
                <While sap2010:WorkflowViewState.IdRef="While_1" Condition="[JToken.Parse(resp2.ReadAsText)(&quot;status&quot;).ToString = &quot;RUNNING&quot;]">
                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_16">
                    <Delay Duration="00:00:10" sap2010:WorkflowViewState.IdRef="Delay_2" />
                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Status IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_5" Response="[resp2]" StatusCode="[status2]" Url="[tenantID + &quot;DATAFABRIC/compass/v2/jobs/&quot;+jobId+&quot;/status/?timeout=0&quot;]">
                      <iai:IONAPIRequestWizard.Headers>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>Accept</x:String>
                          </scg:List>
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>application/json</x:String>
                          </scg:List>
                        </scg:List>
                      </iai:IONAPIRequestWizard.Headers>
                      <iai:IONAPIRequestWizard.QueryParameters>
                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>queryid</x:String>
                          </scg:List>
                          <scg:List x:TypeArguments="x:String" Capacity="4">
                            <x:String>str1</x:String>
                          </scg:List>
                        </scg:List>
                      </iai:IONAPIRequestWizard.QueryParameters>
                    </iai:IONAPIRequestWizard>
                  </Sequence>
                </While>
                <If Condition="[JToken.Parse(resp2.ReadAsText)(&quot;status&quot;).ToString = &quot;FINISHED&quot;]" sap2010:WorkflowViewState.IdRef="If_4">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_17">
                      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="text_plain" ContinueOnError="True" DisplayName="Result IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_4" Response="[resp3]" StatusCode="[status3]" Url="[tenantID + &quot;DATAFABRIC/compass/v2/jobs/&quot;+jobId+&quot;/result/?offset=0&amp;limit=10000&quot;]">
                        <iai:IONAPIRequestWizard.Headers>
                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                            <scg:List x:TypeArguments="x:String" Capacity="4">
                              <x:String>Accept</x:String>
                            </scg:List>
                            <scg:List x:TypeArguments="x:String" Capacity="4">
                              <x:String>application/json</x:String>
                            </scg:List>
                          </scg:List>
                        </iai:IONAPIRequestWizard.Headers>
                        <iai:IONAPIRequestWizard.QueryParameters>
                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                            <scg:List x:TypeArguments="x:String" Capacity="0" />
                            <scg:List x:TypeArguments="x:String" Capacity="0" />
                          </scg:List>
                        </iai:IONAPIRequestWizard.QueryParameters>
                      </iai:IONAPIRequestWizard>
                    </Sequence>
                  </If.Then>
                </If>
                <If Condition="[status3 = 200 OR status3 = 202 OR status3 = 201]" sap2010:WorkflowViewState.IdRef="If_3">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_5">
                      <Sequence.Variables>
                        <Variable x:TypeArguments="s:DateTime" Name="today" />
                        <Variable x:TypeArguments="s:DateTime" Name="dt1" />
                        <Variable x:TypeArguments="x:String" Name="lastRunTime" />
                        <Variable x:TypeArguments="x:Int32" Name="failureCount" />
                        <Variable x:TypeArguments="x:String" Name="vendorName" />
                        <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="poInvoiceResponseDictionary" />
                        <Variable x:TypeArguments="x:String" Name="accountingDim" />
                      </Sequence.Variables>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_3">
                        <Assign.To>
                          <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(resp3.ReadAsText)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_2" Values="[out1]">
                        <ActivityAction x:TypeArguments="njl:JToken">
                          <ActivityAction.Argument>
                            <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
                          </ActivityAction.Argument>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_9">
                            <Sequence.Variables>
                              <Variable x:TypeArguments="x:String" Name="emailSubject" />
                              <Variable x:TypeArguments="x:String" Name="emailReceivedTime" />
                              <Variable x:TypeArguments="x:String" Name="processId" />
                              <Variable x:TypeArguments="x:String" Name="fileName" />
                              <Variable x:TypeArguments="x:Int32" Name="processPoResponseCode" />
                              <Variable x:TypeArguments="x:String" Name="InvoiceType" />
                              <Variable x:TypeArguments="x:String" Name="GLCode" />
                              <Variable x:TypeArguments="x:String" Name="comments" />
                              <Variable x:TypeArguments="x:String" Name="DLComments" />
                              <Variable x:TypeArguments="x:String" Name="APResp" />
                              <Variable x:TypeArguments="x:String" Name="dn" />
                              <Variable x:TypeArguments="x:String" Name="company" />
                            </Sequence.Variables>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_70">
                              <Assign.To>
                                <OutArgument x:TypeArguments="scg:List(x:String)">[approvalList]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="scg:List(x:String)">[new list(Of String)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_53">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[accountingDim]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">
                                  <Literal x:TypeArguments="x:String" Value="" />
                                </InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_22">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[emailSubject]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("Email_Subject").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_21">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[emailReceivedTime]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("Email_Received_Time").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_25">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("Vendor_ID").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_24">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[vendorName]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("Vendor_Name").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_20">
                              <Assign.To>
                                <OutArgument x:TypeArguments="s:String[]">[ocrValues]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="s:String[]">[New String(20) {"","","", "","","","","","","","","","","","","","","","","",""}]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_11">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[ocrValues(0)]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("Invoice_Number").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_14">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[ocrValues(1)]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("Invoice_Date").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_13">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[ocrValues(2)]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("Total_Amount").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_12">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[ocrValues(3)]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("Subtotal_Amount").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_15">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[ocrValues(4)]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("PO_Number").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_16">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[ocrValues(5)]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("Tax_Amount").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_17">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[ocrValues(6)]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("Shipping_Charges").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_18">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[ocrValues(7)]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("Discount_Amount").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <If Condition="[ocrValues(5) = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_22">
                              <If.Then>
                                <Assign DisplayName="Assign Sub total" sap2010:WorkflowViewState.IdRef="Assign_65">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[ocrValues(5)]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">0</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </If.Then>
                            </If>
                            <If Condition="[ocrValues(6) = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_23">
                              <If.Then>
                                <Assign DisplayName="Assign Sub total" sap2010:WorkflowViewState.IdRef="Assign_66">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[ocrValues(6)]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">0</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </If.Then>
                            </If>
                            <If Condition="[ocrValues(7) = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_28">
                              <If.Then>
                                <Assign DisplayName="Assign Sub total" sap2010:WorkflowViewState.IdRef="Assign_74">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[ocrValues(7)]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">0</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </If.Then>
                            </If>
                            <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_7">
                              <iad:CommentOut.Activities>
                                <If Condition="[Math.abs(Convert.ToDecimal(ocrvalues(2)) - Convert.ToDecimal(ocrvalues(3)) - Convert.ToDecimal(ocrvalues(5)) - Convert.ToDecimal(ocrvalues(6)) - Convert.ToDecimal(ocrvalues(7))) &lt; 1]" sap2010:WorkflowViewState.IdRef="If_32">
                                  <If.Else>
                                    <If Condition="[Convert.ToDecimal(ocrvalues(2)) - Convert.ToDecimal(ocrvalues(3)) - Convert.ToDecimal(ocrvalues(5)) - Convert.ToDecimal(ocrvalues(6)) + Convert.ToDecimal(ocrvalues(7)) &lt; 1]" sap2010:WorkflowViewState.IdRef="If_31">
                                      <If.Then>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_84">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[ocrValues(6)]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">[(Convert.ToDecimal(ocrvalues(2)) - Convert.ToDecimal(ocrvalues(3)) - Convert.ToDecimal(ocrvalues(5))).ToString]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                      </If.Then>
                                      <If.Else>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_85">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[ocrValues(6)]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">[(Convert.ToDecimal(ocrvalues(2)) - Convert.ToDecimal(ocrvalues(3)) - Convert.ToDecimal(ocrvalues(5))).ToString]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                      </If.Else>
                                    </If>
                                  </If.Else>
                                </If>
                              </iad:CommentOut.Activities>
                            </iad:CommentOut>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_19">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[ocrValues(8)]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("Vendor_Name").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_43">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[ocrValues(11)]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("DeliveryNote_Number").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_35">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[invoiceType]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("Invoice_Type").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_9">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[fileName]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("File_Name").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_116">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[company]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("Company").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_47">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[division]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("Division").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_40">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[failureCountStr]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("Failure_Count").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_67">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[DLComments]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("Attribute_Comments").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_109">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[DLComments]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace( DLComments.Trim,"\n","\\n")]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_1">
                              <TryCatch.Try>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_91">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:Object">[miscValues("Comments")]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:Object">[DLComments]</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </TryCatch.Try>
                              <TryCatch.Catches>
                                <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_1">
                                  <ActivityAction x:TypeArguments="s:Exception">
                                    <ActivityAction.Argument>
                                      <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                    </ActivityAction.Argument>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_92">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:Object">[miscValues("Comments")]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                  </ActivityAction>
                                </Catch>
                              </TryCatch.Catches>
                            </TryCatch>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_110">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Object">[miscValues("addInfo")]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Object">[item("Additional_Attribute_3").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_131">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[APResp1]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">
                                  <Literal x:TypeArguments="x:String" Value="" />
                                </InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_83">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[APResp]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item("APResp").ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_45">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">
                                  <Literal x:TypeArguments="x:String" Value="" />
                                </InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_96">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[dn]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">
                                  <Literal x:TypeArguments="x:String" Value="" />
                                </InArgument>
                              </Assign.Value>
                            </Assign>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_33">
                              <Sequence.Variables>
                                <Variable x:TypeArguments="scg:List(x:String)" Name="linelist" />
                              </Sequence.Variables>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_75">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="scg:List(scg:List(x:String))">[ocrLineValues]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[New List(Of List(Of String))]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_80">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[accountingDim]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[item("Line_Data").ToString()]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_81">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="scg:List(x:String)">[accountingDimArr]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="scg:List(x:String)">[accountingDim.Split(New String() {Microsoft.VisualBasic.ControlChars.Lf}, StringSplitOptions.RemoveEmptyEntries).ToList]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <If Condition="[accountingDimArr.count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_30">
                                <If.Then>
                                  <ForEach x:TypeArguments="x:Object" DisplayName="ForEach&lt;Object&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_9" Values="[accountingDimArr]">
                                    <ActivityAction x:TypeArguments="x:Object">
                                      <ActivityAction.Argument>
                                        <DelegateInArgument x:TypeArguments="x:Object" Name="item1" />
                                      </ActivityAction.Argument>
                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_35">
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_82">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="scg:List(x:String)">[linelist]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <If Condition="[item1.ToString.Split(New String() {&quot;||&quot;}, StringSplitOptions.None)(0).contains(&quot;(&quot;)]" sap2010:WorkflowViewState.IdRef="If_33">
                                          <If.Then>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_41">
                                              <If Condition="[item1.ToString.Split(New String() {&quot;||&quot;}, StringSplitOptions.None)(0).Split(&quot;(&quot;c).Count &gt; 2]" sap2010:WorkflowViewState.IdRef="If_44">
                                                <If.Then>
                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_42">
                                                    <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_11" Values="[item1.ToString.Split(New String() {&quot;||&quot;}, StringSplitOptions.None)(0).Split(&quot;(&quot;c)]">
                                                      <ActivityAction x:TypeArguments="x:String">
                                                        <ActivityAction.Argument>
                                                          <DelegateInArgument x:TypeArguments="x:String" Name="item3" />
                                                        </ActivityAction.Argument>
                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_43">
                                                          <If Condition="[item3.contains(&quot;,&quot;)]" sap2010:WorkflowViewState.IdRef="If_45">
                                                            <If.Then>
                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_93">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="x:String">[dn]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="x:String">[dn + "," +item3.ToString.split(","c)(0)]</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                            </If.Then>
                                                          </If>
                                                        </Sequence>
                                                      </ActivityAction>
                                                    </ForEach>
                                                  </Sequence>
                                                </If.Then>
                                                <If.Else>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_111">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[dn]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">[dn + "," +(item1.ToString.Split(New String() {"||"}, StringSplitOptions.None)(0).Split("("c)(1)).split(","c)(0)]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                </If.Else>
                                              </If>
                                            </Sequence>
                                          </If.Then>
                                        </If>
                                        <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_8" Values="[item1.ToString.Split(New String() {&quot;||&quot;}, StringSplitOptions.None)]">
                                          <ActivityAction x:TypeArguments="x:String">
                                            <ActivityAction.Argument>
                                              <DelegateInArgument x:TypeArguments="x:String" Name="item2" />
                                            </ActivityAction.Argument>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_34">
                                              <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_8" MethodName="Add">
                                                <InvokeMethod.TargetObject>
                                                  <InArgument x:TypeArguments="scg:List(x:String)">[linelist]</InArgument>
                                                </InvokeMethod.TargetObject>
                                                <InArgument x:TypeArguments="x:String">[item2]</InArgument>
                                              </InvokeMethod>
                                            </Sequence>
                                          </ActivityAction>
                                        </ForEach>
                                        <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_9" MethodName="Add">
                                          <InvokeMethod.TargetObject>
                                            <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[ocrLineValues]</InArgument>
                                          </InvokeMethod.TargetObject>
                                          <InArgument x:TypeArguments="scg:List(x:String)">[linelist]</InArgument>
                                        </InvokeMethod>
                                      </Sequence>
                                    </ActivityAction>
                                  </ForEach>
                                </If.Then>
                              </If>
                            </Sequence>
                            <If Condition="[dn &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_34">
                              <If.Then>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_36">
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_95">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[dn]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[dn.trim(","c)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_6">
                                    <iad:CommentOut.Activities>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_97">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[ocrValues(11)]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[dn]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                    </iad:CommentOut.Activities>
                                  </iad:CommentOut>
                                </Sequence>
                              </If.Then>
                            </If>
                            <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_2" IsValid="[fileExist]" Path="[configurationFolder+&quot;\Failure\&quot;+fileName]" />
                            <If Condition="[item(&quot;Status&quot;).ToString() = &quot;PONOTRECEIVED&quot;]" sap2010:WorkflowViewState.IdRef="If_5">
                              <If.Then>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_19">
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_6">
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_10">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:Int32">[failureCount]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:Int32">[CInt(item("Failure_Count").ToString)]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_8">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[lastRunTime]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">[item("Last_Run_Time").ToString()]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <If Condition="[fileExist]" sap2010:WorkflowViewState.IdRef="If_8">
                                      <If.Then>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_8">
                                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_2" Line="[Environment.NewLine+&quot;Data lake Entry Available. Status : PONOTRECEIVED  Failure Count :&quot;+failureCount.tostring]" Source="[logFile]" />
                                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_5" Line="[&quot;Datalake Entry Values&quot;+Environment.NewLine  +&quot;Invoice Number : &quot; + ocrValues(0) + Environment.NewLine + &quot;Purchase Order Number: &quot; + ocrValues(4) + Environment.NewLine  +&quot;Date: &quot; + ocrValues(1)+ Environment.NewLine  + &quot;Sub Total: &quot; + ocrValues(3)+ Environment.NewLine + &quot;Tax: &quot; + ocrValues(5) + Environment.NewLine  + &quot;Total: &quot; + ocrValues(2) + Environment.NewLine +&quot;Shipping Handling charge : &quot;+ ocrValues(6)+ Environment.NewLine +&quot;Discount : &quot;+ ocrValues(7) + Environment.NewLine +&quot;Delivery Note number : &quot;+ ocrValues(11)]" Source="[logFile]" />
                                          <If Condition="[invoiceType = &quot;POINVOICE&quot;]" sap2010:WorkflowViewState.IdRef="If_42">
                                            <If.Then>
                                              <If Condition="[ocrValues(11).trim &lt;&gt; &quot;&quot; AND miscValues(&quot;processDeliveryNote&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_41">
                                                <If.Then>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_104">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[invoiceType]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">DELIVERYNOTE</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                </If.Then>
                                              </If>
                                            </If.Then>
                                          </If>
                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_47">
                                            <Sequence.Variables>
                                              <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="APRespRespDict" />
                                              <Variable x:TypeArguments="x:Int32" Name="APRespRespCode" />
                                            </Sequence.Variables>
                                            <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;division&quot;,division},{&quot;company&quot;,company},{&quot;VendorID&quot;,vendorID}}]" ContinueOnError="True" DisplayName="AP resp Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_19" OutputArguments="[APRespRespDict]" ResponseCode="[APRespRespCode]" WorkflowFile="[projectPath+&quot;\APResp.xaml&quot;]" />
                                            <If Condition="[APRespRespCode = 200]" sap2010:WorkflowViewState.IdRef="If_49">
                                              <If.Then>
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_119">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[APResp1]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String">[CType(APRespRespDict("APResp"), String)]</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                              </If.Then>
                                            </If>
                                          </Sequence>
                                          <If Condition="[ocrValues(11).trim = &quot;&quot; AND miscValues(&quot;MandateDeliveryNoteWhenPo&quot;).ToString.ToLower = &quot;true&quot; AND miscValues(&quot;processDeliveryNote&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_46">
                                            <If.Then>
                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_45">
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_132">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[processId]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String">[item("RPA_Process_ID").ToString()]</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_133">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[variationID]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String">[item("Header_Variation_ID").ToString()]</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_112">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[status]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String">NEEDSVERIFICATION</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_113">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[statusComments]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String" xml:space="preserve">[DLComments + "   "+" Enter Delivery note number."]</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_11" Line="[&quot;Status: &quot; + status + &quot; Comments: &quot; + statusComments]" Source="[logFile]" />
                                                <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;documentName&quot;,&quot;RPA_Process_Header&quot;},{&quot;logicalId&quot;,datalakeAPILogicalId},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;tenantID&quot;,tenantID},{&quot;RPA_Process_ID&quot;,processId},{&quot;Status&quot;,status},{&quot;Status_Display&quot;,Status},{&quot;Comments&quot;,statusComments},{&quot;Last_Run_Time&quot;,System.DateTime.Now.ToString(&quot;yyyy-MM-ddTHH:mm:ss.fffZ&quot;)},{&quot;Process_Type&quot;,&quot;INVOICE PROCESSING&quot;},{&quot;Name&quot;,&quot;&quot;},{&quot;ERP&quot;,&quot;M3&quot;},{&quot;Variation_ID&quot;,(CInt(variationID) + 1).ToString},{&quot;Updated_By&quot;,&quot;N&quot;},{&quot;Additional1&quot;,&quot;&quot;}, {&quot;Additional2&quot;,&quot;&quot;},{&quot;failureCount&quot;,failureCountStr},{&quot;logFile&quot;,logFile}}]" ContinueOnError="False" DisplayName="Invoke SentToInvoiceProcessingResults_Headers" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_16" WorkflowFile="[projectPath+&quot;\SentToInvoiceProcessingResults_Headers.xaml&quot;]" />
                                                <If Condition="[APResp1 &lt;&gt; APResp]" sap2010:WorkflowViewState.IdRef="If_52">
                                                  <If.Then>
                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_124">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="scg:List(s:String[])">[Attribute_List]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="scg:List(s:String[])" xml:space="preserve">[New List(Of String())() From {
({processId+"7", "Last_Run_Time", System.DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"), variationID}),
    ({processId + "19", "Comments", statusComments, variationID}),
({processId+"31", "APResp", APResp1, variationID})
}]</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                  </If.Then>
                                                  <If.Else>
                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_125">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="scg:List(s:String[])">[Attribute_List]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="scg:List(s:String[])" xml:space="preserve">[New List(Of String())() From {
({processId+"7", "Last_Run_Time", System.DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"), variationID}),
    ({processId + "19", "Comments", statusComments, variationID})
}]</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                  </If.Else>
                                                </If>
                                                <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;logicalId&quot;,datalakeAPILogicalId},{&quot;documentName&quot;,&quot;RPA_Process_Attributes&quot;},{&quot;AttributeList&quot;,Attribute_List},{&quot;Operation&quot;,&quot;update&quot;},{&quot;Process_ID&quot;,processId},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;logFile&quot;,logFile}}]" ContinueOnError="False" DisplayName="Invoke SentToInvoiceProcessingResults_Attributes" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_17" WorkflowFile="[projectPath+&quot;\SentToInvoiceProcessingResults_Attributes.xaml&quot;]" />
                                              </Sequence>
                                            </If.Then>
                                            <If.Else>
                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_44">
                                                <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;ocrValues&quot;,ocrValues},{&quot;colemanAPI&quot;,colemanAPI},{&quot;logFile&quot;,logFile},{&quot;userIdentifier&quot;,userIdentifier},{&quot;distributionType&quot;,distributionType}, {&quot;emailSubject&quot;,emailSubject},{&quot;vendorId&quot;,vendorId},{&quot;miscValues&quot;,miscValues},{&quot;emailReceivedTime&quot;,emailReceivedTime},{&quot;projectPath&quot;,projectPath},{&quot;configurationFolder&quot;,configurationFolder},{&quot;datalakeAPILogicalId&quot;,datalakeAPILogicalId},{&quot;chargeCode&quot;,chargeCode},{&quot;discountCode&quot;,discountCode},{&quot;authUser&quot;,authUser},{&quot;APResp&quot;,APResp},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;failureCount&quot;,failureCount},{&quot;documentPath&quot;,configurationFolder + &quot;\Failure\&quot; + FileName},{&quot;includeDatalake&quot;,True},{&quot;invoiceType&quot;,invoiceType},{&quot;approvalRequired&quot;,approvalRequired},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;matchVendorItemCode&quot;,matchVendorItemCode},{&quot;division&quot;,division},{&quot;ocrLineValues&quot;,ocrLineValues}}]" ContinueOnError="True" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_1" OutputArguments="[poInvoiceResponseDictionary]" ResponseCode="[processPoResponseCode]" WorkflowFile="[projectPath+&quot;\ProcessPOInvoice.xaml&quot;]" />
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_71">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="scg:List(x:String)">[approvalList]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="scg:List(x:String)">[CType(poInvoiceResponseDictionary("approvalList"), List(Of String))]</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                                <If Condition="[processPoResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_9">
                                                  <If.Then>
                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_10">
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_23">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[status]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">[CType(poInvoiceResponseDictionary("status"), String)]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_26">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[processId]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">[item("RPA_Process_ID").ToString()]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_27">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[variationID]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">[item("Header_Variation_ID").ToString()]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <If Condition="[DLComments.EndsWith(CType(poInvoiceResponseDictionary(&quot;statusComments&quot;), String))]" sap2010:WorkflowViewState.IdRef="If_43">
                                                        <If.Then>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_105">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:String">[statusComments]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:String">[DLComments]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                        </If.Then>
                                                        <If.Else>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_106">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:String">[statusComments]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:String" xml:space="preserve">[DLComments + "  " +CType(poInvoiceResponseDictionary("statusComments"), String)]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                        </If.Else>
                                                      </If>
                                                      <If Condition="[status = &quot;PONOTRECEIVED&quot;]" sap2010:WorkflowViewState.IdRef="If_10">
                                                        <If.Then>
                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_20">
                                                            <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;documentName&quot;,&quot;RPA_Process_Header&quot;},{&quot;logicalId&quot;,datalakeAPILogicalId},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;tenantID&quot;,tenantID},{&quot;RPA_Process_ID&quot;,processId},{&quot;Status&quot;,status},{&quot;Status_Display&quot;,status},{&quot;Comments&quot;,statusComments},{&quot;Last_Run_Time&quot;,System.DateTime.Now.ToString(&quot;yyyy-MM-ddTHH:mm:ss.fffZ&quot;)},{&quot;Process_Type&quot;,&quot;INVOICE PROCESSING&quot;},{&quot;Name&quot;,&quot;&quot;},{&quot;ERP&quot;,&quot;M3&quot;},{&quot;Variation_ID&quot;,(CInt(variationID) + 1).ToString},{&quot;Updated_By&quot;,&quot;N&quot;},{&quot;Additional1&quot;,&quot;&quot;}, {&quot;Additional2&quot;,&quot;&quot;},{&quot;failureCount&quot;,(failureCount+1).ToString},{&quot;logFile&quot;,logFile}}]" ContinueOnError="False" DisplayName="Invoke SentToInvoiceProcessingResults_Headers" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_8" WorkflowFile="[projectPath+&quot;\SentToInvoiceProcessingResults_Headers.xaml&quot;]" />
                                                          </Sequence>
                                                        </If.Then>
                                                        <If.Else>
                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_21">
                                                            <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;documentName&quot;,&quot;RPA_Process_Header&quot;},{&quot;logicalId&quot;,datalakeAPILogicalId},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;tenantID&quot;,tenantID},{&quot;RPA_Process_ID&quot;,processId},{&quot;Status&quot;,status},{&quot;Status_Display&quot;,status},{&quot;Comments&quot;,statusComments},{&quot;Last_Run_Time&quot;,System.DateTime.Now.ToString(&quot;yyyy-MM-ddTHH:mm:ss.fffZ&quot;)},{&quot;Process_Type&quot;,&quot;INVOICE PROCESSING&quot;},{&quot;Name&quot;,&quot;&quot;},{&quot;ERP&quot;,&quot;M3&quot;},{&quot;Variation_ID&quot;,(CInt(variationID) + 1).ToString},{&quot;Updated_By&quot;,&quot;N&quot;},{&quot;Additional1&quot;,&quot;&quot;}, {&quot;Additional2&quot;,&quot;&quot;},{&quot;failureCount&quot;,failureCount.ToString},{&quot;logFile&quot;,logFile}}]" ContinueOnError="False" DisplayName="Invoke SentToInvoiceProcessingResults_Headers" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_9" WorkflowFile="[projectPath+&quot;\SentToInvoiceProcessingResults_Headers.xaml&quot;]" />
                                                          </Sequence>
                                                        </If.Else>
                                                      </If>
                                                      <If Condition="[APResp1 &lt;&gt; APResp]" sap2010:WorkflowViewState.IdRef="If_51">
                                                        <If.Then>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_122">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="scg:List(s:String[])">[Attribute_List]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="scg:List(s:String[])" xml:space="preserve">[New List(Of String())() From {
({processId+"7", "Last_Run_Time", System.DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"), variationID}),
    ({processId + "19", "Comments", statusComments, variationID}),
({processId+"31", "APResp", APResp1, variationID})
}]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                        </If.Then>
                                                        <If.Else>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_123">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="scg:List(s:String[])">[Attribute_List]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="scg:List(s:String[])" xml:space="preserve">[New List(Of String())() From {
({processId+"7", "Last_Run_Time", System.DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"), variationID}),
    ({processId + "19", "Comments", statusComments, variationID})
}]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                        </If.Else>
                                                      </If>
                                                      <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;logicalId&quot;,datalakeAPILogicalId},{&quot;documentName&quot;,&quot;RPA_Process_Attributes&quot;},{&quot;AttributeList&quot;,Attribute_List},{&quot;Operation&quot;,&quot;update&quot;},{&quot;Process_ID&quot;,processId},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;logFile&quot;,logFile}}]" ContinueOnError="False" DisplayName="Invoke SentToInvoiceProcessingResults_Attributes" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_11" WorkflowFile="[projectPath+&quot;\SentToInvoiceProcessingResults_Attributes.xaml&quot;]" />
                                                    </Sequence>
                                                  </If.Then>
                                                </If>
                                              </Sequence>
                                            </If.Else>
                                          </If>
                                        </Sequence>
                                      </If.Then>
                                    </If>
                                  </Sequence>
                                </Sequence>
                              </If.Then>
                            </If>
                            <If Condition="[item(&quot;Status&quot;).ToString() = &quot;MISSINGINFORMATION&quot; OR item(&quot;Status&quot;).ToString() = &quot;NEEDSVERIFICATION&quot;]" sap2010:WorkflowViewState.IdRef="If_11">
                              <If.Then>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_18">
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_13">
                                    <If Condition="[fileExist]" sap2010:WorkflowViewState.IdRef="If_13">
                                      <If.Then>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_12">
                                          <If Condition="[item(&quot;Status&quot;).ToString() = &quot;NEEDSVERIFICATION&quot; AND invoiceType = &quot;EXPENSE&quot;]" sap2010:WorkflowViewState.IdRef="If_16">
                                            <If.Then>
                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_22">
                                                <Sequence.Variables>
                                                  <Variable x:TypeArguments="scg:List(x:String)" Name="linelist" />
                                                </Sequence.Variables>
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_56">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="scg:List(scg:List(x:String))">[ocrLineValues]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[New List(Of List(Of String))]</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_44">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String">[item("GLCode").ToString()]</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                                <If Condition="[GLCode = &quot;DISTRIBUTED&quot;]" sap2010:WorkflowViewState.IdRef="If_17">
                                                  <If.Then>
                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_25">
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_51">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[accountingDim]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">[item("Line_Data").ToString()]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_54">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="scg:List(x:String)">[accountingDimArr]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="scg:List(x:String)">[accountingDim.Split(New String() {Microsoft.VisualBasic.ControlChars.Lf}, StringSplitOptions.RemoveEmptyEntries).ToList]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <ForEach x:TypeArguments="x:Object" DisplayName="ForEach&lt;Object&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_4" Values="[accountingDimArr]">
                                                        <ActivityAction x:TypeArguments="x:Object">
                                                          <ActivityAction.Argument>
                                                            <DelegateInArgument x:TypeArguments="x:Object" Name="item" />
                                                          </ActivityAction.Argument>
                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_23">
                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_55">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="scg:List(x:String)">[linelist]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                            <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_5" Values="[item.ToString.Split(New String() {&quot;||&quot;}, StringSplitOptions.None)]">
                                                              <ActivityAction x:TypeArguments="x:String">
                                                                <ActivityAction.Argument>
                                                                  <DelegateInArgument x:TypeArguments="x:String" Name="item2" />
                                                                </ActivityAction.Argument>
                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_24">
                                                                  <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_3" MethodName="Add">
                                                                    <InvokeMethod.TargetObject>
                                                                      <InArgument x:TypeArguments="scg:List(x:String)">[linelist]</InArgument>
                                                                    </InvokeMethod.TargetObject>
                                                                    <InArgument x:TypeArguments="x:String">[item2]</InArgument>
                                                                  </InvokeMethod>
                                                                </Sequence>
                                                              </ActivityAction>
                                                            </ForEach>
                                                            <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_4" MethodName="Add">
                                                              <InvokeMethod.TargetObject>
                                                                <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[ocrLineValues]</InArgument>
                                                              </InvokeMethod.TargetObject>
                                                              <InArgument x:TypeArguments="scg:List(x:String)">[linelist]</InArgument>
                                                            </InvokeMethod>
                                                          </Sequence>
                                                        </ActivityAction>
                                                      </ForEach>
                                                    </Sequence>
                                                  </If.Then>
                                                </If>
                                              </Sequence>
                                            </If.Then>
                                          </If>
                                          <If Condition="[invoiceType = &quot;POINVOICE&quot;]" sap2010:WorkflowViewState.IdRef="If_18">
                                            <If.Then>
                                              <If Condition="[ocrValues(11).trim &lt;&gt; &quot;&quot; AND miscValues(&quot;processDeliveryNote&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_19">
                                                <If.Then>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_57">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[invoiceType]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">DELIVERYNOTE</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                </If.Then>
                                              </If>
                                            </If.Then>
                                          </If>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_61">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[processId]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[item("RPA_Process_ID").ToString()]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_62">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[variationID]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[item("Header_Variation_ID").ToString()]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_8" Line="[Environment.NewLine+&quot;Datalake Entry Available. Status : &quot;+item(&quot;Status&quot;).ToString()+&quot; Is updated by user : Y&quot;]" Source="[logFile]" />
                                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_9" Line="[&quot;Datalake Entry Values&quot;+Environment.NewLine  +&quot;Invoice Number : &quot; + ocrValues(0) + Environment.NewLine + &quot;Purchase Order Number: &quot; + ocrValues(4) + Environment.NewLine  +&quot;Date: &quot; + ocrValues(1)+ Environment.NewLine  + &quot;Sub Total: &quot; + ocrValues(3)+ Environment.NewLine + &quot;Tax: &quot; + ocrValues(5) + Environment.NewLine  + &quot;Total: &quot; + ocrValues(2) + Environment.NewLine +&quot;Shipping Handling charge : &quot;+ ocrValues(6)+ Environment.NewLine +&quot;Discount : &quot;+ ocrValues(7)+ Environment.NewLine +&quot;Delivery Note number : &quot;+ ocrValues(11)]" Source="[logFile]" />
                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_48">
                                            <Sequence.Variables>
                                              <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="APRespRespDict" />
                                              <Variable x:TypeArguments="x:Int32" Name="APRespRespCode" />
                                            </Sequence.Variables>
                                            <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;division&quot;,division},{&quot;company&quot;,company},{&quot;VendorID&quot;,vendorID}}]" ContinueOnError="True" DisplayName="AP resp Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_20" OutputArguments="[APRespRespDict]" ResponseCode="[APRespRespCode]" WorkflowFile="[projectPath+&quot;\APResp.xaml&quot;]" />
                                            <If Condition="[APRespRespCode = 200]" sap2010:WorkflowViewState.IdRef="If_55">
                                              <If.Then>
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_130">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[APResp1]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String">[CType(APRespRespDict("APResp"), String)]</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                              </If.Then>
                                            </If>
                                          </Sequence>
                                          <If Condition="[ocrValues(4).trim = &quot;&quot; AND ocrValues(11).trim = &quot;&quot; AND (invoiceType = &quot;DELIVERYNOTE&quot; OR invoiceType = &quot;POINVOICE&quot;)]" sap2010:WorkflowViewState.IdRef="If_21">
                                            <If.Then>
                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_27">
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_60">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[status]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String">NEEDSVERIFICATION</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_63">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[statusComments]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String" xml:space="preserve">[DLComments + "   "+"Enter either PO or Delivery note number if it is a PO INVOICE"]</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_10" Line="[&quot;Status: &quot; + status + &quot; Comments: &quot; + statusComments]" Source="[logFile]" />
                                                <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;documentName&quot;,&quot;RPA_Process_Header&quot;},{&quot;logicalId&quot;,datalakeAPILogicalId},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;tenantID&quot;,tenantID},{&quot;RPA_Process_ID&quot;,processId},{&quot;Status&quot;,status},{&quot;Status_Display&quot;,Status},{&quot;Comments&quot;,statusComments},{&quot;Last_Run_Time&quot;,System.DateTime.Now.ToString(&quot;yyyy-MM-ddTHH:mm:ss.fffZ&quot;)},{&quot;Process_Type&quot;,&quot;INVOICE PROCESSING&quot;},{&quot;Name&quot;,&quot;&quot;},{&quot;ERP&quot;,&quot;M3&quot;},{&quot;Variation_ID&quot;,(CInt(variationID) + 1).ToString},{&quot;Updated_By&quot;,&quot;N&quot;},{&quot;Additional1&quot;,&quot;&quot;}, {&quot;Additional2&quot;,&quot;&quot;},{&quot;failureCount&quot;,failureCountStr},{&quot;logFile&quot;,logFile}}]" ContinueOnError="False" DisplayName="Invoke SentToInvoiceProcessingResults_Headers" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_13" WorkflowFile="[projectPath+&quot;\SentToInvoiceProcessingResults_Headers.xaml&quot;]" />
                                                <If Condition="[APResp1 &lt;&gt; APResp]" sap2010:WorkflowViewState.IdRef="If_53">
                                                  <If.Then>
                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_126">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="scg:List(s:String[])">[Attribute_List]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="scg:List(s:String[])" xml:space="preserve">[New List(Of String())() From {
({processId+"7", "Last_Run_Time", System.DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"), variationID}),
    ({processId + "19", "Comments", statusComments, variationID}),
({processId+"31", "APResp", APResp1, variationID})
}]</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                  </If.Then>
                                                  <If.Else>
                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_127">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="scg:List(s:String[])">[Attribute_List]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="scg:List(s:String[])" xml:space="preserve">[New List(Of String())() From {
({processId+"7", "Last_Run_Time", System.DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"), variationID}),
    ({processId + "19", "Comments", statusComments, variationID})
}]</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                  </If.Else>
                                                </If>
                                                <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;logicalId&quot;,datalakeAPILogicalId},{&quot;documentName&quot;,&quot;RPA_Process_Attributes&quot;},{&quot;AttributeList&quot;,Attribute_List},{&quot;Operation&quot;,&quot;update&quot;},{&quot;Process_ID&quot;,processId},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;logFile&quot;,logFile}}]" ContinueOnError="False" DisplayName="Invoke SentToInvoiceProcessingResults_Attributes" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_14" WorkflowFile="[projectPath+&quot;\SentToInvoiceProcessingResults_Attributes.xaml&quot;]" />
                                              </Sequence>
                                            </If.Then>
                                            <If.Else>
                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_26">
                                                <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;ocrValues&quot;,ocrValues},{&quot;colemanAPI&quot;,colemanAPI},{&quot;vendorNames&quot;,ocrValues(8)},{&quot;logFile&quot;,logFile},{&quot;invoiceType&quot;,invoiceType},{&quot;userIdentifier&quot;,userIdentifier},{&quot;miscValues&quot;,miscValues},{&quot;distributionType&quot;,distributionType}, {&quot;emailSubject&quot;,emailSubject},{&quot;emailReceivedTime&quot;,emailReceivedTime},{&quot;projectPath&quot;,projectPath}, {&quot;manualEntry&quot;,false},{&quot;configurationFolder&quot;,configurationFolder},{&quot;datalakeAPILogicalId&quot;,datalakeAPILogicalId},{&quot;chargeCode&quot;,chargeCode},{&quot;discountCode&quot;,discountCode},{&quot;authUser&quot;,authUser},{&quot;APResp&quot;,APResp},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;failureCount&quot;,0},{&quot;documentPath&quot;,configurationFolder + &quot;\Failure\&quot; + FileName},{&quot;includeDatalake&quot;,True},{&quot;extractFromWidgetDatalake&quot;,extractFromWidgetDatalake},{&quot;approvalRequired&quot;,approvalRequired},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;matchVendorItemCode&quot;,matchVendorItemCode},{&quot;GLCode&quot;,GLCode},{&quot;division&quot;,division},{&quot;vendorId&quot;,vendorId},{&quot;ocrLineValues&quot;,ocrLineValues}}]" ContinueOnError="True" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_4" OutputArguments="[poInvoiceResponseDictionary]" ResponseCode="[processPoResponseCode]" WorkflowFile="[projectPath+&quot;\ProcessPOInvoice.xaml&quot;]" />
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_72">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="scg:List(x:String)">[approvalList]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="scg:List(x:String)">[CType(poInvoiceResponseDictionary("approvalList"), List(Of String))]</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                                <If Condition="[processPoResponseCode=200]" sap2010:WorkflowViewState.IdRef="If_14">
                                                  <If.Then>
                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_15">
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_30">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[status]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">[CType(poInvoiceResponseDictionary("status"), String)]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <If Condition="[status = &quot;PONOTRECEIVED&quot;]" sap2010:WorkflowViewState.IdRef="If_15">
                                                        <If.Then>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_37">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:String">[failureCountStr]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:String">1</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                        </If.Then>
                                                      </If>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_33">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[statusComments]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String" xml:space="preserve">[DLComments +"  " +CType(poInvoiceResponseDictionary("statusComments"), String)]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;documentName&quot;,&quot;RPA_Process_Header&quot;},{&quot;logicalId&quot;,datalakeAPILogicalId},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;tenantID&quot;,tenantID},{&quot;RPA_Process_ID&quot;,processId},{&quot;Status&quot;,status},{&quot;Status_Display&quot;,Status},{&quot;Comments&quot;,statusComments},{&quot;Last_Run_Time&quot;,System.DateTime.Now.ToString(&quot;yyyy-MM-ddTHH:mm:ss.fffZ&quot;)},{&quot;Process_Type&quot;,&quot;INVOICE PROCESSING&quot;},{&quot;Name&quot;,&quot;&quot;},{&quot;ERP&quot;,&quot;M3&quot;},{&quot;Variation_ID&quot;,(CInt(variationID) + 1).ToString},{&quot;Updated_By&quot;,&quot;N&quot;},{&quot;Additional1&quot;,&quot;&quot;}, {&quot;Additional2&quot;,&quot;&quot;},{&quot;failureCount&quot;,failureCountStr},{&quot;logFile&quot;,logFile}}]" ContinueOnError="False" DisplayName="Invoke SentToInvoiceProcessingResults_Headers" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_7" WorkflowFile="[projectPath+&quot;\SentToInvoiceProcessingResults_Headers.xaml&quot;]" />
                                                      <If Condition="[APResp1 &lt;&gt; APResp]" sap2010:WorkflowViewState.IdRef="If_54">
                                                        <If.Then>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_128">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="scg:List(s:String[])">[Attribute_List]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="scg:List(s:String[])" xml:space="preserve">[New List(Of String())() From {
({processId+"7", "Last_Run_Time", System.DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"), variationID}),
    ({processId + "19", "Comments", statusComments, variationID}),
({processId+"31", "APResp", APResp1, variationID})
}]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                        </If.Then>
                                                        <If.Else>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_129">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="scg:List(s:String[])">[Attribute_List]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="scg:List(s:String[])" xml:space="preserve">[New List(Of String())() From {
({processId+"7", "Last_Run_Time", System.DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"), variationID}),
    ({processId + "19", "Comments", statusComments, variationID})
}]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                        </If.Else>
                                                      </If>
                                                      <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;logicalId&quot;,datalakeAPILogicalId},{&quot;documentName&quot;,&quot;RPA_Process_Attributes&quot;},{&quot;AttributeList&quot;,Attribute_List},{&quot;Operation&quot;,&quot;update&quot;},{&quot;Process_ID&quot;,processId},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;logFile&quot;,logFile}}]" ContinueOnError="False" DisplayName="Invoke SentToInvoiceProcessingResults_Attributes" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_12" WorkflowFile="[projectPath+&quot;\SentToInvoiceProcessingResults_Attributes.xaml&quot;]" />
                                                    </Sequence>
                                                  </If.Then>
                                                </If>
                                              </Sequence>
                                            </If.Else>
                                          </If>
                                        </Sequence>
                                      </If.Then>
                                    </If>
                                  </Sequence>
                                </Sequence>
                              </If.Then>
                            </If>
                            <If Condition="[miscValues(&quot;AutomateApproval&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_25">
                              <If.Then>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_28">
                                  <If Condition="[approvalList.count&gt;0]" sap2010:WorkflowViewState.IdRef="If_24">
                                    <If.Then>
                                      <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_5" MethodName="Add">
                                        <InvokeMethod.TargetObject>
                                          <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[approvalLists]</InArgument>
                                        </InvokeMethod.TargetObject>
                                        <InArgument x:TypeArguments="scg:List(x:String)">[approvalList]</InArgument>
                                      </InvokeMethod>
                                    </If.Then>
                                  </If>
                                </Sequence>
                              </If.Then>
                            </If>
                          </Sequence>
                        </ActivityAction>
                      </ForEach>
                    </Sequence>
                  </If.Then>
                </If>
              </Sequence>
            </If.Then>
          </If>
        </Sequence>
      </If.Then>
    </If>
    <If Condition="[miscValues(&quot;AutomateApproval&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_27">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_29">
          <If Condition="[approvalLists.count&gt;0]" sap2010:WorkflowViewState.IdRef="If_26">
            <If.Then>
              <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;approvalLists&quot;,approvalLists},{&quot;logFile&quot;,logFile},{&quot;tenantID&quot;,tenantID}}]" ContinueOnError="True" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_15" WorkflowFile="[projectPath+&quot;\approval.xaml&quot;]" />
            </If.Then>
          </If>
        </Sequence>
      </If.Then>
    </If>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_69" sap:VirtualizedContainerService.HintSize="2096.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_73" sap:VirtualizedContainerService.HintSize="2096.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="2096.66666666667,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_6" sap:VirtualizedContainerService.HintSize="2096.66666666667,22" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="1948.66666666667,62" />
      <sap2010:ViewStateData Id="Delay_1" sap:VirtualizedContainerService.HintSize="1948.66666666667,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_7" sap:VirtualizedContainerService.HintSize="1948.66666666667,22" />
      <sap2010:ViewStateData Id="Delay_2" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_16" sap:VirtualizedContainerService.HintSize="222,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="While_1" sap:VirtualizedContainerService.HintSize="1800.66666666667,372" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_4" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_17" sap:VirtualizedContainerService.HintSize="222,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="1800.66666666667,300" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="1652.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_70" sap:VirtualizedContainerService.HintSize="1600,62" />
      <sap2010:ViewStateData Id="Assign_53" sap:VirtualizedContainerService.HintSize="1600,62" />
      <sap2010:ViewStateData Id="Assign_22" sap:VirtualizedContainerService.HintSize="1600,62" />
      <sap2010:ViewStateData Id="Assign_21" sap:VirtualizedContainerService.HintSize="1600,62" />
      <sap2010:ViewStateData Id="Assign_25" sap:VirtualizedContainerService.HintSize="1600,62" />
      <sap2010:ViewStateData Id="Assign_24" sap:VirtualizedContainerService.HintSize="1600,62" />
      <sap2010:ViewStateData Id="Assign_20" sap:VirtualizedContainerService.HintSize="1600,62" />
      <sap2010:ViewStateData Id="Assign_11" sap:VirtualizedContainerService.HintSize="1600,62" />
      <sap2010:ViewStateData Id="Assign_14" sap:VirtualizedContainerService.HintSize="1600,62" />
      <sap2010:ViewStateData Id="Assign_13" sap:VirtualizedContainerService.HintSize="1600,62" />
      <sap2010:ViewStateData Id="Assign_12" sap:VirtualizedContainerService.HintSize="1600,62" />
      <sap2010:ViewStateData Id="Assign_15" sap:VirtualizedContainerService.HintSize="1600,62" />
      <sap2010:ViewStateData Id="Assign_16" sap:VirtualizedContainerService.HintSize="1600,62" />
      <sap2010:ViewStateData Id="Assign_17" sap:VirtualizedContainerService.HintSize="1600,62" />
      <sap2010:ViewStateData Id="Assign_18" sap:VirtualizedContainerService.HintSize="1600,62" />
      <sap2010:ViewStateData Id="Assign_65" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_22" sap:VirtualizedContainerService.HintSize="1600,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_66" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_23" sap:VirtualizedContainerService.HintSize="1600,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_74" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_28" sap:VirtualizedContainerService.HintSize="1600,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_84" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_85" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_31" sap:VirtualizedContainerService.HintSize="510,216" />
      <sap2010:ViewStateData Id="If_32" sap:VirtualizedContainerService.HintSize="636,370" />
      <sap2010:ViewStateData Id="CommentOut_7" sap:VirtualizedContainerService.HintSize="1600,466" />
      <sap2010:ViewStateData Id="Assign_19" sap:VirtualizedContainerService.HintSize="1600,62" />
      <sap2010:ViewStateData Id="Assign_43" sap:VirtualizedContainerService.HintSize="1600,62" />
      <sap2010:ViewStateData Id="Assign_35" sap:VirtualizedContainerService.HintSize="1600,62" />
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="1600,62" />
      <sap2010:ViewStateData Id="Assign_116" sap:VirtualizedContainerService.HintSize="1600,62" />
      <sap2010:ViewStateData Id="Assign_47" sap:VirtualizedContainerService.HintSize="1600,62" />
      <sap2010:ViewStateData Id="Assign_40" sap:VirtualizedContainerService.HintSize="1600,62" />
      <sap2010:ViewStateData Id="Assign_67" sap:VirtualizedContainerService.HintSize="1600,62" />
      <sap2010:ViewStateData Id="Assign_109" sap:VirtualizedContainerService.HintSize="1600,62" />
      <sap2010:ViewStateData Id="Assign_91" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_92" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_1" sap:VirtualizedContainerService.HintSize="404.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_1" sap:VirtualizedContainerService.HintSize="1600,300" />
      <sap2010:ViewStateData Id="Assign_110" sap:VirtualizedContainerService.HintSize="1600,62" />
      <sap2010:ViewStateData Id="Assign_131" sap:VirtualizedContainerService.HintSize="1600,62" />
      <sap2010:ViewStateData Id="Assign_83" sap:VirtualizedContainerService.HintSize="1600,62" />
      <sap2010:ViewStateData Id="Assign_45" sap:VirtualizedContainerService.HintSize="1600,62" />
      <sap2010:ViewStateData Id="Assign_96" sap:VirtualizedContainerService.HintSize="1600,62" />
      <sap2010:ViewStateData Id="Assign_75" sap:VirtualizedContainerService.HintSize="1132,60" />
      <sap2010:ViewStateData Id="Assign_80" sap:VirtualizedContainerService.HintSize="1132,60" />
      <sap2010:ViewStateData Id="Assign_81" sap:VirtualizedContainerService.HintSize="1132,60" />
      <sap2010:ViewStateData Id="Assign_82" sap:VirtualizedContainerService.HintSize="955,60" />
      <sap2010:ViewStateData Id="Assign_93" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_45" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="Sequence_43" sap:VirtualizedContainerService.HintSize="486,340">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_11" sap:VirtualizedContainerService.HintSize="516.666666666667,492.666666666667" />
      <sap2010:ViewStateData Id="Sequence_42" sap:VirtualizedContainerService.HintSize="538.666666666667,616.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_111" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_44" sap:VirtualizedContainerService.HintSize="806.666666666667,770.666666666667" />
      <sap2010:ViewStateData Id="Sequence_41" sap:VirtualizedContainerService.HintSize="828.666666666667,894.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_33" sap:VirtualizedContainerService.HintSize="954.666666666667,1048.66666666667" />
      <sap2010:ViewStateData Id="InvokeMethod_8" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="Sequence_34" sap:VirtualizedContainerService.HintSize="239.333333333333,258">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_8" sap:VirtualizedContainerService.HintSize="954.666666666667,410.666666666667" />
      <sap2010:ViewStateData Id="InvokeMethod_9" sap:VirtualizedContainerService.HintSize="954.666666666667,134" />
      <sap2010:ViewStateData Id="Sequence_35" sap:VirtualizedContainerService.HintSize="977,1898">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_9" sap:VirtualizedContainerService.HintSize="1007,2046" />
      <sap2010:ViewStateData Id="If_30" sap:VirtualizedContainerService.HintSize="1132,2194" />
      <sap2010:ViewStateData Id="Sequence_33" sap:VirtualizedContainerService.HintSize="1600,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_95" sap:VirtualizedContainerService.HintSize="256,62" />
      <sap2010:ViewStateData Id="Assign_97" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="CommentOut_6" sap:VirtualizedContainerService.HintSize="256,158" />
      <sap2010:ViewStateData Id="Sequence_36" sap:VirtualizedContainerService.HintSize="278,384">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_34" sap:VirtualizedContainerService.HintSize="1600,538" />
      <sap2010:ViewStateData Id="Path_Validate_2" sap:VirtualizedContainerService.HintSize="1600,22" />
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="1386,62" />
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="1386,62" />
      <sap2010:ViewStateData Id="Append_Line_2" sap:VirtualizedContainerService.HintSize="1238,22" />
      <sap2010:ViewStateData Id="Append_Line_5" sap:VirtualizedContainerService.HintSize="1238,22" />
      <sap2010:ViewStateData Id="Assign_104" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_41" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="If_42" sap:VirtualizedContainerService.HintSize="1238,370" />
      <sap2010:ViewStateData Id="InvokeWorkflow_19" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_119" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_49" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="Sequence_47" sap:VirtualizedContainerService.HintSize="1238,402">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_132" sap:VirtualizedContainerService.HintSize="510,62" />
      <sap2010:ViewStateData Id="Assign_133" sap:VirtualizedContainerService.HintSize="510,62" />
      <sap2010:ViewStateData Id="Assign_112" sap:VirtualizedContainerService.HintSize="510,62" />
      <sap2010:ViewStateData Id="Assign_113" sap:VirtualizedContainerService.HintSize="510,62" />
      <sap2010:ViewStateData Id="Append_Line_11" sap:VirtualizedContainerService.HintSize="510,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_16" sap:VirtualizedContainerService.HintSize="510,22" />
      <sap2010:ViewStateData Id="Assign_124" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_125" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="If_52" sap:VirtualizedContainerService.HintSize="510,219.333333333333" />
      <sap2010:ViewStateData Id="InvokeWorkflow_17" sap:VirtualizedContainerService.HintSize="510,22" />
      <sap2010:ViewStateData Id="Sequence_45" sap:VirtualizedContainerService.HintSize="532,937.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_1" sap:VirtualizedContainerService.HintSize="658,22" />
      <sap2010:ViewStateData Id="Assign_71" sap:VirtualizedContainerService.HintSize="658,62" />
      <sap2010:ViewStateData Id="Assign_23" sap:VirtualizedContainerService.HintSize="510,62" />
      <sap2010:ViewStateData Id="Assign_26" sap:VirtualizedContainerService.HintSize="510,62" />
      <sap2010:ViewStateData Id="Assign_27" sap:VirtualizedContainerService.HintSize="510,62" />
      <sap2010:ViewStateData Id="Assign_105" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_106" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_43" sap:VirtualizedContainerService.HintSize="510,216" />
      <sap2010:ViewStateData Id="InvokeWorkflow_8" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_20" sap:VirtualizedContainerService.HintSize="222,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_9" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_21" sap:VirtualizedContainerService.HintSize="222,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_10" sap:VirtualizedContainerService.HintSize="510,300" />
      <sap2010:ViewStateData Id="Assign_122" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_123" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="If_51" sap:VirtualizedContainerService.HintSize="510,219.333333333333" />
      <sap2010:ViewStateData Id="InvokeWorkflow_11" sap:VirtualizedContainerService.HintSize="510,22" />
      <sap2010:ViewStateData Id="Sequence_10" sap:VirtualizedContainerService.HintSize="532,1307.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_9" sap:VirtualizedContainerService.HintSize="658,1461.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_44" sap:VirtualizedContainerService.HintSize="680,1749.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_46" sap:VirtualizedContainerService.HintSize="1238,1903.33333333333" />
      <sap2010:ViewStateData Id="Sequence_8" sap:VirtualizedContainerService.HintSize="1260,3003.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_8" sap:VirtualizedContainerService.HintSize="1386,3157.33333333333" />
      <sap2010:ViewStateData Id="Sequence_6" sap:VirtualizedContainerService.HintSize="1408,3485.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_19" sap:VirtualizedContainerService.HintSize="1430,3609.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_5" sap:VirtualizedContainerService.HintSize="1600,3763.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_56" sap:VirtualizedContainerService.HintSize="485.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_44" sap:VirtualizedContainerService.HintSize="485.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_51" sap:VirtualizedContainerService.HintSize="337.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_54" sap:VirtualizedContainerService.HintSize="337.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_55" sap:VirtualizedContainerService.HintSize="284.666666666667,62" />
      <sap2010:ViewStateData Id="InvokeMethod_3" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="Sequence_24" sap:VirtualizedContainerService.HintSize="239.333333333333,258">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_5" sap:VirtualizedContainerService.HintSize="284.666666666667,410.666666666667" />
      <sap2010:ViewStateData Id="InvokeMethod_4" sap:VirtualizedContainerService.HintSize="284.666666666667,134" />
      <sap2010:ViewStateData Id="Sequence_23" sap:VirtualizedContainerService.HintSize="306.666666666667,810.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_4" sap:VirtualizedContainerService.HintSize="337.333333333333,963.333333333333" />
      <sap2010:ViewStateData Id="Sequence_25" sap:VirtualizedContainerService.HintSize="359.333333333333,1291.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_17" sap:VirtualizedContainerService.HintSize="485.333333333333,1445.33333333333" />
      <sap2010:ViewStateData Id="Sequence_22" sap:VirtualizedContainerService.HintSize="507.333333333333,1773.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_16" sap:VirtualizedContainerService.HintSize="1238,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_57" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_19" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="If_18" sap:VirtualizedContainerService.HintSize="1238,370" />
      <sap2010:ViewStateData Id="Assign_61" sap:VirtualizedContainerService.HintSize="1238,62" />
      <sap2010:ViewStateData Id="Assign_62" sap:VirtualizedContainerService.HintSize="1238,62" />
      <sap2010:ViewStateData Id="Append_Line_8" sap:VirtualizedContainerService.HintSize="1238,22" />
      <sap2010:ViewStateData Id="Append_Line_9" sap:VirtualizedContainerService.HintSize="1238,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_20" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_130" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_55" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="Sequence_48" sap:VirtualizedContainerService.HintSize="1238,402">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_60" sap:VirtualizedContainerService.HintSize="510,62" />
      <sap2010:ViewStateData Id="Assign_63" sap:VirtualizedContainerService.HintSize="510,62" />
      <sap2010:ViewStateData Id="Append_Line_10" sap:VirtualizedContainerService.HintSize="510,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_13" sap:VirtualizedContainerService.HintSize="510,22" />
      <sap2010:ViewStateData Id="Assign_126" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_127" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="If_53" sap:VirtualizedContainerService.HintSize="510,219.333333333333" />
      <sap2010:ViewStateData Id="InvokeWorkflow_14" sap:VirtualizedContainerService.HintSize="510,22" />
      <sap2010:ViewStateData Id="Sequence_27" sap:VirtualizedContainerService.HintSize="532,733.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_4" sap:VirtualizedContainerService.HintSize="658,22" />
      <sap2010:ViewStateData Id="Assign_72" sap:VirtualizedContainerService.HintSize="658,62" />
      <sap2010:ViewStateData Id="Assign_30" sap:VirtualizedContainerService.HintSize="510,62" />
      <sap2010:ViewStateData Id="Assign_37" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_15" sap:VirtualizedContainerService.HintSize="510,216" />
      <sap2010:ViewStateData Id="Assign_33" sap:VirtualizedContainerService.HintSize="510,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_7" sap:VirtualizedContainerService.HintSize="510,22" />
      <sap2010:ViewStateData Id="Assign_128" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_129" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="If_54" sap:VirtualizedContainerService.HintSize="510,219.333333333333" />
      <sap2010:ViewStateData Id="InvokeWorkflow_12" sap:VirtualizedContainerService.HintSize="510,22" />
      <sap2010:ViewStateData Id="Sequence_15" sap:VirtualizedContainerService.HintSize="532,927.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_14" sap:VirtualizedContainerService.HintSize="658,1081.33333333333" />
      <sap2010:ViewStateData Id="Sequence_26" sap:VirtualizedContainerService.HintSize="680,1369.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_21" sap:VirtualizedContainerService.HintSize="1238,1523.33333333333" />
      <sap2010:ViewStateData Id="Sequence_12" sap:VirtualizedContainerService.HintSize="1260,2920">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_13" sap:VirtualizedContainerService.HintSize="1386,3074">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_13" sap:VirtualizedContainerService.HintSize="1408,3198">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_18" sap:VirtualizedContainerService.HintSize="1430,3322">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_11" sap:VirtualizedContainerService.HintSize="1600,3476">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeMethod_5" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="If_24" sap:VirtualizedContainerService.HintSize="464,288" />
      <sap2010:ViewStateData Id="Sequence_28" sap:VirtualizedContainerService.HintSize="486,412">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_25" sap:VirtualizedContainerService.HintSize="1600,566" />
      <sap2010:ViewStateData Id="Sequence_9" sap:VirtualizedContainerService.HintSize="1622,12824">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_2" sap:VirtualizedContainerService.HintSize="1652.66666666667,12976.6666666667" />
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="1674.66666666667,13202.6666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="1800.66666666667,13356.6666666667" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="1822.66666666667,14232.6666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="1948.66666666667,14386.6666666667" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="1970.66666666667,14736.6666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="2096.66666666667,14890.6666666667" />
      <sap2010:ViewStateData Id="InvokeWorkflow_15" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_26" sap:VirtualizedContainerService.HintSize="464,214" />
      <sap2010:ViewStateData Id="Sequence_29" sap:VirtualizedContainerService.HintSize="486,338">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_27" sap:VirtualizedContainerService.HintSize="2096.66666666667,492" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="2118.66666666667,15914.6666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="2158.66666666667,16314.6666666667" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>