﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow" this:Workflow.documentPath="C:\Users\<USER>\Downloads\Bijlages 250127 LS 5001102 - Invoices for the month January 2025.pdf" this:Workflow.tenantID="https://mingle-ionapi.inforcloudsuite.com/MANDALA_DEM/" this:Workflow.configurationFolder="C:\M3VendorInvoiceProcessing" this:Workflow.promptPath="C:\Users\<USER>\Downloads\Prompt1_Classification_Page_No.txt"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:iad1="clr-namespace:Infor.Activities.Desktop;assembly=Infor.Activities.Desktop"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Web;assembly=Infor.Activities.Web"
 xmlns:iaw1="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:iro="clr-namespace:Infor.RPA.OCR;assembly=Infor.RPA.OCR"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:this="clr-namespace:RehostedWorkflowDesigner"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="documentPath" Type="InArgument(x:String)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="configurationFolder" Type="InArgument(x:String)" />
    <x:Property Name="promptPath" Type="InArgument(x:String)" />
    <x:Property Name="promptPath2" Type="InArgument(x:String)" />
    <x:Property Name="copiedFilePath" Type="InArgument(x:String)" />
    <x:Property Name="subDownloadFolder" Type="InArgument(x:String)" />
    <x:Property Name="customPrompt" Type="InArgument(x:String)" />
    <x:Property Name="customPrompt1" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.Linq.Expressions</x:String>
      <x:String>System.Text.RegularExpressions</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System.IO</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_17">
    <Sequence.Variables>
      <Variable x:TypeArguments="scg:Dictionary(x:String, x:String)" Name="invoiceDictionary" />
      <Variable x:TypeArguments="x:String" Name="pgno" />
    </Sequence.Variables>
    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_18">
      <Sequence.Variables>
        <Variable x:TypeArguments="x:String" Name="promptText" />
        <Variable x:TypeArguments="njl:JToken" Name="values" />
        <Variable x:TypeArguments="x:Int32" Name="responseCode" />
        <Variable x:TypeArguments="x:String" Name="noOfPages" />
        <Variable x:TypeArguments="x:String" Name="prevValue" />
        <Variable x:TypeArguments="x:Int32" Name="count" />
        <Variable x:TypeArguments="x:String" Name="str1" />
        <Variable x:TypeArguments="x:String" Name="currValue" />
        <Variable x:TypeArguments="x:String" Name="pgstr" />
        <Variable x:TypeArguments="x:String" Name="str" />
        <Variable x:TypeArguments="x:String" Name="invoiceNumber" />
      </Sequence.Variables>
      <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Read Text File" sap2010:WorkflowViewState.IdRef="File_Read_1" Source="[promptPath]" Text="[promptText]" />
      <iro:DocumentOC Pages="{x:Null}" ActivityID="f69e5a30-0b3c-4f29-9d8a-1e6947a44cd8" ContinueOnError="True" DisplayName="Get OCR Text" ErrorCode="[responseCode]" ExecuteFile="True" FilePath="[documentPath]" sap2010:WorkflowViewState.IdRef="DocumentOC_1" ResponseObject="[values]" />
      <If Condition="[responseCode = 200]" sap2010:WorkflowViewState.IdRef="If_18">
        <If.Then>
          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_19">
            <Sequence.Variables>
              <Variable x:TypeArguments="x:String" Name="promptRequest" />
              <Variable x:TypeArguments="x:String" Name="IonBody" />
              <Variable x:TypeArguments="x:String" Name="ocrOutput" />
              <Variable x:TypeArguments="njl:JToken" Name="genAIRequestToken" />
              <Variable x:TypeArguments="iru:ResponseObject" Name="genAIResponse" />
              <Variable x:TypeArguments="x:Int32" Name="genAIRespCode" />
              <Variable x:TypeArguments="njl:JToken" Name="out1" />
              <Variable x:TypeArguments="x:String" Name="out2" />
              <Variable x:TypeArguments="njl:JToken" Name="jout" />
              <Variable x:TypeArguments="x:String" Name="vend" />
              <Variable x:TypeArguments="scg:List(x:String)" Name="txtFiles" />
              <Variable x:TypeArguments="x:Boolean" Name="customExists" />
              <Variable x:TypeArguments="x:String" Name="customText" />
              <Variable x:TypeArguments="x:String" Name="genAiVersion" />
              <Variable x:TypeArguments="x:String" Name="genAiModel" />
              <Variable x:TypeArguments="x:String" Name="outputStructure" />
              <Variable x:TypeArguments="x:String" Name="value" />
            </Sequence.Variables>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_38">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[noOfPages]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[values("_metadata")("NumberOfPages").ToString]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_70">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[customText]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">
                  <Literal x:TypeArguments="x:String" Value="" />
                </InArgument>
              </Assign.Value>
            </Assign>
            <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_1" IsValid="[customExists]" Path="[customPrompt]" />
            <If Condition="[customExists]" sap2010:WorkflowViewState.IdRef="If_21">
              <If.Then>
                <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Read Text File" sap2010:WorkflowViewState.IdRef="File_Read_3" Source="[customPrompt]" Text="[customText]" />
              </If.Then>
            </If>
            <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_1">
              <iad:CommentOut.Activities>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_17">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[promptText.Replace("{text}",values.tostring)]</InArgument>
                  </Assign.Value>
                </Assign>
              </iad:CommentOut.Activities>
            </iad:CommentOut>
            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_43">
              <Assign sap2010:WorkflowViewState.IdRef="Assign_89">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[genAiVersion]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[miscValues("genAiVersion").ToString]</InArgument>
                </Assign.Value>
              </Assign>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_90">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[genAiModel]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[miscValues("genAiModel").ToString]</InArgument>
                </Assign.Value>
              </Assign>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_69">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[promptText.Replace("{notes}",customText.tostring)]</InArgument>
                </Assign.Value>
              </Assign>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_18">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[promptRequest.Replace("'","\'")]</InArgument>
                </Assign.Value>
              </Assign>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_97">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[outputStructure]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[promptRequest.Split(New String() {"====="}, StringSplitOptions.None)(1)]</InArgument>
                </Assign.Value>
              </Assign>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_98">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[promptRequest.Split(New String() {"====="}, StringSplitOptions.None)(0)]</InArgument>
                </Assign.Value>
              </Assign>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_99">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[value]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[Regex.Replace(Values.ToString(), "[^A-Za-z0-9\s!@#$%^&amp;*()_+={}\[\]:;,'&lt;&gt;.?//|-]", String.Empty).Replace("'","\'")]</InArgument>
                </Assign.Value>
              </Assign>
              <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Latest Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_3" Template="{}{&#xA;  'model': '{{%model%}}',&#xA;  'version': '{{%version%}}',&#xA;  'temperature': 0,&#xA;  'max_response': 8192,&#xA;  'stop_sequence': [&#xA;    'Explanations',&#xA;    'Explanation'&#xA;  ],&#xA;  'system': 'You are an expert document based question answering tool. You will be provided with the raw text of the document using textract. You are to identify the page numbers for a specific vendor where and all there are invoice related pages available. You will also be provided with some instructions on how to identify the correct page numbers for a given vendor from the document. Make sure to understand the provided example to correctly arrive at the invoice page numbers. There will be only 1 vendor on a give page. Make sure you respond without any code blocks in your response.',&#xA;&#xA;  'messages': [&#xA;      {&#xA;        'role': 'user',&#xA;        'content': [&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%prompt%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%ocrText%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Use the following output format for your response. Your response should only have the JSON without code blocks as the response. Do not include any of your analysis statements before or after the JSON in the response. Do not add any additional objects to the JSON that are not present in the following Output Format Structure.'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%outputStructure%}}'&#xA;          }&#xA;        ]&#xA;      },&#xA;      {&#xA;        'role': 'assistant',&#xA;        'content': [&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Based on the invoice document, here are the extracted values in the requested JSON format without any code blocks:'&#xA;          }&#xA;        ]&#xA;      }&#xA;  ]&#xA;}&#xA;" Text="[IonBody]">
                <ias:Template_Apply.Values>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="8">
                      <x:String>prompt</x:String>
                      <x:String>model</x:String>
                      <x:String>version</x:String>
                      <x:String>ocrText</x:String>
                      <x:String>outputStructure</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="8">
                      <x:String>promptRequest</x:String>
                      <x:String>genAiModel</x:String>
                      <x:String>genAiVersion</x:String>
                      <x:String>value</x:String>
                      <x:String>outputStructure</x:String>
                    </scg:List>
                  </scg:List>
                </ias:Template_Apply.Values>
              </ias:Template_Apply>
            </Sequence>
            <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_2">
              <iad:CommentOut.Activities>
                <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_1" Template="{}{ &#xA;'config': { &#xA;'temperature': 0, &#xA;'max_response':8192,&#xA;'stop_sequence': [&#xA;                      'Explanations','Explanation'&#xA;                ]   &#xA;}, &#xA;'prompt':'{{%prompt%}}',&#xA; 'model': '{{%model%}}',   'version': '{{%version%}}'&#xA;" Text="[IonBody]">
                  <ias:Template_Apply.Values>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>prompt</x:String>
                        <x:String>model</x:String>
                        <x:String>version</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>promptRequest</x:String>
                        <x:String>genAiModel</x:String>
                        <x:String>genAiVersion</x:String>
                      </scg:List>
                    </scg:List>
                  </ias:Template_Apply.Values>
                </ias:Template_Apply>
              </iad:CommentOut.Activities>
            </iad:CommentOut>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_19">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[ocrOutput]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[values.toString.Replace("'","")]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_21">
              <Assign.To>
                <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:String)">[invoiceDictionary]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="scg:Dictionary(x:String, x:String)">[New Dictionary (Of String, String)]</InArgument>
              </Assign.Value>
            </Assign>
            <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_5" JTokenObject="[genAIRequestToken]" JTokenString="[IonBody]" />
            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_2" PostData="[genAIRequestToken.tostring]" Response="[genAIResponse]" ResponseCode="[genAIRespCode]" Url="[tenantID + &quot;GENAI/chatsvc/api/v1/messages&quot;]">
              <iai:IONAPIRequestWizard.Headers>
                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>x-infor-logicalidprefix</x:String>
                    <x:String>Accept</x:String>
                  </scg:List>
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>lid://infor.colemanddp</x:String>
                    <x:String>application/json</x:String>
                  </scg:List>
                </scg:List>
              </iai:IONAPIRequestWizard.Headers>
              <iai:IONAPIRequestWizard.QueryParameters>
                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                  <scg:List x:TypeArguments="x:String" Capacity="0" />
                  <scg:List x:TypeArguments="x:String" Capacity="0" />
                </scg:List>
              </iai:IONAPIRequestWizard.QueryParameters>
            </iai:IONAPIRequestWizard>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_22">
              <Assign.To>
                <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(genAIResponse.ReadAsText)]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_23">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[out2]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[out1("content").ToString.Replace("\n","")]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_24">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[out2]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[(out2.ToString.Replace("None","''")).Replace("''''","''")]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_95">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[out2]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[out2.Substring(out2.Indexof("{"c),out2.LastIndexOf("}"c) - out2.Indexof("{"c)+1)]</InArgument>
              </Assign.Value>
            </Assign>
            <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_6" JTokenObject="[jout]" JTokenString="[out2.ToString]" />
            <Assign sap2010:WorkflowViewState.IdRef="Assign_45">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[prevValue]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">
                  <Literal x:TypeArguments="x:String" Value="" />
                </InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_46">
              <Assign.To>
                <OutArgument x:TypeArguments="x:Int32">[count]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:Int32">1</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_47">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[str1]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">0</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_60">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[vend]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">
                  <Literal x:TypeArguments="x:String" Value="" />
                </InArgument>
              </Assign.Value>
            </Assign>
            <ForEach x:TypeArguments="njl:JProperty" DisplayName="ForEach&lt;JProperty&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_9" Values="[(JObject.Parse(jout.ToString)).Properties()]">
              <ActivityAction x:TypeArguments="njl:JProperty">
                <ActivityAction.Argument>
                  <DelegateInArgument x:TypeArguments="njl:JProperty" Name="item" />
                </ActivityAction.Argument>
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_22">
                  <Sequence.Variables>
                    <Variable x:TypeArguments="x:String" Name="page" />
                  </Sequence.Variables>
                  <If Condition="[item.value(0).ToString = &quot;Invoice&quot;]" sap2010:WorkflowViewState.IdRef="If_14">
                    <If.Then>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_23">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_28">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[page]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[item.value(1)(0).ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_24">
                          <Sequence.Variables>
                            <Variable x:TypeArguments="x:String" Name="screenshotPath" />
                            <Variable x:TypeArguments="x:String" Name="base64string" />
                            <Variable x:TypeArguments="x:String" Name="outfile" />
                            <Variable x:TypeArguments="x:String" Name="promptText1" />
                            <Variable x:TypeArguments="x:String" Name="promptRequest1" />
                            <Variable x:TypeArguments="x:String" Name="IonBody1" />
                            <Variable x:TypeArguments="njl:JToken" Name="genAIRequestToken1" />
                            <Variable x:TypeArguments="iru:ResponseObject" Name="genAIResponse1" />
                            <Variable x:TypeArguments="njl:JToken" Name="out3" />
                            <Variable x:TypeArguments="x:String" Name="out4" />
                            <Variable x:TypeArguments="njl:JToken" Name="jout1" />
                            <Variable x:TypeArguments="x:String" Name="customText1" />
                            <Variable x:TypeArguments="x:Boolean" Name="customExists1" />
                          </Sequence.Variables>
                          <iaw:OpenBrowser ErrorCode="{x:Null}" Browser="Chrome" ContinueOnError="True" DisplayName="Open Browser" sap2010:WorkflowViewState.IdRef="OpenBrowser_1" PrivateBrowsingEnabled="False" URL="https://www.google.com/" ZoomPercentage="60" />
                          <iaw:NavigateTo ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Navigate To" sap2010:WorkflowViewState.IdRef="NavigateTo_1" URL="[Path.GetDirectoryName(documentPath) + &quot;/&quot; + Uri.EscapeDataString(Path.GetFileName(documentPath)) + &quot;#page=&quot;+page]" WaitAfter="0" WaitBefore="0" ZoomPercentage="100" />
                          <iaw:MaximizeWindow ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Maximize Window" sap2010:WorkflowViewState.IdRef="MaximizeWindow_1" WaitAfter="0" WaitBefore="0" />
                          <iad1:SendKeys ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Send Keys" sap2010:WorkflowViewState.IdRef="SendKeys_1" IsWeb="False" Text="^-" WaitAfter="0" WaitBefore="0" />
                          <iad1:SendKeys ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Send Keys" sap2010:WorkflowViewState.IdRef="SendKeys_3" IsWeb="False" Text="^-" WaitAfter="0" WaitBefore="1000" />
                          <iad1:SendKeys ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Send Keys" sap2010:WorkflowViewState.IdRef="SendKeys_2" IsWeb="False" Text="^-" WaitAfter="0" WaitBefore="1000" />
                          <iaw:ScreenShot ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Take Screenshot" sap2010:WorkflowViewState.IdRef="ScreenShot_1" SaveLocation="[screenshotPath]" WaitAfter="0" WaitBefore="10000" />
                          <ias:FileToBase64 ErrorCode="{x:Null}" Base64="[base64string]" ContinueOnError="True" DisplayName="File to Base64 String" FilePath="[screenshotPath]" sap2010:WorkflowViewState.IdRef="FileToBase64_1" />
                          <iaw:CloseBrowser ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Close Browser" sap2010:WorkflowViewState.IdRef="CloseBrowser_1" />
                          <ias:File_Create ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Create File" sap2010:WorkflowViewState.IdRef="File_Create_1" Name="[item.name.ToString +&quot;-&quot;+ item.value(1)(0).ToString + &quot;.txt&quot;]" OutputFile="[outfile]" Target="[configurationFolder + &quot;\InProgress&quot;]" />
                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="[base64string]" Source="[outfile]" />
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_87">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[customText1]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">
                                <Literal x:TypeArguments="x:String" Value="" />
                              </InArgument>
                            </Assign.Value>
                          </Assign>
                          <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_2" IsValid="[customExists1]" Path="[customPrompt1]" />
                          <If Condition="[customExists1]" sap2010:WorkflowViewState.IdRef="If_22">
                            <If.Then>
                              <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Read Text File" sap2010:WorkflowViewState.IdRef="File_Read_4" Source="[customPrompt1]" Text="[customText1]" />
                            </If.Then>
                          </If>
                          <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Read Text File" sap2010:WorkflowViewState.IdRef="File_Read_2" Source="[promptPath2]" Text="[promptText1]" />
                          <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_3">
                            <iad:CommentOut.Activities>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_29">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[promptRequest1]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[promptText1.Replace("{text}",values.tostring)]</InArgument>
                                </Assign.Value>
                              </Assign>
                            </iad:CommentOut.Activities>
                          </iad:CommentOut>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_44">
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_88">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[promptRequest1]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[promptText1.Replace("{notes}",customText1.tostring)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_66">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[promptRequest1]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[promptRequest1.Replace("{prompt1_response}",item.tostring)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_30">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[promptRequest1]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[promptRequest1.Replace("'","\'")]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_100">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[outputStructure]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[promptRequest1.Split(New String() {"====="}, StringSplitOptions.None)(1)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_101">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[promptRequest1]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[promptRequest1.Split(New String() {"====="}, StringSplitOptions.None)(0)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_102">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[value]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[Regex.Replace(Values.ToString(), "[^A-Za-z0-9\s!@#$%^&amp;*()_+={}\[\]:;,'&lt;&gt;.?//|-]", String.Empty).Replace("'","\'")]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Latest Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_4" Template="{}{&#xA;  'model': '{{%model%}}',&#xA;  'version': '{{%version%}}',&#xA;  'temperature': 0,&#xA;  'max_response': 8192,&#xA;  'stop_sequence': [&#xA;    'Explanations',&#xA;    'Explanation'&#xA;  ],&#xA;  'system': 'You are an expert document based Question-Answering tool. Your task is to answer the 2 questions using the image of only the first page in the document as reference for layout and empty values, but the extraction of values must be from the raw text. Ignore brevity and it is mandatory to include all the requested values from all the pages as in the raw text in your response. In the provided image you will see 1/N. N indicating number of pages in the full document. This means that you should look for the requested values in the question from all those N pages. Without missing a single one. The content of these pages will be available in the raw text indicated by PageNo value.',&#xA;  'messages': [&#xA;      {&#xA;        'role': 'user',&#xA;        'content': [&#xA;          {&#xA;            'type': 'image',&#xA;            'data': 'data:image/png;base64,{{%base64string%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Read the following instructions'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%prompt%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%ocrText%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Use the following output format for your response. Your response should only have the JSON without code blocks as the response. Do not include any of your analysis statements before or after the JSON in the response. Do not add any additional objects to the JSON that are not present in the following Output Format Structure.'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%outputStructure%}}'&#xA;          }&#xA;        ]&#xA;      },&#xA;      {&#xA;        'role': 'assistant',&#xA;        'content': [&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Based on the invoice document, here are the extracted values in the requested JSON format without any code blocks:'&#xA;          }&#xA;        ]&#xA;      }&#xA;  ]&#xA;}&#xA;" Text="[IonBody1]">
                              <ias:Template_Apply.Values>
                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                  <scg:List x:TypeArguments="x:String" Capacity="8">
                                    <x:String>prompt</x:String>
                                    <x:String>base64string</x:String>
                                    <x:String>model</x:String>
                                    <x:String>version</x:String>
                                    <x:String>ocrText</x:String>
                                    <x:String>outputStructure</x:String>
                                  </scg:List>
                                  <scg:List x:TypeArguments="x:String" Capacity="8">
                                    <x:String>promptRequest1</x:String>
                                    <x:String>base64string</x:String>
                                    <x:String>genAiModel</x:String>
                                    <x:String>genAiVersion</x:String>
                                    <x:String>value</x:String>
                                    <x:String>outputStructure</x:String>
                                  </scg:List>
                                </scg:List>
                              </ias:Template_Apply.Values>
                            </ias:Template_Apply>
                          </Sequence>
                          <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_4">
                            <iad:CommentOut.Activities>
                              <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_2" Template="{}{ &#xA;'config': { &#xA;'temperature': 0, &#xA;'max_response':8192,&#xA;'stop_sequence': [&#xA;                      'Explanations','Explanation'&#xA;                ]   &#xA;}, &#xA;'prompt':'{{%prompt%}}',&#xA; 'model': '{{%model%}}',   'version': '{{%version%}}','encoded_image': 'data:image/png;base64,{{%base64string%}}'}&#xA;" Text="[IonBody1]">
                                <ias:Template_Apply.Values>
                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>prompt</x:String>
                                      <x:String>base64string</x:String>
                                      <x:String>model</x:String>
                                      <x:String>version</x:String>
                                    </scg:List>
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>promptRequest1</x:String>
                                      <x:String>base64string</x:String>
                                      <x:String>genAiModel</x:String>
                                      <x:String>genAiVersion</x:String>
                                    </scg:List>
                                  </scg:List>
                                </ias:Template_Apply.Values>
                              </ias:Template_Apply>
                            </iad:CommentOut.Activities>
                          </iad:CommentOut>
                          <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_7" JTokenObject="[genAIRequestToken1]" JTokenString="[IonBody1]" />
                          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_3" PostData="[genAIRequestToken1]" Response="[genAIResponse1]" ResponseCode="[genAIRespCode]" Url="[tenantID + &quot;GENAI/chatsvc/api/v1/messages&quot;]">
                            <iai:IONAPIRequestWizard.Headers>
                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                  <x:String>x-infor-logicalidprefix</x:String>
                                  <x:String>Accept</x:String>
                                </scg:List>
                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                  <x:String>lid://infor.colemanddp</x:String>
                                  <x:String>application/json</x:String>
                                </scg:List>
                              </scg:List>
                            </iai:IONAPIRequestWizard.Headers>
                            <iai:IONAPIRequestWizard.QueryParameters>
                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                <scg:List x:TypeArguments="x:String" Capacity="0" />
                                <scg:List x:TypeArguments="x:String" Capacity="0" />
                              </scg:List>
                            </iai:IONAPIRequestWizard.QueryParameters>
                          </iai:IONAPIRequestWizard>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_31">
                            <Assign.To>
                              <OutArgument x:TypeArguments="njl:JToken">[out3]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(genAIResponse1.ReadAsText)]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_32">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[out4]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[out3("content").ToString.Replace("\n","")]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_33">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[out4]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[(out4.ToString.Replace("None","''")).Replace("''''","''")]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_96">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[out4]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[out4.Substring(out4.Indexof("{"c),out4.LastIndexOf("}"c) - out4.Indexof("{"c)+1)]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_8" JTokenObject="[jout1]" JTokenString="[out4.ToString]" />
                          <ForEach x:TypeArguments="njl:JProperty" DisplayName="ForEach&lt;JProperty&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_11" Values="[(JObject.Parse(jout1.ToString)).Properties()]">
                            <ActivityAction x:TypeArguments="njl:JProperty">
                              <ActivityAction.Argument>
                                <DelegateInArgument x:TypeArguments="njl:JProperty" Name="item2" />
                              </ActivityAction.Argument>
                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_25">
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_26">
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_36">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[currValue]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[item2.value(0).ToString]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <If Condition="[CInt(currValue) &lt;= CInt(noOfPages)]" sap2010:WorkflowViewState.IdRef="If_15">
                                    <If.Then>
                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_27">
                                        <If Condition="[count = 1]" sap2010:WorkflowViewState.IdRef="If_16">
                                          <If.Then>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_39">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[prevValue]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">[currValue]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                          </If.Then>
                                          <If.Else>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_28">
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_40">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[str]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">
                                                    <Literal x:TypeArguments="x:String" Value="" />
                                                  </InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_50">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[pgstr]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[(CInt(str1.Split("-"c)(str1.Split("-"c).length-1)) + 1).ToString]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <ForEach x:TypeArguments="x:Int32" sap2010:WorkflowViewState.IdRef="ForEach`1_12" Values="[Enumerable.Range(CInt(pgstr), CInt(currValue) - CInt(pgstr))]">
                                                <ActivityAction x:TypeArguments="x:Int32">
                                                  <ActivityAction.Argument>
                                                    <DelegateInArgument x:TypeArguments="x:Int32" Name="i" />
                                                  </ActivityAction.Argument>
                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_29">
                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_43">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="x:String">[str]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="x:String">[str + i.ToString + "-"]</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                  </Sequence>
                                                </ActivityAction>
                                              </ForEach>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_44">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[str1]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[str.SubString(0,str.length-1)]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_48">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[prevValue]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[currValue]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_8" MethodName="Add">
                                                <InvokeMethod.TargetObject>
                                                  <InArgument x:TypeArguments="scg:Dictionary(x:String, x:String)">[invoiceDictionary]</InArgument>
                                                </InvokeMethod.TargetObject>
                                                <InArgument x:TypeArguments="x:String">[invoiceNumber + "~" + vend+ "~" + pgno + "~"]</InArgument>
                                                <InArgument x:TypeArguments="x:String">[str1]</InArgument>
                                              </InvokeMethod>
                                            </Sequence>
                                          </If.Else>
                                        </If>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_42">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:Int32">[count]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:Int32">[count+1]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_56">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[invoiceNumber]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">[item2.name.ToString]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_68">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[invoiceNumber]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">[invoiceNumber.Replace("\","").Replace("/","")]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_59">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[vend]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">[item.Name.ToString]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_64">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[pgno]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">
                                              <Literal x:TypeArguments="x:String" Value="" />
                                            </InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <ForEach x:TypeArguments="x:Object" DisplayName="ForEach&lt;Object&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_16" Values="[item2.Value]">
                                          <ActivityAction x:TypeArguments="x:Object">
                                            <ActivityAction.Argument>
                                              <DelegateInArgument x:TypeArguments="x:Object" Name="item3" />
                                            </ActivityAction.Argument>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_63">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[pgno]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">[pgno + item3.ToString + ","]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                          </ActivityAction>
                                        </ForEach>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_65">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[pgno]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">[pgno.SubString(0,pgno.length-1)]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                      </Sequence>
                                    </If.Then>
                                  </If>
                                </Sequence>
                              </Sequence>
                            </ActivityAction>
                          </ForEach>
                        </Sequence>
                      </Sequence>
                    </If.Then>
                  </If>
                </Sequence>
              </ActivityAction>
            </ForEach>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_52">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[str]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">
                  <Literal x:TypeArguments="x:String" Value="" />
                </InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_53">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[pgstr]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[(CInt(str1.Split("-"c)(str1.Split("-"c).length-1)) + 1).ToString]</InArgument>
              </Assign.Value>
            </Assign>
            <If Condition="[CInt(pgstr) &lt;= CInt(CurrValue) AND CInt(currValue) &lt;= CInt(noOfPages)]" sap2010:WorkflowViewState.IdRef="If_17">
              <If.Then>
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_32">
                  <ForEach x:TypeArguments="x:Int32" sap2010:WorkflowViewState.IdRef="ForEach`1_14" Values="[Enumerable.Range(CInt(pgstr), CInt(noOfPages) - (CInt(currValue) - CInt(pgstr)+1))]">
                    <ActivityAction x:TypeArguments="x:Int32">
                      <ActivityAction.Argument>
                        <DelegateInArgument x:TypeArguments="x:Int32" Name="i" />
                      </ActivityAction.Argument>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_31">
                        <If Condition="[i &lt;= CInt(noOfPages)]" sap2010:WorkflowViewState.IdRef="If_19">
                          <If.Then>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_51">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[str]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[str + i.ToString + "-"]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </If.Then>
                        </If>
                      </Sequence>
                    </ActivityAction>
                  </ForEach>
                  <If Condition="[str.Length &gt; 0]" sap2010:WorkflowViewState.IdRef="If_20">
                    <If.Then>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_35">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_54">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[str1]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[str.SubString(0,str.length-1)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_9" MethodName="Add">
                          <InvokeMethod.TargetObject>
                            <InArgument x:TypeArguments="scg:Dictionary(x:String, x:String)">[invoiceDictionary]</InArgument>
                          </InvokeMethod.TargetObject>
                          <InArgument x:TypeArguments="x:String">[invoiceNumber + "~" + vend+ "~" + pgno + "~"]</InArgument>
                          <InArgument x:TypeArguments="x:String">[str1]</InArgument>
                        </InvokeMethod>
                      </Sequence>
                    </If.Then>
                  </If>
                </Sequence>
              </If.Then>
            </If>
            <Switch x:TypeArguments="x:Boolean" DisplayName="Rename .PDF files if any" Expression="[documentPath.contains(&quot;.PDF&quot;)]" sap2010:WorkflowViewState.IdRef="Switch`1_4">
              <Sequence x:Key="True" DisplayName="PDF Sequence" sap2010:WorkflowViewState.IdRef="Sequence_42">
                <Sequence.Variables>
                  <Variable x:TypeArguments="x:String" Name="pdfFileName" />
                  <Variable x:TypeArguments="x:String" Name="copiedFilePath" />
                </Sequence.Variables>
                <Assign DisplayName="Assign pdfFileName" sap2010:WorkflowViewState.IdRef="Assign_84">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[pdfFileName]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[Path.GetFileName(documentPath).replace(".PDF","_copy.pdf")]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:File_Copy ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Copy File" sap2010:WorkflowViewState.IdRef="File_Copy_4" OutputFile="[copiedFilePath]" OverwriteFile="False" Source="[documentPath]" Target="[Path.GetDirectoryName(documentPath)]" TargetFilename="[pdfFileName]" />
                <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_4" Source="[documentPath]" />
                <Assign DisplayName="Assign attachment+_Copy" sap2010:WorkflowViewState.IdRef="Assign_85">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[documentPath]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[documentPath.replace(".PDF","_copy.pdf")]</InArgument>
                  </Assign.Value>
                </Assign>
              </Sequence>
            </Switch>
            <If Condition="[(invoiceDictionary.count)&gt;1]" sap2010:WorkflowViewState.IdRef="If_13">
              <If.Then>
                <iaw1:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From{{&quot;invoiceDictionary&quot;,invoiceDictionary},{&quot;logFile&quot;,logFile},{&quot;successFolder&quot;,subDownloadFolder},{&quot;attachment&quot;,documentPath},{&quot;tenantID&quot;,tenantID}}]" ContinueOnError="True" DisplayName="ProcessDocument" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_2" WorkflowFile="[Environment.GetFolderPath(Environment.SpecialFolder.UserProfile) + &quot;\AppData\Local\InforRPA\M3InvoiceProcessingGenAIV2&quot;+&quot;\ProcessDocument.xaml&quot;]" />
              </If.Then>
              <If.Else>
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_34">
                  <Sequence.Variables>
                    <Variable x:TypeArguments="x:String" Name="pdfFileName" />
                  </Sequence.Variables>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_61">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[pdfFileName]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[Path.GetFileName(documentPath).replace(".pdf",invoiceNumber + "~" + vend+ "~" + pgno +"~"+String.Join("-", Enumerable.Range(1, CInt(noOfPages)))+".pdf")]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_86">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[pdfFileName]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(pdfFileName, "[:#&amp;@`'*/\?&lt;&gt;|""]", "_")]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <ias:File_Copy ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Copy File" sap2010:WorkflowViewState.IdRef="File_Copy_2" OutputFile="[copiedFilePath]" OverwriteFile="False" Source="[documentPath]" Target="[subDownloadFolder]" TargetFilename="[pdfFileName]" />
                  <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_1" Source="[documentPath]" />
                </Sequence>
              </If.Else>
            </If>
          </Sequence>
        </If.Then>
      </If>
    </Sequence>
    <sads:DebugSymbol.Symbol>d1FDOlxVc2Vyc1xkbWl0dGFwYWxsaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYyXFNwbGl0LnhhbWzLAgFyAdYBAQUBygIB5wIBBAGCAwHEAwEDAe8BAaYCAQJTA/gGDgIBAVgF9gYQAgECZgdmuQEDAegDZwdnnwIDAeADaAf1BgwCAQNmqAFmtgEDAesDZpQBZqIBAwHpA2eSAmecAgMB5gNnlgFnpgEDAeQDZ8MBZ9MBAwHiA2czZ1kDAeEDaBVoKwIBBGoL8wYWAgEGfg2FARYDAdwDhgENjwEWAwHYA5ABDZAByQEDAdMDkQENlQESAwHLA5YBDaEBHgMBygOiAQ3vARgDAaQD8AENgwIeAwGjA4QCDYsCFgMBnwOMAg2TAhYDAZsDlAINlALfAQMBlgOVAg2oAicDAY4DqQINsAIWAwGKA7ECDbgCFgMBhgO5Ag3AAhYDAYEDwQINyAIWAwH5AskCDckC2AEDAfQCygIN0wIWAwHwAtQCDdsCFgMB7ALcAg3jAhYDAegC5AIN7QIWAwHkAu4CDfgFFwIBbPkFDYIGFgIBaIMGDYoGFgIBYosGDboGEgIBO7sGDdQGFgIBI9UGDfIGEgIBB4MBOIMBZwMB3wOAATmAAUQDAd0DjAETjAFCAwHbA4gBOYgBRQMB2QOQAaABkAGwAQMB1gOQAbYBkAHGAQMB1AORARuRASsDAcwDkwERkwHFAQMBzgOjAQ+qARgDAcUDqwEPsgEYAwHAA7MBD7oBGAMBugO7AQ/CARgDAbUDwwEPygEYAwGxA8sBD9IBGAMBrQPTAQ/aARgDAakD2wEP7gEkAwGlA4kCOIkCWQMBogOGAjmGAkQDAaADkQJSkQJ2AwGeA44CU44CZgMBnAOUAtEBlALcAQMBmQOUAq4BlALDAQMBlwOVAv0BlQKbAgMBlQOVAsQClQLVAgMBkwOVAqUClQK2AgMBkQOVAtoClQKSAwMBjwOuAjquAmIDAY0DqwI7qwJBAwGLA7YCOLYCYwMBiQOzAjmzAj8DAYcDvgI4vgJzAwGEA7sCObsCPwMBggPGAjjGAooBAwH8AsMCOcMCPwMB+gLJAsQByQLVAQMB9wLJAq4ByQK2AQMB9QLQAhPQAkIDAfMCzAI5zAJEAwHxAtkCN9kCOAMB7wLWAjjWAj8DAe0C4QI44QI5AwHrAt4COd4CPwMB6QLqAhPqAkIDAecC5gI55gI/AwHlAu4CkgHuAsEBAwHjAvMCEfYFHAIBbf8FE/8FQgIBa/sFOfsFPgIBaYgGOIgGegIBZYUGOYUGQAIBY4sGG4sGagIBPI0GEbgGHAIBQbsGY7sGjgECASS8Bg/TBhoCASbVBhvVBj0CAQjXBhHXBvIEAgEg2gYR8AYcAgEKkwG0AZMBwgEDAdEDkwGeAZMBrgEDAc8DqAE6qAFfAwHIA6UBO6UBSQMBxgOwATqwAV0DAcMDrQE7rQFHAwHBA7gBOrgBbQMBvQO1ATu1AUoDAbsDwAE6wAFbAwG4A70BO70BSgMBtgPIATrIAYMBAwG0A8UBO8UBTAMBsgPQATrQAYMBAwGwA80BO80BSgMBrgPYATrYAbcBAwGsA9UBO9UBQgMBqgPbAe8Q2wH6EAMBpwPbAbQB2wHpEAMBpgP3AhP1BRgCAW6OBhOiBh0CAVWjBhO3BhgCAULBBhHIBhoCATfJBhHJBq8CAgEvygYRygayAQIBLMsGEdIGGgIBJ9cGY9cG0gICASLXBsgD1wbvBAIBId4GE+UGHAIBHOYGE+0GHAIBF+4GE+4GngICAQ7vBhPvBrQBAgEL9wIh9wJRAgFv+QIX8wUiAgFwjgZsjgbCAQIBYZMGF6AGIgIBVqMGIaMGNgIBQ6UGF7UGIgIBRcYGPMYGeAIBOsMGPcMGSgIBOMkGngHJBrABAgE1yQbmAckGjQICATTJBs4ByQbeAQIBMskGnQLJBqwCAgEwygafAcoGrwECAS3QBjzQBmYCASrNBj3NBksCASjjBj7jBtgBAgEf4AY/4AZMAgEd6wY+6wadAQIBGugGP+gGTAIBGO4GnwHuBrEBAgEV7gbnAe4G/AECARPuBs8B7gbfAQIBEe4GjALuBpsCAgEP7wahAe8GsQECAQz6AhmBAyIDAd8CggMZ8gUkAgFxlAYZnwYeAgFXpgYZrQYiAgFPrgYZtAYoAgFG/wJE/wJfAwHiAvwCRfwCSwMB4AKSAxuSA4cCAwHcApMDG5MD4QIDAdcClAMblAPLAQMB1AKVAxuVA9IBAwHQApYDG5YD1QEDAcwClwMblwPVAQMByAKYAxuYA+cBAwHDApkDG5kD5AEDAb4CmgMbmgOoAQMBvQKbAxubA80CAwG3ApwDG5wDzQEDAbICnQMbpgMkAwGuAqcDG6cD2QEDAakCqAMbrAMgAwGhAq0DG60DzwEDAZwCrgMbuQMsAwGbAroDG4EEJgMB+wGCBBuXBCwDAfoBmAQbmATvAQMB9QGZBBusBDUDAe0BrQQbtAQkAwHpAbUEG7wEJAMB5QG9BBvEBCQDAeABxQQbzAQkAwHYAc0EG80E5wEDAdMBzgQb8QUlAgFylAYnlAZCAgFYlgYdnQYmAgFbqwZEqwZjAgFSqAZFqAZLAgFQswZCswZIAgFNsgZCsgZwAgFJsAZesAZxAgFHkgPXAZID8AEDAd4CkgOAApIDhAIDAd0CkwOkAZMDrAIDAdsCkwPZApMD3gIDAdoCkwO3ApMDugIDAdkCkwPGApMDyQIDAdgClAO2AZQDuQEDAdYClAPFAZQDyAEDAdUClQO9AZUDwAEDAdMClQOuAZUDsgEDAdIClQPMAZUDzwEDAdEClgO9AZYDwAEDAc8ClgOuAZYDsgEDAc4ClgPMAZYD0gEDAc0ClwO9AZcDwAEDAcsClwOuAZcDsgEDAcoClwPMAZcD0gEDAckCmAPOAZgD0QEDAccCmAPdAZgD5AEDAcYCmAOxAZgDwwEDAcQCmQNJmQNZAwHBApkDngGZA7ABAwG/ApsDhgKbA5ECAwG7ApsDmQKbA8oCAwG5ApsDpwGbA/oBAwG4ApwDpwGcA7cBAwG1ApwDvwGcA8oBAwGzAqMDIaMDUAMBsQKfA0efA1QDAa8CpwOuAacDvwEDAawCpwPFAacD1gEDAaoCqAMpqAM6AwGiAqoDH6oD1QEDAaQCrQO9Aa0DzAEDAZ8CrQOoAa0DtwEDAZ0CuwMdwgMmAwGVAsMDHcoDJgMBkQLLAx3SAyYDAYwC0wMd2gMmAwGIAtsDHeIDJgMBhALjAx3qAyYDAYAC6wMdgAQyAwH8AZgE4AGYBOwBAwH4AZgEvAGYBNIBAwH2AZkEiwKZBKECAwH0AZkEywKZBNwCAwHyAZkEqwKZBL0CAwHwAZkE4QKZBJkDAwHuAbIESLIEcQMB7AGvBEmvBE8DAeoBugRGugRxAwHoAbcER7cETQMB5gHCBEbCBIEBAwHjAb8ER78ETQMB4QHKBEbKBJgBAwHbAccER8cETQMB2QHNBNMBzQTkAQMB1gHNBLwBzQTFAQMB1AHOBKEBzgTRAQMB0gHTBB/vBSoCAXObBkibBmACAV6YBkmYBk4CAVyqA8MBqgPSAQMBpwKqA6wBqgO9AQMBpQLAA0jAA30DAZgCvQNJvQNZAwGWAsgDSMgDhAEDAZQCxQNJxQNZAwGSAtADSNADagMBjwLNA0nNA1kDAY0C2ANI2AOSAQMBiwLVA0nVA1oDAYkC4ANI4AOSAQMBhwLdA0ndA1kDAYUC6ANI6APFAQMBgwLlA0nlA1ADAYEC6wPhE+sD7RMDAf4B6wPCAesD2xMDAf0B1AQh7gUsAgF01QQj3AQsAwHOAd0EI+0FKAIBddoETtoEZwMB0QHXBE/XBFoDAc8B3QQx3QRaAgF23wQn6wUyAgF54AQpqQUuAwGeAaoFKbEFMgMBmQGyBSm5BTIDAZUBugUpwQUyAwGQAcIFKckFMgMBjAHKBSnTBTIDAYgB1AUp4gUzAwGAAeMFKeoFMgIBeuAEN+AERAMBnwHiBC3pBDYDAckB7AQtpwU4AwGhAa8FU68FXAMBnAGsBVSsBVsDAZoBtwVUtwVpAwGYAbQFVbQFZAMBlgG/BVS/BYMBAwGTAbwFVbwFZAMBkQHHBVTHBWgDAY8BxAVVxAVbAwGNAdAFL9AFXgMBiwHMBVXMBVsDAYkB1AWnAdQFtgEDAYcB2QUt4AU2AwGBAegFVOgFdQIBfeUFVeUFWwIBe+cEWOcEYwMBzAHkBFnkBGQDAcoB7QQv9gQ4AwHFAfcEL/4EOAMBvwH/BC+PBTkDAbYBkAUvlwU4AwGwAZgFL58FOAMBqwGgBS+mBT4DAaIB3gVY3gV1AwGEAdsFWdsFXwMBggHzBDXzBGQDAcgB7wRb7wRgAwHGAfwEWvwEnAEDAcIB+QRb+QRiAwHAAf8EiAH/BMgBAwG+AYQFM40FPgMBtwGVBVqVBXkDAbMBkgVbkgVhAwGxAZ0FWp0FZQMBrgGaBVuaBWYDAawBpQVYpQVeAwGpAaQFWKQFhgEDAaUBogV0ogWHAQMBowGFBTWMBT4DAbgBigVgigV4AwG7AYcFYYcFZgMBuQE=</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="File_Read_1" sap:VirtualizedContainerService.HintSize="1177,22" />
      <sap2010:ViewStateData Id="DocumentOC_1" sap:VirtualizedContainerService.HintSize="1177,22" />
      <sap2010:ViewStateData Id="Assign_38" sap:VirtualizedContainerService.HintSize="1030,60" />
      <sap2010:ViewStateData Id="Assign_70" sap:VirtualizedContainerService.HintSize="1030,60" />
      <sap2010:ViewStateData Id="Path_Validate_1" sap:VirtualizedContainerService.HintSize="1030,22" />
      <sap2010:ViewStateData Id="File_Read_3" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_21" sap:VirtualizedContainerService.HintSize="1030,208" />
      <sap2010:ViewStateData Id="Assign_17" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="CommentOut_1" sap:VirtualizedContainerService.HintSize="1030,156" />
      <sap2010:ViewStateData Id="Assign_89" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_90" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_69" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_18" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_97" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_98" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_99" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Template_Apply_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_43" sap:VirtualizedContainerService.HintSize="1030,846">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Template_Apply_1" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_2" sap:VirtualizedContainerService.HintSize="1030,118" />
      <sap2010:ViewStateData Id="Assign_19" sap:VirtualizedContainerService.HintSize="1030,60" />
      <sap2010:ViewStateData Id="Assign_21" sap:VirtualizedContainerService.HintSize="1030,60" />
      <sap2010:ViewStateData Id="DeserializeJSON_5" sap:VirtualizedContainerService.HintSize="1030,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_2" sap:VirtualizedContainerService.HintSize="1030,22" />
      <sap2010:ViewStateData Id="Assign_22" sap:VirtualizedContainerService.HintSize="1030,60" />
      <sap2010:ViewStateData Id="Assign_23" sap:VirtualizedContainerService.HintSize="1030,60" />
      <sap2010:ViewStateData Id="Assign_24" sap:VirtualizedContainerService.HintSize="1030,60" />
      <sap2010:ViewStateData Id="Assign_95" sap:VirtualizedContainerService.HintSize="1030,60" />
      <sap2010:ViewStateData Id="DeserializeJSON_6" sap:VirtualizedContainerService.HintSize="1030,22" />
      <sap2010:ViewStateData Id="Assign_45" sap:VirtualizedContainerService.HintSize="1030,60" />
      <sap2010:ViewStateData Id="Assign_46" sap:VirtualizedContainerService.HintSize="1030,60" />
      <sap2010:ViewStateData Id="Assign_47" sap:VirtualizedContainerService.HintSize="1030,60" />
      <sap2010:ViewStateData Id="Assign_60" sap:VirtualizedContainerService.HintSize="1030,60" />
      <sap2010:ViewStateData Id="Assign_28" sap:VirtualizedContainerService.HintSize="828,62" />
      <sap2010:ViewStateData Id="OpenBrowser_1" sap:VirtualizedContainerService.HintSize="806,58" />
      <sap2010:ViewStateData Id="NavigateTo_1" sap:VirtualizedContainerService.HintSize="806,58" />
      <sap2010:ViewStateData Id="MaximizeWindow_1" sap:VirtualizedContainerService.HintSize="806,22" />
      <sap2010:ViewStateData Id="SendKeys_1" sap:VirtualizedContainerService.HintSize="806,22" />
      <sap2010:ViewStateData Id="SendKeys_3" sap:VirtualizedContainerService.HintSize="806,22" />
      <sap2010:ViewStateData Id="SendKeys_2" sap:VirtualizedContainerService.HintSize="806,22" />
      <sap2010:ViewStateData Id="ScreenShot_1" sap:VirtualizedContainerService.HintSize="806,22" />
      <sap2010:ViewStateData Id="FileToBase64_1" sap:VirtualizedContainerService.HintSize="806,22" />
      <sap2010:ViewStateData Id="CloseBrowser_1" sap:VirtualizedContainerService.HintSize="806,22" />
      <sap2010:ViewStateData Id="File_Create_1" sap:VirtualizedContainerService.HintSize="806,22" />
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="806,22" />
      <sap2010:ViewStateData Id="Assign_87" sap:VirtualizedContainerService.HintSize="806,62" />
      <sap2010:ViewStateData Id="Path_Validate_2" sap:VirtualizedContainerService.HintSize="806,22" />
      <sap2010:ViewStateData Id="File_Read_4" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_22" sap:VirtualizedContainerService.HintSize="806,214" />
      <sap2010:ViewStateData Id="File_Read_2" sap:VirtualizedContainerService.HintSize="806,22" />
      <sap2010:ViewStateData Id="Assign_29" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="CommentOut_3" sap:VirtualizedContainerService.HintSize="806,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_88" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_66" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_30" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_100" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_101" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_102" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Template_Apply_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_44" sap:VirtualizedContainerService.HintSize="806,758">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Template_Apply_2" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_4" sap:VirtualizedContainerService.HintSize="806,118" />
      <sap2010:ViewStateData Id="DeserializeJSON_7" sap:VirtualizedContainerService.HintSize="806,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_3" sap:VirtualizedContainerService.HintSize="806,22" />
      <sap2010:ViewStateData Id="Assign_31" sap:VirtualizedContainerService.HintSize="806,62" />
      <sap2010:ViewStateData Id="Assign_32" sap:VirtualizedContainerService.HintSize="806,60" />
      <sap2010:ViewStateData Id="Assign_33" sap:VirtualizedContainerService.HintSize="806,60" />
      <sap2010:ViewStateData Id="Assign_96" sap:VirtualizedContainerService.HintSize="806,60" />
      <sap2010:ViewStateData Id="DeserializeJSON_8" sap:VirtualizedContainerService.HintSize="806,22" />
      <sap2010:ViewStateData Id="Assign_36" sap:VirtualizedContainerService.HintSize="732,60" />
      <sap2010:ViewStateData Id="Assign_39" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_40" sap:VirtualizedContainerService.HintSize="294,60" />
      <sap2010:ViewStateData Id="Assign_50" sap:VirtualizedContainerService.HintSize="294,60" />
      <sap2010:ViewStateData Id="Assign_43" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_29" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_12" sap:VirtualizedContainerService.HintSize="294,332" />
      <sap2010:ViewStateData Id="Assign_44" sap:VirtualizedContainerService.HintSize="294,60" />
      <sap2010:ViewStateData Id="Assign_48" sap:VirtualizedContainerService.HintSize="294,60" />
      <sap2010:ViewStateData Id="InvokeMethod_8" sap:VirtualizedContainerService.HintSize="294,128" />
      <sap2010:ViewStateData Id="Sequence_28" sap:VirtualizedContainerService.HintSize="316,1024">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_16" sap:VirtualizedContainerService.HintSize="585,1172" />
      <sap2010:ViewStateData Id="Assign_42" sap:VirtualizedContainerService.HintSize="584.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_56" sap:VirtualizedContainerService.HintSize="584.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_68" sap:VirtualizedContainerService.HintSize="584.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_59" sap:VirtualizedContainerService.HintSize="584.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_64" sap:VirtualizedContainerService.HintSize="584.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_63" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="ForEach`1_16" sap:VirtualizedContainerService.HintSize="584.666666666667,214.666666666667" />
      <sap2010:ViewStateData Id="Assign_65" sap:VirtualizedContainerService.HintSize="584.666666666667,62" />
      <sap2010:ViewStateData Id="Sequence_27" sap:VirtualizedContainerService.HintSize="607,2163">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_15" sap:VirtualizedContainerService.HintSize="732,2311" />
      <sap2010:ViewStateData Id="Sequence_26" sap:VirtualizedContainerService.HintSize="754,2535">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_25" sap:VirtualizedContainerService.HintSize="776,2659">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_11" sap:VirtualizedContainerService.HintSize="806,2807">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_24" sap:VirtualizedContainerService.HintSize="828,5806.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_23" sap:VirtualizedContainerService.HintSize="850,6032.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_14" sap:VirtualizedContainerService.HintSize="976,6186.66666666667" />
      <sap2010:ViewStateData Id="Sequence_22" sap:VirtualizedContainerService.HintSize="998,6310.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_9" sap:VirtualizedContainerService.HintSize="1030,6463.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_52" sap:VirtualizedContainerService.HintSize="1030,62" />
      <sap2010:ViewStateData Id="Assign_53" sap:VirtualizedContainerService.HintSize="1030,62" />
      <sap2010:ViewStateData Id="Assign_51" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_19" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="Sequence_31" sap:VirtualizedContainerService.HintSize="486,340">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_14" sap:VirtualizedContainerService.HintSize="516.666666666667,492.666666666667" />
      <sap2010:ViewStateData Id="Assign_54" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_9" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Sequence_35" sap:VirtualizedContainerService.HintSize="264,360">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_20" sap:VirtualizedContainerService.HintSize="516.666666666667,514" />
      <sap2010:ViewStateData Id="Sequence_32" sap:VirtualizedContainerService.HintSize="538.666666666667,1170.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_17" sap:VirtualizedContainerService.HintSize="1030,1324.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_84" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="File_Copy_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="File_Delete_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_85" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_42" sap:VirtualizedContainerService.HintSize="264,412">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_4" sap:VirtualizedContainerService.HintSize="1030,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_2" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Assign_61" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_86" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="File_Copy_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="File_Delete_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_34" sap:VirtualizedContainerService.HintSize="264,412">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_13" sap:VirtualizedContainerService.HintSize="1030,566" />
      <sap2010:ViewStateData Id="Sequence_19" sap:VirtualizedContainerService.HintSize="1052,11791">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_18" sap:VirtualizedContainerService.HintSize="1177,11939" />
      <sap2010:ViewStateData Id="Sequence_18" sap:VirtualizedContainerService.HintSize="1199,12187">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_17" sap:VirtualizedContainerService.HintSize="1221,12311">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="1261,12431" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>