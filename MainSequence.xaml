﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow" this:Workflow.configurationFolder="C:\M3VendorInvoiceProcessing" this:Workflow.invoiceSource="OutlookClientEmail" this:Workflow.emailAccount="<EMAIL>" this:Workflow.emailFolder="M3" this:Workflow.numberOfEmails="30" this:Workflow.directoriesNames="OutlookDownloads|Failure|InProgress|Success|Logs|Reprocess" this:Workflow.logFolderName="Logs" this:Workflow.invoiceFolderPath="C:\M3VendorInvoiceProcessingGenAI" this:Workflow.colemanAPI="https://mingle-ionapi.inforcloudsuite.com/MANDALA_DEM/APIFLOWS/Invoice_Matching_val_percentage/Invoice_Matching_val_percentage?ionapiRespStyle=sync" this:Workflow.tenantID="https://mingle-ionapi.inforcloudsuite.com/MANDALA_DEM/" this:Workflow.userIdentifier="TO_APRESP" this:Workflow.distributionType="USER" this:Workflow.projectPath="[Environment.GetFolderPath(Environment.SpecialFolder.UserProfile) + &quot;\AppData\Local\InforRPA\M3InvoiceProcessingGenAIV2&quot;]" this:Workflow.maxNotReceivedCount="10" this:Workflow.enableMessageBoxes="True" this:Workflow.poFilterCondition="NA" this:Workflow.poFilterValues="1|4" this:Workflow.datalakeAPILogicalId="infor.ims.imsfromrpastudio" this:Workflow.chargeCode="POPPA" this:Workflow.discountCode="110" this:Workflow.authUser="SUPPLIERAUTHUSER" this:Workflow.imsAPIUrl="https://mingle-ionapi.inforcloudsuite.com/MANDALA_DEM/IONSERVICES/api/ion/messaging/service/v2/message" this:Workflow.extractNumericFromPO="False" this:Workflow.vatCodeConfig="01" this:Workflow.poDiscountsHandlingConfig="False" this:Workflow.matchVendorItemCode="False" this:Workflow.extractFromWidgetDatalake="True" this:Workflow.approvalRequired="False" this:Workflow.checkAmountBussinessRule="Amount_Check" this:Workflow.approvalWorkflow="M3UserApproval" this:Workflow.processExpenseInvoice="True" this:Workflow.ERP="M3" this:Workflow.useGenAIExtraction="True" this:Workflow.processDeliveryNote="False" this:Workflow.GenAIPromptFileURL="https://mingle-ionapi.inforcloudsuite.com/MANDALA_DEM/IDM/api/items/search/item/resource?%24query=%2FGenAIPrompt%5B%40RESOURCENAME%20%3D%20%22GenAIPrompt.txt%22%5D%20SORTBY(%40LASTCHANGEDTS%20DESCENDING)" this:Workflow.processEmails="True" this:Workflow.processFolders="False" this:Workflow.businessRuleForTolerance="DivisionVendorTolerance" this:Workflow.businessRuleForGUID="VendorDivisionGUID" this:Workflow.division="DE1" this:Workflow.company="999" this:Workflow.IONAPI="https://mingle-ionapi.inforcloudsuite.com/MANDALA_DEM/ifsservice/usermgt/v2/users/me" this:Workflow.includeDistribution="True" this:Workflow.businessRuleForDivisionGLCode="NA" this:Workflow.useBusinessRuleForTelerance="False" this:Workflow.enableStagingForExpenseInException="True" this:Workflow.mandateGEOC="False" this:Workflow.SplittingDoc="True" this:Workflow.groupByTransDate="False" this:Workflow.GenAIPromptFileURLForSplit="https://mingle-ionapi.inforcloudsuite.com/MANDALA_DEM/IDM/api/items/search/item/resource?%24query=%2FGenAIPrompt%5B%40RESOURCENAME%20%3D%20%22GenAISplit1.txt%22%5D%20SORTBY(%40LASTCHANGEDTS%20DESCENDING)" this:Workflow.GenAIPromptFileURLForSplit2="https://mingle-ionapi.inforcloudsuite.com/MANDALA_DEM/IDM/api/items/search/item/resource?%24query=%2FGenAIPrompt%5B%40RESOURCENAME%20%3D%20%22GenAISplit2.txt%22%5D%20SORTBY(%40LASTCHANGEDTS%20DESCENDING)" this:Workflow.createInvoiceIrrespectiveTolerance="False" this:Workflow.autoAllocateOpenLines="False" this:Workflow.reprocess="True" this:Workflow.numberOfParts="5" this:Workflow.AutomateValidation="False" this:Workflow.AutomateApproval="False" this:Workflow.SendEmail="False" this:Workflow.BusinessRuleAPResp="NA" this:Workflow.genAiVersion="claude-3-7-sonnet-20250219-v1:0" this:Workflow.genAiModel="CLAUDE" this:Workflow.handleCashDiscount="True" this:Workflow.MandateDeliveryNoteWhenPo="False"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:this="clr-namespace:RehostedWorkflowDesigner"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="configurationFolder" Type="InArgument(x:String)" />
    <x:Property Name="invoiceSource" Type="InArgument(x:String)" />
    <x:Property Name="emailAccount" Type="InArgument(x:String)" />
    <x:Property Name="emailFolder" Type="InArgument(x:String)" />
    <x:Property Name="numberOfEmails" Type="InArgument(x:Int32)" />
    <x:Property Name="directoriesNames" Type="InArgument(x:String)" />
    <x:Property Name="logFolderName" Type="InArgument(x:String)" />
    <x:Property Name="invoiceFolderPath" Type="InArgument(x:String)" />
    <x:Property Name="colemanAPI" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="userIdentifier" Type="InArgument(x:String)" />
    <x:Property Name="distributionType" Type="InArgument(x:String)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="maxNotReceivedCount" Type="InArgument(x:Int32)" />
    <x:Property Name="enableMessageBoxes" Type="InArgument(x:Boolean)" />
    <x:Property Name="poFilterCondition" Type="InArgument(x:String)" />
    <x:Property Name="poFilterValues" Type="InArgument(x:String)" />
    <x:Property Name="datalakeAPILogicalId" Type="InArgument(x:String)" />
    <x:Property Name="chargeCode" Type="InArgument(x:String)" />
    <x:Property Name="discountCode" Type="InArgument(x:String)" />
    <x:Property Name="authUser" Type="InArgument(x:String)" />
    <x:Property Name="imsAPIUrl" Type="InArgument(x:String)" />
    <x:Property Name="extractNumericFromPO" Type="InArgument(x:Boolean)" />
    <x:Property Name="vatCodeConfig" Type="InArgument(x:String)" />
    <x:Property Name="poDiscountsHandlingConfig" Type="InArgument(x:Boolean)" />
    <x:Property Name="matchVendorItemCode" Type="InArgument(x:Boolean)" />
    <x:Property Name="extractFromWidgetDatalake" Type="InArgument(x:Boolean)" />
    <x:Property Name="approvalRequired" Type="InArgument(x:Boolean)" />
    <x:Property Name="checkAmountBussinessRule" Type="InArgument(x:String)" />
    <x:Property Name="approvalWorkflow" Type="InArgument(x:String)" />
    <x:Property Name="processExpenseInvoice" Type="InArgument(x:Boolean)" />
    <x:Property Name="ERP" Type="InArgument(x:String)" />
    <x:Property Name="useGenAIExtraction" Type="InArgument(x:Boolean)" />
    <x:Property Name="processDeliveryNote" Type="InArgument(x:Boolean)" />
    <x:Property Name="GenAIPromptFileURL" Type="InArgument(x:String)" />
    <x:Property Name="processEmails" Type="InArgument(x:Boolean)" />
    <x:Property Name="processFolders" Type="InArgument(x:Boolean)" />
    <x:Property Name="businessRuleForTolerance" Type="InArgument(x:String)" />
    <x:Property Name="businessRuleForGUID" Type="InArgument(x:String)" />
    <x:Property Name="division" Type="InArgument(x:String)" />
    <x:Property Name="company" Type="InArgument(x:String)" />
    <x:Property Name="IONAPI" Type="InArgument(x:String)" />
    <x:Property Name="includeDistribution" Type="InArgument(x:Boolean)" />
    <x:Property Name="businessRuleForDivisionGLCode" Type="InArgument(x:String)" />
    <x:Property Name="useBusinessRuleForTelerance" Type="InArgument(x:Boolean)" />
    <x:Property Name="enableStagingForExpenseInException" Type="InArgument(x:Boolean)" />
    <x:Property Name="mandateGEOC" Type="InArgument(x:Boolean)" />
    <x:Property Name="SplittingDoc" Type="InArgument(x:Boolean)" />
    <x:Property Name="groupByTransDate" Type="InArgument(x:Boolean)" />
    <x:Property Name="GenAIPromptFileURLForSplit" Type="InArgument(x:String)" />
    <x:Property Name="GenAIPromptFileURLForSplit2" Type="InArgument(x:String)" />
    <x:Property Name="createInvoiceIrrespectiveTolerance" Type="InArgument(x:Boolean)" />
    <x:Property Name="autoAllocateOpenLines" Type="InArgument(x:Boolean)" />
    <x:Property Name="reprocess" Type="InArgument(x:Boolean)" />
    <x:Property Name="numberOfParts" Type="InArgument(x:Int32)" />
    <x:Property Name="AutomateValidation" Type="InArgument(x:Boolean)" />
    <x:Property Name="AutomateApproval" Type="InArgument(x:Boolean)" />
    <x:Property Name="SendEmail" Type="InArgument(x:Boolean)" />
    <x:Property Name="BusinessRuleAPResp" Type="InArgument(x:String)" />
    <x:Property Name="genAiVersion" Type="InArgument(x:String)" />
    <x:Property Name="genAiModel" Type="InArgument(x:String)" />
    <x:Property Name="handleCashDiscount" Type="InArgument(x:Boolean)" />
    <x:Property Name="MandateDeliveryNoteWhenPo" Type="InArgument(x:Boolean)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.Workflow</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.Globalization</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>System.Text.RegularExpressions</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.Workflow</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="MainSequence" sap2010:WorkflowViewState.IdRef="Sequence_42">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="logFile" />
      <Variable x:TypeArguments="scg:Dictionary(x:String, scg:List(x:String))" Name="ocrKeysDictionary" />
      <Variable x:TypeArguments="x:Boolean" Name="manualEntryFilePathExist" />
      <Variable x:TypeArguments="s:String[]" Default="[{&quot;Email Subject&quot;,&quot;Email Received time&quot;,&quot;File Name&quot;,&quot;Invoice Number&quot;,&quot;PO Number&quot;,&quot;Invoice Date&quot;,&quot;Sub Total&quot;,&quot;Total&quot;,&quot;Company&quot;,&quot;Vendor Id&quot;,&quot;Vendor name&quot;,&quot;Status&quot;,&quot;Last Run Time&quot;,&quot;Failure Count&quot;,&quot;Is updated by user&quot;,&quot;Tax&quot;,&quot;Shipping Handling charge&quot;,&quot;Discount&quot;}]" Name="manualEntryHeaders" />
      <Variable x:TypeArguments="x:String" Name="notificationFailureCount" />
      <Variable x:TypeArguments="x:Int32" Name="re" />
      <Variable x:TypeArguments="x:String" Name="d1" />
      <Variable x:TypeArguments="x:Int32" Name="datalakeResponseCode" />
      <Variable x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="miscValues" />
      <Variable x:TypeArguments="x:Boolean" Name="GenAIPromptFileExists" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="GenAIOutput" />
      <Variable x:TypeArguments="x:String" Name="GenAIStatus" />
      <Variable x:TypeArguments="njl:JToken" Name="value" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="resp" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="paymentRespCode" />
      <Variable x:TypeArguments="scg:List(x:String)" Name="files" />
      <Variable x:TypeArguments="x:String" Name="strStartTime" />
      <Variable x:TypeArguments="x:Int32" Name="r" />
      <Variable x:TypeArguments="njl:JToken" Name="v" />
    </Sequence.Variables>
    <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_2">
      <TryCatch.Try>
        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" QueryParameters="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_2" Response="[resp]" Url="[IONAPI]" />
      </TryCatch.Try>
      <TryCatch.Catches>
        <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_2">
          <ActivityAction x:TypeArguments="s:Exception">
            <ActivityAction.Argument>
              <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
            </ActivityAction.Argument>
          </ActivityAction>
        </Catch>
      </TryCatch.Catches>
    </TryCatch>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_19">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[strStartTime]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[System.DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign DisplayName="Map Assign" sap2010:WorkflowViewState.IdRef="Assign_20">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[new Dictionary(Of String, Object)]</InArgument>
      </Assign.Value>
    </Assign>
    <If Condition="[authUser=&quot;NA&quot;]" DisplayName="Authuser If" sap2010:WorkflowViewState.IdRef="If_29">
      <If.Then>
        <Assign DisplayName="AuthUser Assign" sap2010:WorkflowViewState.IdRef="Assign_21">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[authUser]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">
              <Literal x:TypeArguments="x:String" Value="" />
            </InArgument>
          </Assign.Value>
        </Assign>
      </If.Then>
    </If>
    <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;configurationFolder&quot;,configurationFolder},{&quot;directoriesNames&quot;,directoriesNames.split(&quot;|&quot;c).ToList},{&quot;logFolderName&quot;,logFolderName},{&quot;numberOfParts&quot;,numberOfParts}}]" ContinueOnError="True" DisplayName="CreateDirectories Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_20" WorkflowFile="[projectPath+&quot;\CreateDirectories.xaml&quot;]" />
    <If Condition="[useGenAIExtraction]" DisplayName="IfUseGenAIExtraction" sap2010:WorkflowViewState.IdRef="If_36">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_33">
          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_27">
            <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_14" IsValid="[GenAIPromptFileExists]" Path="[configurationFolder+&quot;\&quot;+&quot;GenAIPrompt.txt&quot;]" />
            <If Condition="[GenAIPromptFileExists]" sap2010:WorkflowViewState.IdRef="If_30">
              <If.Then>
                <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_13" Source="[configurationFolder+&quot;\&quot;+&quot;GenAIPrompt.txt&quot;]" />
              </If.Then>
            </If>
            <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;configurationFolder&quot;,configurationFolder},{&quot;logFile&quot;,logFile},{&quot;GenAIPromptFileURL&quot;,GenAIPromptFileURL},{&quot;fileName&quot;,&quot;GenAIPrompt&quot;}}]" ContinueOnError="True" DisplayName="Invoke getGenAIPrompt download" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_21" OutputArguments="[GenAIOutput]" WorkflowFile="[projectPath+&quot;\getGenAIPrompt.xaml&quot;]" />
            <Assign sap2010:WorkflowViewState.IdRef="Assign_22">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[GenAIStatus]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[CType(GenAIOutput("GenAIStatus"), String)]</InArgument>
              </Assign.Value>
            </Assign>
          </Sequence>
          <Sequence DisplayName="GenAIPrompt_Ext Sequence" sap2010:WorkflowViewState.IdRef="Sequence_28">
            <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_15" IsValid="[GenAIPromptFileExists]" Path="[configurationFolder+&quot;\&quot;+&quot;GenAIPrompt_Ext.txt&quot;]" />
            <If Condition="[GenAIPromptFileExists]" sap2010:WorkflowViewState.IdRef="If_31">
              <If.Then>
                <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_14" Source="[configurationFolder+&quot;\&quot;+&quot;GenAIPrompt_Ext.txt&quot;]" />
              </If.Then>
            </If>
            <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;configurationFolder&quot;,configurationFolder},{&quot;logFile&quot;,logFile},{&quot;GenAIPromptFileURL&quot;,tenantID + &quot;IDM/api/items/search/item/resource?%24query=%2FGenAIPrompt%5B%40RESOURCENAME%20%3D%20%22GenAIPrompt_Ext.txt%22%5D%20SORTBY(%40LASTCHANGEDTS%20DESCENDING)&quot;},{&quot;fileName&quot;,&quot;GenAIPrompt_Ext&quot;}}]" ContinueOnError="True" DisplayName="Invoke getGenAIPrompt download" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_22" OutputArguments="[GenAIOutput]" WorkflowFile="[projectPath+&quot;\getGenAIPrompt.xaml&quot;]" />
            <Assign sap2010:WorkflowViewState.IdRef="Assign_23">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[GenAIStatus]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[CType(GenAIOutput("GenAIStatus"), String)]</InArgument>
              </Assign.Value>
            </Assign>
          </Sequence>
          <Sequence DisplayName="Split1Custom Sequence" sap2010:WorkflowViewState.IdRef="Sequence_29">
            <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_16" IsValid="[GenAIPromptFileExists]" Path="[configurationFolder+&quot;\&quot;+&quot;GenAISplit1_Ext.txt&quot;]" />
            <If Condition="[GenAIPromptFileExists]" sap2010:WorkflowViewState.IdRef="If_32">
              <If.Then>
                <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_15" Source="[configurationFolder+&quot;\&quot;+&quot;GenAISplit1_Ext.txt&quot;]" />
              </If.Then>
            </If>
            <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;configurationFolder&quot;,configurationFolder},{&quot;logFile&quot;,logFile},{&quot;GenAIPromptFileURL&quot;,tenantID + &quot;IDM/api/items/search/item/resource?%24query=%2FGenAIPrompt%5B%40RESOURCENAME%20%3D%20%22GenAISplit1_Ext.txt%22%5D%20SORTBY(%40LASTCHANGEDTS%20DESCENDING)&quot;},{&quot;fileName&quot;,&quot;GenAISplit1_Ext&quot;}}]" ContinueOnError="True" DisplayName="Invoke getGenAIPrompt download" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_23" OutputArguments="[GenAIOutput]" WorkflowFile="[projectPath+&quot;\getGenAIPrompt.xaml&quot;]" />
            <Assign sap2010:WorkflowViewState.IdRef="Assign_24">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[GenAIStatus]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[CType(GenAIOutput("GenAIStatus"), String)]</InArgument>
              </Assign.Value>
            </Assign>
          </Sequence>
          <Sequence DisplayName="CustomPrompt GenAI Sequence" sap2010:WorkflowViewState.IdRef="Sequence_30">
            <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_17" IsValid="[GenAIPromptFileExists]" Path="[configurationFolder+&quot;\&quot;+&quot;UnstructuredExpenseDocumentPrompt_Ext.txt&quot;]" />
            <If Condition="[GenAIPromptFileExists]" sap2010:WorkflowViewState.IdRef="If_33">
              <If.Then>
                <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_16" Source="[configurationFolder+&quot;\&quot;+&quot;UnstructuredExpenseDocumentPrompt_Ext.txt&quot;]" />
              </If.Then>
            </If>
            <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;configurationFolder&quot;,configurationFolder},{&quot;logFile&quot;,logFile},{&quot;GenAIPromptFileURL&quot;,tenantID + &quot;IDM/api/items/search/item/resource?%24query=%2FGenAIPrompt%5B%40RESOURCENAME%20%3D%20%22UnstructuredExpenseDocumentPrompt_Ext.txt%22%5D%20SORTBY(%40LASTCHANGEDTS%20DESCENDING)&quot;},{&quot;fileName&quot;,&quot;UnstructuredExpenseDocumentPrompt_Ext&quot;}}]" ContinueOnError="True" DisplayName="Invoke getGenAIPrompt Split 1 download" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_24" OutputArguments="[GenAIOutput]" WorkflowFile="[projectPath+&quot;\getGenAIPrompt.xaml&quot;]" />
            <Assign sap2010:WorkflowViewState.IdRef="Assign_25">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[GenAIStatus]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[CType(GenAIOutput("GenAIStatus"), String)]</InArgument>
              </Assign.Value>
            </Assign>
          </Sequence>
          <Sequence DisplayName="Classification GenAI Sequence" sap2010:WorkflowViewState.IdRef="Sequence_31">
            <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_18" IsValid="[GenAIPromptFileExists]" Path="[configurationFolder+&quot;\&quot;+&quot;ClassificationPrompt.txt&quot;]" />
            <If Condition="[GenAIPromptFileExists]" sap2010:WorkflowViewState.IdRef="If_34">
              <If.Then>
                <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_17" Source="[configurationFolder+&quot;\&quot;+&quot;ClassificationPrompt.txt&quot;]" />
              </If.Then>
            </If>
            <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;configurationFolder&quot;,configurationFolder},{&quot;logFile&quot;,logFile},{&quot;GenAIPromptFileURL&quot;,tenantID + &quot;IDM/api/items/search/item/resource?%24query=%2FGenAIPrompt%5B%40RESOURCENAME%20%3D%20%22ClassificationPrompt.txt%22%5D%20SORTBY(%40LASTCHANGEDTS%20DESCENDING)&quot;},{&quot;fileName&quot;,&quot;ClassificationPrompt&quot;}}]" ContinueOnError="True" DisplayName="Invoke getGenAIPrompt Split 1 download" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_25" OutputArguments="[GenAIOutput]" WorkflowFile="[projectPath+&quot;\getGenAIPrompt.xaml&quot;]" />
            <Assign sap2010:WorkflowViewState.IdRef="Assign_26">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[GenAIStatus]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[CType(GenAIOutput("GenAIStatus"), String)]</InArgument>
              </Assign.Value>
            </Assign>
          </Sequence>
          <Sequence DisplayName="Unstructured GenAI Sequence" sap2010:WorkflowViewState.IdRef="Sequence_32">
            <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_19" IsValid="[GenAIPromptFileExists]" Path="[configurationFolder+&quot;\&quot;+&quot;UnstructuredExpenseDocumentPrompt.txt&quot;]" />
            <If Condition="[GenAIPromptFileExists]" sap2010:WorkflowViewState.IdRef="If_35">
              <If.Then>
                <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_18" Source="[configurationFolder+&quot;\&quot;+&quot;UnstructuredExpenseDocumentPrompt.txt&quot;]" />
              </If.Then>
            </If>
            <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;configurationFolder&quot;,configurationFolder},{&quot;logFile&quot;,logFile},{&quot;GenAIPromptFileURL&quot;,tenantID + &quot;IDM/api/items/search/item/resource?%24query=%2FGenAIPrompt%5B%40RESOURCENAME%20%3D%20%22UnstructuredExpenseDocumentPrompt.txt%22%5D%20SORTBY(%40LASTCHANGEDTS%20DESCENDING)&quot;},{&quot;fileName&quot;,&quot;UnstructuredExpenseDocumentPrompt&quot;}}]" ContinueOnError="True" DisplayName="Invoke getGenAIPrompt Split 1 download" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_26" OutputArguments="[GenAIOutput]" WorkflowFile="[projectPath+&quot;\getGenAIPrompt.xaml&quot;]" />
            <Assign sap2010:WorkflowViewState.IdRef="Assign_27">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[GenAIStatus]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[CType(GenAIOutput("GenAIStatus"), String)]</InArgument>
              </Assign.Value>
            </Assign>
          </Sequence>
        </Sequence>
      </If.Then>
    </If>
    <If Condition="[useGenAIExtraction AND SplittingDoc]" DisplayName="IfSplitGenAIExtraction" sap2010:WorkflowViewState.IdRef="If_41">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_38">
          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_34">
            <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_20" IsValid="[GenAIPromptFileExists]" Path="[configurationFolder+&quot;\&quot;+&quot;GenAISplit1.txt&quot;]" />
            <If Condition="[GenAIPromptFileExists]" sap2010:WorkflowViewState.IdRef="If_37">
              <If.Then>
                <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_19" Source="[configurationFolder+&quot;\&quot;+&quot;GenAISplit1.txt&quot;]" />
              </If.Then>
            </If>
            <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;configurationFolder&quot;,configurationFolder},{&quot;logFile&quot;,logFile},{&quot;GenAIPromptFileURL&quot;,GenAIPromptFileURLForSplit},{&quot;fileName&quot;,&quot;GenAISplit1&quot;}}]" ContinueOnError="True" DisplayName="Invoke getGenAIPrompt Split 1 download" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_27" OutputArguments="[GenAIOutput]" WorkflowFile="[projectPath+&quot;\getGenAIPrompt.xaml&quot;]" />
            <Assign sap2010:WorkflowViewState.IdRef="Assign_28">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[GenAIStatus]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[CType(GenAIOutput("GenAIStatus"), String)]</InArgument>
              </Assign.Value>
            </Assign>
          </Sequence>
          <Sequence DisplayName="Split 2 Sequence" sap2010:WorkflowViewState.IdRef="Sequence_37">
            <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_21" IsValid="[GenAIPromptFileExists]" Path="[configurationFolder+&quot;\&quot;+&quot;GenAISplit2.txt&quot;]" />
            <If Condition="[GenAIPromptFileExists]" sap2010:WorkflowViewState.IdRef="If_38">
              <If.Then>
                <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_20" Source="[configurationFolder+&quot;\&quot;+&quot;GenAISplit2.txt&quot;]" />
              </If.Then>
            </If>
            <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;configurationFolder&quot;,configurationFolder},{&quot;logFile&quot;,logFile},{&quot;GenAIPromptFileURL&quot;,GenAIPromptFileURLForSplit2},{&quot;fileName&quot;,&quot;GenAISplit2&quot;}}]" ContinueOnError="True" DisplayName="Invoke getGenAIPrompt Split 2 download" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_28" OutputArguments="[GenAIOutput]" WorkflowFile="[projectPath+&quot;\getGenAIPrompt.xaml&quot;]" />
            <Assign sap2010:WorkflowViewState.IdRef="Assign_29">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[GenAIStatus]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[CType(GenAIOutput("GenAIStatus"), String)]</InArgument>
              </Assign.Value>
            </Assign>
            <Sequence DisplayName="Split 2 Sequence" sap2010:WorkflowViewState.IdRef="Sequence_35">
              <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_22" IsValid="[GenAIPromptFileExists]" Path="[configurationFolder+&quot;\&quot;+&quot;GenAISplit2.txt&quot;]" />
              <If Condition="[GenAIPromptFileExists]" sap2010:WorkflowViewState.IdRef="If_39">
                <If.Then>
                  <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_21" Source="[configurationFolder+&quot;\&quot;+&quot;GenAISplit2.txt&quot;]" />
                </If.Then>
              </If>
              <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;configurationFolder&quot;,configurationFolder},{&quot;logFile&quot;,logFile},{&quot;GenAIPromptFileURL&quot;,GenAIPromptFileURLForSplit2},{&quot;fileName&quot;,&quot;GenAISplit2&quot;}}]" ContinueOnError="True" DisplayName="Invoke getGenAIPrompt Split 2 download" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_29" OutputArguments="[GenAIOutput]" WorkflowFile="[projectPath+&quot;\getGenAIPrompt.xaml&quot;]" />
              <Assign sap2010:WorkflowViewState.IdRef="Assign_30">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[GenAIStatus]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[CType(GenAIOutput("GenAIStatus"), String)]</InArgument>
                </Assign.Value>
              </Assign>
            </Sequence>
            <Sequence DisplayName="Split 2 Ext Sequence" sap2010:WorkflowViewState.IdRef="Sequence_36">
              <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_23" IsValid="[GenAIPromptFileExists]" Path="[configurationFolder+&quot;\&quot;+&quot;GenAISplit2_Ext.txt&quot;]" />
              <If Condition="[GenAIPromptFileExists]" sap2010:WorkflowViewState.IdRef="If_40">
                <If.Then>
                  <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_22" Source="[configurationFolder+&quot;\&quot;+&quot;GenAISplit2_Ext.txt&quot;]" />
                </If.Then>
              </If>
              <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;configurationFolder&quot;,configurationFolder},{&quot;logFile&quot;,logFile},{&quot;GenAIPromptFileURL&quot;,tenantID + &quot;IDM/api/items/search/item/resource?%24query=%2FGenAIPrompt%5B%40RESOURCENAME%20%3D%20%22GenAISplit2_Ext.txt%22%5D%20SORTBY(%40LASTCHANGEDTS%20DESCENDING)&quot;},{&quot;fileName&quot;,&quot;GenAISplit2_Ext&quot;}}]" ContinueOnError="True" DisplayName="Invoke getGenAIPrompt Split 2 download" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_30" OutputArguments="[GenAIOutput]" WorkflowFile="[projectPath+&quot;\getGenAIPrompt.xaml&quot;]" />
              <Assign sap2010:WorkflowViewState.IdRef="Assign_31">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[GenAIStatus]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[CType(GenAIOutput("GenAIStatus"), String)]</InArgument>
                </Assign.Value>
              </Assign>
            </Sequence>
          </Sequence>
        </Sequence>
      </If.Then>
    </If>
    <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_1">
      <iad:CommentOut.Activities>
        <sco:Collection x:TypeArguments="Activity" />
      </iad:CommentOut.Activities>
    </iad:CommentOut>
    <Assign DisplayName="Logs Assign" sap2010:WorkflowViewState.IdRef="Assign_32">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[logFile]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String" xml:space="preserve">[configurationFolder+  "\Logs\JobsResults"+System.DateTime.Now.ToString("ddMMyyyy")+".txt"]</InArgument>
      </Assign.Value>
    </Assign>
    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Empty Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_7" Source="[logFile]">
      <ias:Append_Line.Line>
        <InArgument x:TypeArguments="x:String">
          <Literal x:TypeArguments="x:String" Value="" />
        </InArgument>
      </ias:Append_Line.Line>
    </ias:Append_Line>
    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Start time Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_8" Line="[&quot;Start Time : &quot; +strStartTime]" Source="[logFile]" />
    <Sequence DisplayName="Misc Values assign Sequence" sap2010:WorkflowViewState.IdRef="Sequence_39">
      <InvokeMethod DisplayName="strStartTime InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_41" MethodName="Add">
        <InvokeMethod.TargetObject>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</InArgument>
        </InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="x:String">StartTime</InArgument>
        <InArgument x:TypeArguments="x:Object">[strStartTime]</InArgument>
      </InvokeMethod>
      <InvokeMethod DisplayName="Comments InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_42" MethodName="Add">
        <InvokeMethod.TargetObject>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</InArgument>
        </InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="x:String">Comments</InArgument>
        <InArgument x:TypeArguments="x:Object">[""]</InArgument>
      </InvokeMethod>
      <InvokeMethod DisplayName="Additional Attr InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_70" MethodName="Add">
        <InvokeMethod.TargetObject>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</InArgument>
        </InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="x:String">addInfo</InArgument>
        <InArgument x:TypeArguments="x:Object">[""]</InArgument>
      </InvokeMethod>
      <InvokeMethod DisplayName="ERP InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_43" MethodName="Add">
        <InvokeMethod.TargetObject>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</InArgument>
        </InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="x:String">ERP</InArgument>
        <InArgument x:TypeArguments="x:Object">[ERP]</InArgument>
      </InvokeMethod>
      <InvokeMethod DisplayName="useGenAIExtraction InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_44" MethodName="Add">
        <InvokeMethod.TargetObject>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</InArgument>
        </InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="x:String">useGenAIExtraction</InArgument>
        <InArgument x:TypeArguments="x:Object">[useGenAIExtraction]</InArgument>
      </InvokeMethod>
      <InvokeMethod DisplayName="processDeliveryNote InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_45" MethodName="Add">
        <InvokeMethod.TargetObject>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</InArgument>
        </InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="x:String">processDeliveryNote</InArgument>
        <InArgument x:TypeArguments="x:Object">[processDeliveryNote]</InArgument>
      </InvokeMethod>
      <InvokeMethod DisplayName="businessRuleForTolerance InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_46" MethodName="Add">
        <InvokeMethod.TargetObject>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</InArgument>
        </InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="x:String">businessRuleForTolerance</InArgument>
        <InArgument x:TypeArguments="x:Object">[businessRuleForTolerance]</InArgument>
      </InvokeMethod>
      <InvokeMethod DisplayName="businessRuleForGUID InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_47" MethodName="Add">
        <InvokeMethod.TargetObject>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</InArgument>
        </InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="x:String">businessRuleForGUID</InArgument>
        <InArgument x:TypeArguments="x:Object">[businessRuleForGUID]</InArgument>
      </InvokeMethod>
      <InvokeMethod DisplayName="division InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_48" MethodName="Add">
        <InvokeMethod.TargetObject>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</InArgument>
        </InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="x:String">division</InArgument>
        <InArgument x:TypeArguments="x:Object">[division]</InArgument>
      </InvokeMethod>
      <InvokeMethod DisplayName="company InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_49" MethodName="Add">
        <InvokeMethod.TargetObject>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</InArgument>
        </InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="x:String">company</InArgument>
        <InArgument x:TypeArguments="x:Object">[company]</InArgument>
      </InvokeMethod>
      <InvokeMethod DisplayName="includeDistribution InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_50" MethodName="Add">
        <InvokeMethod.TargetObject>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</InArgument>
        </InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="x:String">includeDistribution</InArgument>
        <InArgument x:TypeArguments="x:Object">[includeDistribution]</InArgument>
      </InvokeMethod>
      <InvokeMethod DisplayName="DivisiontoGLCode InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_51" MethodName="Add">
        <InvokeMethod.TargetObject>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</InArgument>
        </InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="x:String">businessRuleForDivisionGLCode</InArgument>
        <InArgument x:TypeArguments="x:Object">[businessRuleForDivisionGLCode]</InArgument>
      </InvokeMethod>
      <InvokeMethod DisplayName="useBusinessRuleForTelerance InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_52" MethodName="Add">
        <InvokeMethod.TargetObject>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</InArgument>
        </InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="x:String">useBusinessRuleForTelerance</InArgument>
        <InArgument x:TypeArguments="x:Object">[useBusinessRuleForTelerance]</InArgument>
      </InvokeMethod>
      <InvokeMethod DisplayName="enableStagingForExpenseInException InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_53" MethodName="Add">
        <InvokeMethod.TargetObject>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</InArgument>
        </InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="x:String">enableStagingForExpenseInException</InArgument>
        <InArgument x:TypeArguments="x:Object">[enableStagingForExpenseInException]</InArgument>
      </InvokeMethod>
      <InvokeMethod DisplayName="mandateGEOC InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_54" MethodName="Add">
        <InvokeMethod.TargetObject>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</InArgument>
        </InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="x:String">mandateGEOC</InArgument>
        <InArgument x:TypeArguments="x:Object">[mandateGEOC]</InArgument>
      </InvokeMethod>
      <InvokeMethod DisplayName=" Spliting InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_55" MethodName="Add">
        <InvokeMethod.TargetObject>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</InArgument>
        </InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="x:String">SplittingDoc</InArgument>
        <InArgument x:TypeArguments="x:Object">[SplittingDoc]</InArgument>
      </InvokeMethod>
      <InvokeMethod DisplayName=" groupByTransDate InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_56" MethodName="Add">
        <InvokeMethod.TargetObject>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</InArgument>
        </InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="x:String">groupByTransDate</InArgument>
        <InArgument x:TypeArguments="x:Object">[groupByTransDate]</InArgument>
      </InvokeMethod>
      <InvokeMethod DisplayName="createInvoiceIrrespectiveTolerance InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_57" MethodName="Add">
        <InvokeMethod.TargetObject>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</InArgument>
        </InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="x:String">createInvoiceIrrespectiveTolerance</InArgument>
        <InArgument x:TypeArguments="x:Object">[createInvoiceIrrespectiveTolerance]</InArgument>
      </InvokeMethod>
      <InvokeMethod DisplayName="autoAllocateOpenLines InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_58" MethodName="Add">
        <InvokeMethod.TargetObject>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</InArgument>
        </InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="x:String">autoAllocateOpenLines</InArgument>
        <InArgument x:TypeArguments="x:Object">[autoAllocateOpenLines]</InArgument>
      </InvokeMethod>
      <InvokeMethod DisplayName="AutomateValidation InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_59" MethodName="Add">
        <InvokeMethod.TargetObject>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</InArgument>
        </InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="x:String">AutomateValidation</InArgument>
        <InArgument x:TypeArguments="x:Object">[AutomateValidation]</InArgument>
      </InvokeMethod>
      <InvokeMethod DisplayName="AutomateApproval InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_60" MethodName="Add">
        <InvokeMethod.TargetObject>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</InArgument>
        </InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="x:String">AutomateApproval</InArgument>
        <InArgument x:TypeArguments="x:Object">[AutomateApproval]</InArgument>
      </InvokeMethod>
      <InvokeMethod DisplayName="vtcdConfig InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_61" MethodName="Add">
        <InvokeMethod.TargetObject>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</InArgument>
        </InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="x:String">vatCodeConfig</InArgument>
        <InArgument x:TypeArguments="x:Object">[vatCodeConfig]</InArgument>
      </InvokeMethod>
      <InvokeMethod DisplayName="SendEmail InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_62" MethodName="Add">
        <InvokeMethod.TargetObject>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</InArgument>
        </InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="x:String">SendEmail</InArgument>
        <InArgument x:TypeArguments="x:Object">[SendEmail]</InArgument>
      </InvokeMethod>
      <InvokeMethod DisplayName="BusinessRuleAPResp InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_63" MethodName="Add">
        <InvokeMethod.TargetObject>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</InArgument>
        </InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="x:String">BusinessRuleAPResp</InArgument>
        <InArgument x:TypeArguments="x:Object">[BusinessRuleAPResp]</InArgument>
      </InvokeMethod>
      <InvokeMethod DisplayName="genAiModelInvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_64" MethodName="Add">
        <InvokeMethod.TargetObject>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</InArgument>
        </InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="x:String">genAiModel</InArgument>
        <InArgument x:TypeArguments="x:Object">[genAiModel]</InArgument>
      </InvokeMethod>
      <InvokeMethod DisplayName="genAiVersion InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_65" MethodName="Add">
        <InvokeMethod.TargetObject>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</InArgument>
        </InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="x:String">genAiVersion</InArgument>
        <InArgument x:TypeArguments="x:Object">[genAiVersion]</InArgument>
      </InvokeMethod>
      <InvokeMethod DisplayName="handleCashDiscount InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_66" MethodName="Add">
        <InvokeMethod.TargetObject>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</InArgument>
        </InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="x:String">handleCashDiscount</InArgument>
        <InArgument x:TypeArguments="x:Object">[handleCashDiscount]</InArgument>
      </InvokeMethod>
      <InvokeMethod DisplayName="MandateDeliveryNoteWhenPo InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_67" MethodName="Add">
        <InvokeMethod.TargetObject>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</InArgument>
        </InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="x:String">MandateDeliveryNoteWhenPo</InArgument>
        <InArgument x:TypeArguments="x:Object">[MandateDeliveryNoteWhenPo]</InArgument>
      </InvokeMethod>
      <InvokeMethod DisplayName="userIdentifier InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_68" MethodName="Add">
        <InvokeMethod.TargetObject>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</InArgument>
        </InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="x:String">userIdentifier</InArgument>
        <InArgument x:TypeArguments="x:Object">[userIdentifier]</InArgument>
      </InvokeMethod>
      <InvokeMethod DisplayName="distributionType InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_69" MethodName="Add">
        <InvokeMethod.TargetObject>
          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[miscValues]</InArgument>
        </InvokeMethod.TargetObject>
        <InArgument x:TypeArguments="x:String">distributionType</InArgument>
        <InArgument x:TypeArguments="x:Object">[distributionType]</InArgument>
      </InvokeMethod>
    </Sequence>
    <If Condition="[processEmails]" DisplayName="IfProcessEmails" sap2010:WorkflowViewState.IdRef="If_43">
      <If.Then>
        <If Condition="[invoiceSource =&quot;OutlookClientEmail&quot; or  invoiceSource =&quot;OutlookGraphEmail&quot;]" DisplayName="Process Emails If" sap2010:WorkflowViewState.IdRef="If_42">
          <If.Then>
            <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;configurationFolder&quot;,configurationFolder},{&quot;emailAccount&quot;,emailAccount},{&quot;emailFolder&quot;,emailFolder},{&quot;numberOfEmails&quot;,numberOfEmails},{&quot;logFile&quot;,logFile},{&quot;ocrKeysDictionary&quot;,ocrKeysDictionary},{&quot;tenantID&quot;,tenantID},{&quot;colemanAPI&quot;,colemanAPI},{&quot;userIdentifier&quot;,userIdentifier},{&quot;distributionType&quot;,distributionType},{&quot;projectPath&quot;,projectPath},{&quot;enableMessageBoxes&quot;,enableMessageBoxes},{&quot;poFilterValues&quot;,poFilterValues},{&quot;poFilterCondition&quot;,poFilterCondition},{&quot;datalakeAPILogicalId&quot;,datalakeAPILogicalId},{&quot;chargeCode&quot;,chargeCode},{&quot;discountCode&quot;,discountCode},{&quot;authUser&quot;,authUser},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;extractNumericFromPO&quot;,extractNumericFromPO},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;poDiscountsHandlingConfig&quot;,poDiscountsHandlingConfig},{&quot;matchVendorItemCode&quot;,matchVendorItemCode},{&quot;invoiceSource&quot;,invoiceSource},{&quot;extractFromWidgetDatalake&quot;,extractFromWidgetDatalake},{&quot;approvalRequired&quot;,approvalRequired},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;processExpenseInvoice&quot;,processExpenseInvoice},{&quot;miscValues&quot;,miscValues}}]" ContinueOnError="True" DisplayName="GetOutlookEmails Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_31" WorkflowFile="[projectPath+&quot;\Outlook_parallel.xaml&quot;]" />
          </If.Then>
        </If>
      </If.Then>
    </If>
    <If Condition="[processFolders]" DisplayName="IfProcessFolders" sap2010:WorkflowViewState.IdRef="If_45">
      <If.Then>
        <If Condition="[invoiceFolderPath &lt;&gt; &quot;&quot; and invoiceFolderPath &lt;&gt; &quot;NA&quot;]" DisplayName="Process Folders If" sap2010:WorkflowViewState.IdRef="If_44">
          <If.Then>
            <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;configurationFolder&quot;,configurationFolder},{&quot;logFile&quot;,logFile},{&quot;ocrKeysDictionary&quot;,ocrKeysDictionary},{&quot;tenantID&quot;,tenantID},{&quot;colemanAPI&quot;,colemanAPI},{&quot;userIdentifier&quot;,userIdentifier},{&quot;distributionType&quot;,distributionType},{&quot;projectPath&quot;,projectPath},{&quot;enableMessageBoxes&quot;,enableMessageBoxes},{&quot;poFilterValues&quot;,poFilterValues},{&quot;poFilterCondition&quot;,poFilterCondition},{&quot;datalakeAPILogicalId&quot;,datalakeAPILogicalId},{&quot;chargeCode&quot;,chargeCode},{&quot;discountCode&quot;,discountCode},{&quot;authUser&quot;,authUser},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;invoiceFolderPath&quot;,invoiceFolderPath},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;poDiscountsHandlingConfig&quot;,poDiscountsHandlingConfig},{&quot;matchVendorItemCode&quot;,matchVendorItemCode},{&quot;extractNumericFromPO&quot;,extractNumericFromPO},{&quot;extractFromWidgetDatalake&quot;,extractFromWidgetDatalake},{&quot;approvalRequired&quot;,approvalRequired},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;miscValues&quot;,miscValues},{&quot;processExpenseInvoice&quot;,processExpenseInvoice}}]" ContinueOnError="True" DisplayName="Process Folders" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_32" WorkflowFile="[projectPath+&quot;\ReadFilesFromFolder.xaml&quot;]" />
          </If.Then>
        </If>
      </If.Then>
    </If>
    <If Condition="[extractFromWidgetDatalake AND reprocess]" DisplayName="extractFromWidgetDatalake If" sap2010:WorkflowViewState.IdRef="If_47">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_41">
          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_40">
            <iaw:InvokeWorkflow OutputArguments="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;configurationFolder&quot;,configurationFolder},{&quot;tenantID&quot;,tenantID},{&quot;colemanAPI&quot;,colemanAPI},{&quot;logFile&quot;,logFile},{&quot;distributionType&quot;,distributionType},{&quot;userIdentifier&quot;,userIdentifier},{&quot;projectPath&quot;,projectPath},{&quot;miscValues&quot;,miscValues},{&quot;maxNotReceivedCount&quot;,maxNotReceivedCount},{&quot;datalakeAPILogicalId&quot;,datalakeAPILogicalId},{&quot;chargeCode&quot;,chargeCode},{&quot;discountCode&quot;,discountCode},{&quot;authUser&quot;,authUser},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;extractFromWidgetDatalake&quot;,extractFromWidgetDatalake},{&quot;approvalRequired&quot;,approvalRequired},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;matchVendorItemCode&quot;,matchVendorItemCode},{&quot;enableMessageBoxes&quot;,enableMessageBoxes}}]" ContinueOnError="True" DisplayName="Read Datalake" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_33" ResponseCode="[datalakeResponseCode]" WorkflowFile="[projectpath+&quot;\ExtractFromDatalake - Copy.xaml&quot;]" />
            <If Condition="[datalakeResponseCode&lt;&gt; 200]" sap2010:WorkflowViewState.IdRef="If_46">
              <If.Then>
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Datalake error Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_9" Line="Error occurred while retriving the data from the Datalake." Source="[logFile]" />
              </If.Then>
            </If>
          </Sequence>
        </Sequence>
      </If.Then>
    </If>
    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="End Time Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_10" Line="[&quot;End Time : &quot; +System.DateTime.Now.ToString(&quot;yyyy/MM/dd HH:mm:ss&quot;)]" Source="[logFile]" />
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_2" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_2" sap:VirtualizedContainerService.HintSize="404,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_2" sap:VirtualizedContainerService.HintSize="634,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_19" sap:VirtualizedContainerService.HintSize="634,62" />
      <sap2010:ViewStateData Id="Assign_20" sap:VirtualizedContainerService.HintSize="634,62" />
      <sap2010:ViewStateData Id="Assign_21" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_29" sap:VirtualizedContainerService.HintSize="634,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_20" sap:VirtualizedContainerService.HintSize="634,22" />
      <sap2010:ViewStateData Id="Path_Validate_14" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="File_Delete_13" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_30" sap:VirtualizedContainerService.HintSize="464,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_21" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_22" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Sequence_27" sap:VirtualizedContainerService.HintSize="486,556">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Path_Validate_15" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="File_Delete_14" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_31" sap:VirtualizedContainerService.HintSize="464,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_22" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_23" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Sequence_28" sap:VirtualizedContainerService.HintSize="486,556">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Path_Validate_16" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="File_Delete_15" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_32" sap:VirtualizedContainerService.HintSize="464,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_23" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_24" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Sequence_29" sap:VirtualizedContainerService.HintSize="486,556">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Path_Validate_17" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="File_Delete_16" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_33" sap:VirtualizedContainerService.HintSize="464,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_24" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_25" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Sequence_30" sap:VirtualizedContainerService.HintSize="486,556">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Path_Validate_18" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="File_Delete_17" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_34" sap:VirtualizedContainerService.HintSize="464,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_25" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_26" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Sequence_31" sap:VirtualizedContainerService.HintSize="486,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Path_Validate_19" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="File_Delete_18" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_35" sap:VirtualizedContainerService.HintSize="464,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_26" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_27" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Sequence_32" sap:VirtualizedContainerService.HintSize="486,556">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_33" sap:VirtualizedContainerService.HintSize="508,3155">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_36" sap:VirtualizedContainerService.HintSize="634,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Path_Validate_20" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="File_Delete_19" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_37" sap:VirtualizedContainerService.HintSize="464,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_27" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_28" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Sequence_34" sap:VirtualizedContainerService.HintSize="508,564">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Path_Validate_21" sap:VirtualizedContainerService.HintSize="486,22" />
      <sap2010:ViewStateData Id="File_Delete_20" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_38" sap:VirtualizedContainerService.HintSize="486,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_28" sap:VirtualizedContainerService.HintSize="486,22" />
      <sap2010:ViewStateData Id="Assign_29" sap:VirtualizedContainerService.HintSize="486,62" />
      <sap2010:ViewStateData Id="Path_Validate_22" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="File_Delete_21" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_39" sap:VirtualizedContainerService.HintSize="464,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_29" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_30" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Sequence_35" sap:VirtualizedContainerService.HintSize="486,564">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Path_Validate_23" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="File_Delete_22" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_40" sap:VirtualizedContainerService.HintSize="464,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_30" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_31" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Sequence_36" sap:VirtualizedContainerService.HintSize="486,564">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_37" sap:VirtualizedContainerService.HintSize="508,1772">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_38" sap:VirtualizedContainerService.HintSize="530,2500">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_41" sap:VirtualizedContainerService.HintSize="634,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_1" sap:VirtualizedContainerService.HintSize="634,66" />
      <sap2010:ViewStateData Id="Assign_32" sap:VirtualizedContainerService.HintSize="634,60" />
      <sap2010:ViewStateData Id="Append_Line_7" sap:VirtualizedContainerService.HintSize="634,22" />
      <sap2010:ViewStateData Id="Append_Line_8" sap:VirtualizedContainerService.HintSize="634,22" />
      <sap2010:ViewStateData Id="InvokeMethod_41" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="InvokeMethod_42" sap:VirtualizedContainerService.HintSize="218,134" />
      <sap2010:ViewStateData Id="InvokeMethod_70" sap:VirtualizedContainerService.HintSize="218,134" />
      <sap2010:ViewStateData Id="InvokeMethod_43" sap:VirtualizedContainerService.HintSize="218,134" />
      <sap2010:ViewStateData Id="InvokeMethod_44" sap:VirtualizedContainerService.HintSize="218,134" />
      <sap2010:ViewStateData Id="InvokeMethod_45" sap:VirtualizedContainerService.HintSize="218,134" />
      <sap2010:ViewStateData Id="InvokeMethod_46" sap:VirtualizedContainerService.HintSize="218,134" />
      <sap2010:ViewStateData Id="InvokeMethod_47" sap:VirtualizedContainerService.HintSize="218,134" />
      <sap2010:ViewStateData Id="InvokeMethod_48" sap:VirtualizedContainerService.HintSize="218,134" />
      <sap2010:ViewStateData Id="InvokeMethod_49" sap:VirtualizedContainerService.HintSize="218,134" />
      <sap2010:ViewStateData Id="InvokeMethod_50" sap:VirtualizedContainerService.HintSize="218,134" />
      <sap2010:ViewStateData Id="InvokeMethod_51" sap:VirtualizedContainerService.HintSize="218,134" />
      <sap2010:ViewStateData Id="InvokeMethod_52" sap:VirtualizedContainerService.HintSize="218,134" />
      <sap2010:ViewStateData Id="InvokeMethod_53" sap:VirtualizedContainerService.HintSize="218,134" />
      <sap2010:ViewStateData Id="InvokeMethod_54" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="InvokeMethod_55" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="InvokeMethod_56" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="InvokeMethod_57" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="InvokeMethod_58" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="InvokeMethod_59" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="InvokeMethod_60" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="InvokeMethod_61" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="InvokeMethod_62" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="InvokeMethod_63" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="InvokeMethod_64" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="InvokeMethod_65" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="InvokeMethod_66" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="InvokeMethod_67" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="InvokeMethod_68" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="InvokeMethod_69" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="Sequence_39" sap:VirtualizedContainerService.HintSize="634,5202">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_31" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_42" sap:VirtualizedContainerService.HintSize="464,214" />
      <sap2010:ViewStateData Id="If_43" sap:VirtualizedContainerService.HintSize="634,368" />
      <sap2010:ViewStateData Id="InvokeWorkflow_32" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_44" sap:VirtualizedContainerService.HintSize="464,214" />
      <sap2010:ViewStateData Id="If_45" sap:VirtualizedContainerService.HintSize="634,368" />
      <sap2010:ViewStateData Id="InvokeWorkflow_33" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Append_Line_9" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_46" sap:VirtualizedContainerService.HintSize="464,214" />
      <sap2010:ViewStateData Id="Sequence_40" sap:VirtualizedContainerService.HintSize="486,400">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_41" sap:VirtualizedContainerService.HintSize="508,524">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_47" sap:VirtualizedContainerService.HintSize="634,678" />
      <sap2010:ViewStateData Id="Append_Line_10" sap:VirtualizedContainerService.HintSize="633,22" />
      <sap2010:ViewStateData Id="Sequence_42" sap:VirtualizedContainerService.HintSize="656,7884.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="696,8004.66666666667" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>