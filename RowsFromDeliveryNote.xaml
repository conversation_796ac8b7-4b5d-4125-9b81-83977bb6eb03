﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="ocrValues" Type="InArgument(s:String[])" />
    <x:Property Name="M3TotalTableRows" Type="OutArgument(scg:List(s:String[]))" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="logfile" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="VendorID" Type="OutArgument(x:String)" />
    <x:Property Name="Status" Type="OutArgument(x:String)" />
    <x:Property Name="commentStatus" Type="OutArgument(x:String)" />
    <x:Property Name="division" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.Activities.Workflow</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.IO</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.Windows.Markup</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.Activities.Workflow</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_28">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:Int32" Name="vendorResponseCode" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="vendorResponseDictionary" />
    </Sequence.Variables>
    <If Condition="True" sap2010:WorkflowViewState.IdRef="If_18">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_27">
          <Sequence.Variables>
            <Variable x:TypeArguments="x:Boolean" Name="dnExists" />
            <Variable x:TypeArguments="x:String" Name="dn" />
            <Variable x:TypeArguments="scg:List(x:String)" Name="deliveryNumbers" />
            <Variable x:TypeArguments="scg:List(x:String)" Name="vendorList" />
          </Sequence.Variables>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_1">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(x:String)">[deliveryNumbers]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_2">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(x:String)">[deliveryNumbers]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(x:String)">[ocrValues(11).split(","c).ToList]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_3">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Boolean">[dnExists]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Boolean">True</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_4">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(s:String[])">[new list(OF string())]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_5">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[dn]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">
                <Literal x:TypeArguments="x:String" Value="" />
              </InArgument>
            </Assign.Value>
          </Assign>
          <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_3" Values="[deliveryNumbers]">
            <ActivityAction x:TypeArguments="x:String">
              <ActivityAction.Argument>
                <DelegateInArgument x:TypeArguments="x:String" Name="item0" />
              </ActivityAction.Argument>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_18">
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_17">
                  <Sequence.Variables>
                    <Variable x:TypeArguments="x:String" Name="tolamount" />
                    <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="linesOcrWorkflowOutput" />
                    <Variable x:TypeArguments="x:Int32" Name="linesOcrWorkflowStatus" />
                    <Variable x:TypeArguments="x:String" Name="req2" />
                  </Sequence.Variables>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_6">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String" xml:space="preserve">["F2PNLI,F2REPN,F2SCOC,F2RPQA,F2TRDT,F2ITNO,F2CONO,F2CUCD,F2DIVI,F2SUNO,F2IVQA,F2PUNO,F2SUDO,F2RCAC,F2RPQT,F2SCOP,F2SERA,F2IVQT,F2ICAC,F2PNLS  from FGRECL where F2SUDO = '" + item0.trim()+ "' and F2IMST != 9 "]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;miscValues&quot;,miscValues},{&quot;req2&quot;,req2}}]" ContinueOnError="True" DisplayName="Lines extraction Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_1" OutputArguments="[linesOcrWorkflowOutput]" ResponseCode="[linesOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\LinesExtractionWithPO.xaml&quot;]" />
                  <If Condition="[linesOcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_10">
                    <If.Then>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_15">
                        <Sequence.Variables>
                          <Variable x:TypeArguments="scg:List(s:String[])" Name="M3TotalTableRows1" />
                          <Variable x:TypeArguments="x:Boolean" Name="DNinM3exists" />
                        </Sequence.Variables>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_7">
                          <Assign.To>
                            <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows1]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="scg:List(s:String[])">[CType(linesOcrWorkflowOutput("M3TotalTableRows"), List(Of String()))]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_8">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Boolean">[DNinM3exists]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Boolean">[CType(linesOcrWorkflowOutput("DNinM3exists"), Boolean)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_9">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[vendorID]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[CType(linesOcrWorkflowOutput("VendorID"), String)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <If Condition="[M3TotalTableRows1.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_9">
                          <If.Then>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_3">
                              <If Condition="[dnExists]" sap2010:WorkflowViewState.IdRef="If_1">
                                <If.Then>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_2">
                                    <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_1" Values="[M3TotalTableRows1]">
                                      <ActivityAction x:TypeArguments="s:String[]">
                                        <ActivityAction.Argument>
                                          <DelegateInArgument x:TypeArguments="s:String[]" Name="item" />
                                        </ActivityAction.Argument>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_1">
                                          <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_1" MethodName="Add">
                                            <InvokeMethod.TargetObject>
                                              <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</InArgument>
                                            </InvokeMethod.TargetObject>
                                            <InArgument x:TypeArguments="s:String[]">[item]</InArgument>
                                          </InvokeMethod>
                                        </Sequence>
                                      </ActivityAction>
                                    </ForEach>
                                  </Sequence>
                                </If.Then>
                              </If>
                            </Sequence>
                          </If.Then>
                          <If.Else>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_14">
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_10">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[dn]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[dn + item0.trim + ", "]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <If Condition="[DNinM3exists]" sap2010:WorkflowViewState.IdRef="If_8">
                                <If.Then>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_4">
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_11">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[VendorID]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">[CType(linesOcrWorkflowOutput("VendorID"), String)]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_12">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">["Lines already invoiced for delivery notes "]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_13">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">Failure</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                  </Sequence>
                                </If.Then>
                                <If.Else>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_13">
                                    <If Condition="[deliveryNumbers.Count = 1]" sap2010:WorkflowViewState.IdRef="If_7">
                                      <If.Then>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_11">
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_14">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String" xml:space="preserve">["F2PNLI,F2REPN,F2SCOC,F2RPQA,F2TRDT,F2ITNO,F2CONO,F2CUCD,F2DIVI,F2SUNO,F2IVQA,F2PUNO,F2SUDO,F2RCAC,F2RPQT,F2SCOP,F2SERA,F2IVQT,F2ICAC,F2PNLS  from FGRECL where F2PUNO = '" + ocrValues(4)+ "' and F2DIVI = '"+division + "' and F2IMST != 9 "]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;miscValues&quot;,miscValues},{&quot;req2&quot;,req2}}]" ContinueOnError="True" DisplayName="Lines extraction Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_2" OutputArguments="[linesOcrWorkflowOutput]" ResponseCode="[linesOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\LinesExtractionWithPO.xaml&quot;]" />
                                          <If Condition="[linesOcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_6">
                                            <If.Then>
                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_10">
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_15">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="scg:List(s:String[])">[new list(OF string())]</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_16">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows1]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="scg:List(s:String[])">[CType(linesOcrWorkflowOutput("M3TotalTableRows"), List(Of String()))]</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_17">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[vendorID]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String">[CType(linesOcrWorkflowOutput("VendorID"), String)]</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                                <If Condition="[M3TotalTableRows1.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_5">
                                                  <If.Then>
                                                    <If Condition="[dnExists]" sap2010:WorkflowViewState.IdRef="If_4">
                                                      <If.Then>
                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_6">
                                                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="[&quot;Receipt lines for the delivery note number &quot;  + &quot; is extracted.&quot;]" Source="[logfile]" />
                                                          <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_2" Values="[M3TotalTableRows1]">
                                                            <ActivityAction x:TypeArguments="s:String[]">
                                                              <ActivityAction.Argument>
                                                                <DelegateInArgument x:TypeArguments="s:String[]" Name="item" />
                                                              </ActivityAction.Argument>
                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_5">
                                                                <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_2" MethodName="Add">
                                                                  <InvokeMethod.TargetObject>
                                                                    <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</InArgument>
                                                                  </InvokeMethod.TargetObject>
                                                                  <InArgument x:TypeArguments="s:String[]">[item]</InArgument>
                                                                </InvokeMethod>
                                                              </Sequence>
                                                            </ActivityAction>
                                                          </ForEach>
                                                        </Sequence>
                                                      </If.Then>
                                                      <If.Else>
                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_9">
                                                          <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;VendorName&quot;,ocrValues(8)},{&quot;VendorAddress&quot;,ocrValues(12)},{&quot;VendorPhone&quot;,ocrValues(13)}}]" ContinueOnError="True" DisplayName="VendorAddress Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_3" OutputArguments="[vendorResponseDictionary]" ResponseCode="[vendorResponseCode]" WorkflowFile="[projectPath+&quot;\vendorIDAddressMatch.xaml&quot;]" />
                                                          <If Condition="[vendorResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_2">
                                                            <If.Then>
                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_7">
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_18">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("vendorID"), String)]</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                              </Sequence>
                                                            </If.Then>
                                                          </If>
                                                          <If Condition="[vendorId &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_3">
                                                            <If.Else>
                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_8">
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_19">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:String">Missing required Information</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_20">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:String">MISSINGINFORMATION</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                              </Sequence>
                                                            </If.Else>
                                                          </If>
                                                        </Sequence>
                                                      </If.Else>
                                                    </If>
                                                  </If.Then>
                                                </If>
                                              </Sequence>
                                            </If.Then>
                                          </If>
                                        </Sequence>
                                      </If.Then>
                                      <If.Else>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_12">
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_21">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">["No receipts available for the given delivery Note: " + dn.Substring(0,dn.Length-2)]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_22">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">PONOTRECEIVED</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                        </Sequence>
                                      </If.Else>
                                    </If>
                                  </Sequence>
                                </If.Else>
                              </If>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_23">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:Boolean">[dnExists]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                </Assign.Value>
                              </Assign>
                            </Sequence>
                          </If.Else>
                        </If>
                      </Sequence>
                    </If.Then>
                    <If.Else>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_16">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_24">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[CType(linesOcrWorkflowOutput("commentStatus"), String)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_25">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[CType(linesOcrWorkflowOutput("Status"), String)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_26">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Boolean">[dnExists]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                          </Assign.Value>
                        </Assign>
                      </Sequence>
                    </If.Else>
                  </If>
                </Sequence>
              </Sequence>
            </ActivityAction>
          </ForEach>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_27">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(x:String)">[vendorList]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(x:String)">[M3TotalTableRows.Where(Function(arr) arr.Length &gt; 1). Select(Function(arr) arr(7)).Distinct().ToList()]</InArgument>
            </Assign.Value>
          </Assign>
          <If Condition="[vendorList.Count &gt; 1 AND deliveryNumbers.Count &lt;&gt; 1]" DisplayName="vendor ID If" sap2010:WorkflowViewState.IdRef="If_14">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_22">
                <Sequence.Variables>
                  <Variable x:TypeArguments="x:String" Name="vendorStr" />
                </Sequence.Variables>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_28">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[vendorStr]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">
                      <Literal x:TypeArguments="x:String" Value="" />
                    </InArgument>
                  </Assign.Value>
                </Assign>
                <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_4" Values="[vendorList]">
                  <ActivityAction x:TypeArguments="x:String">
                    <ActivityAction.Argument>
                      <DelegateInArgument x:TypeArguments="x:String" Name="item" />
                    </ActivityAction.Argument>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_19">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_29">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[vendorStr]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[vendorStr + item + ";"]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                  </ActivityAction>
                </ForEach>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_30">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[vendorStr]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[vendorStr.SubString(0,vendorStr.length-1)]</InArgument>
                  </Assign.Value>
                </Assign>
                <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;vendorName&quot;,ocrValues(8)},{&quot;vendorStr&quot;,vendorStr}}]" ContinueOnError="True" DisplayName="VendorID Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_4" OutputArguments="[vendorResponseDictionary]" ResponseCode="[vendorResponseCode]" WorkflowFile="[projectPath+&quot;\vendorID.xaml&quot;]" />
                <If Condition="[vendorResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_12">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_21">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_31">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("vendorID"), String)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <If Condition="[NOT vendorId = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_11">
                        <If.Then>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_20">
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_32">
                              <Assign.To>
                                <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows.Where(Function(arr) arr.Length &gt; 1 AndAlso arr(7) = vendorId).ToList()]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </Sequence>
                        </If.Then>
                      </If>
                    </Sequence>
                  </If.Then>
                </If>
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_23">
                <If Condition="[vendorList.Count = 1]" sap2010:WorkflowViewState.IdRef="If_13">
                  <If.Then>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_33">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[M3TotalTableRows(0)(7)]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </If.Then>
                </If>
              </Sequence>
            </If.Else>
          </If>
          <If Condition="[vendorID = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_17">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_26">
                <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;VendorName&quot;,ocrValues(8)},{&quot;VendorAddress&quot;,ocrValues(12)},{&quot;VendorPhone&quot;,ocrValues(13)}}]" ContinueOnError="True" DisplayName="VendorAddress Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_5" OutputArguments="[vendorResponseDictionary]" ResponseCode="[vendorResponseCode]" WorkflowFile="[projectPath+&quot;\vendorIDAddressMatch.xaml&quot;]" />
                <If Condition="[vendorResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_15">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_24">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_34">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("vendorID"), String)]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                  </If.Then>
                </If>
                <If Condition="[vendorId &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_16">
                  <If.Else>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_25">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_35">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">Missing required Information</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_36">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">MISSINGINFORMATION</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                  </If.Else>
                </If>
              </Sequence>
            </If.Then>
          </If>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Log " sap2010:WorkflowViewState.IdRef="Append_Line_2" Line="[&quot;Vendor ID: &quot; + vendorId]" Source="[logFile]" />
        </Sequence>
      </If.Then>
    </If>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="2637.***********,62" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="2637.***********,62" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="2637.***********,62" />
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="2637.***********,62" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="2637.***********,62" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="2562.66666666667,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_1" sap:VirtualizedContainerService.HintSize="2562.66666666667,22" />
      <sap2010:ViewStateData Id="Assign_7" sap:VirtualizedContainerService.HintSize="2250.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="2250.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="2250.66666666667,62" />
      <sap2010:ViewStateData Id="InvokeMethod_1" sap:VirtualizedContainerService.HintSize="217.***********3,134" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="239.***********3,258">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_1" sap:VirtualizedContainerService.HintSize="284.666666666667,410.666666666667" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="306.666666666667,534.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="464,688.666666666667" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="486,812.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="1716.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_11" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_12" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_13" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_4" sap:VirtualizedContainerService.HintSize="264,390">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_14" sap:VirtualizedContainerService.HintSize="1092.66666666667,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_2" sap:VirtualizedContainerService.HintSize="1092.66666666667,22" />
      <sap2010:ViewStateData Id="Assign_15" sap:VirtualizedContainerService.HintSize="944.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_16" sap:VirtualizedContainerService.HintSize="944.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_17" sap:VirtualizedContainerService.HintSize="944.666666666667,62" />
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="284.666666666667,22" />
      <sap2010:ViewStateData Id="InvokeMethod_2" sap:VirtualizedContainerService.HintSize="217.***********3,134" />
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="239.***********3,258">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_2" sap:VirtualizedContainerService.HintSize="284.666666666667,410.666666666667" />
      <sap2010:ViewStateData Id="Sequence_6" sap:VirtualizedContainerService.HintSize="306.666666666667,596.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_3" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_18" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="464,340" />
      <sap2010:ViewStateData Id="Assign_19" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_20" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_8" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="464,442" />
      <sap2010:ViewStateData Id="Sequence_9" sap:VirtualizedContainerService.HintSize="486,1008">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="818.666666666667,1162" />
      <sap2010:ViewStateData Id="If_5" sap:VirtualizedContainerService.HintSize="944.666666666667,1316" />
      <sap2010:ViewStateData Id="Sequence_10" sap:VirtualizedContainerService.HintSize="966.666666666667,1746">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_6" sap:VirtualizedContainerService.HintSize="1092.66666666667,1900" />
      <sap2010:ViewStateData Id="Sequence_11" sap:VirtualizedContainerService.HintSize="1114.66666666667,2188">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_21" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_22" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_12" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_7" sap:VirtualizedContainerService.HintSize="1404.66666666667,2342" />
      <sap2010:ViewStateData Id="Sequence_13" sap:VirtualizedContainerService.HintSize="1426.66666666667,2466">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_8" sap:VirtualizedContainerService.HintSize="1716.66666666667,2620" />
      <sap2010:ViewStateData Id="Assign_23" sap:VirtualizedContainerService.HintSize="1716.66666666667,62" />
      <sap2010:ViewStateData Id="Sequence_14" sap:VirtualizedContainerService.HintSize="1738.66666666667,2948">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_9" sap:VirtualizedContainerService.HintSize="2250.66666666667,3102" />
      <sap2010:ViewStateData Id="Sequence_15" sap:VirtualizedContainerService.HintSize="2272.66666666667,3532">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_24" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_25" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_26" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_16" sap:VirtualizedContainerService.HintSize="264,390">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_10" sap:VirtualizedContainerService.HintSize="2562.66666666667,3686">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_17" sap:VirtualizedContainerService.HintSize="2584.66666666667,3974">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_18" sap:VirtualizedContainerService.HintSize="2606.66666666667,4098">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_3" sap:VirtualizedContainerService.HintSize="2637.***********,4250.66666666667" />
      <sap2010:ViewStateData Id="Assign_27" sap:VirtualizedContainerService.HintSize="2637.***********,62" />
      <sap2010:ViewStateData Id="Assign_28" sap:VirtualizedContainerService.HintSize="612,62" />
      <sap2010:ViewStateData Id="Assign_29" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_19" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_4" sap:VirtualizedContainerService.HintSize="612,338.666666666667" />
      <sap2010:ViewStateData Id="Assign_30" sap:VirtualizedContainerService.HintSize="612,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_4" sap:VirtualizedContainerService.HintSize="612,22" />
      <sap2010:ViewStateData Id="Assign_31" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_32" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_20" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_11" sap:VirtualizedContainerService.HintSize="464,340" />
      <sap2010:ViewStateData Id="Sequence_21" sap:VirtualizedContainerService.HintSize="486,566">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_12" sap:VirtualizedContainerService.HintSize="612,720" />
      <sap2010:ViewStateData Id="Sequence_22" sap:VirtualizedContainerService.HintSize="634,1488.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_33" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_13" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="Sequence_23" sap:VirtualizedContainerService.HintSize="486,340">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_14" sap:VirtualizedContainerService.HintSize="2637.***********,1642.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_5" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_34" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_24" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_15" sap:VirtualizedContainerService.HintSize="464,340" />
      <sap2010:ViewStateData Id="Assign_35" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_36" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_25" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_16" sap:VirtualizedContainerService.HintSize="464,442" />
      <sap2010:ViewStateData Id="Sequence_26" sap:VirtualizedContainerService.HintSize="486,1008">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_17" sap:VirtualizedContainerService.HintSize="2637.***********,1162">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_2" sap:VirtualizedContainerService.HintSize="2637.***********,22" />
      <sap2010:ViewStateData Id="Sequence_27" sap:VirtualizedContainerService.HintSize="2659.***********,7933.***********">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_18" sap:VirtualizedContainerService.HintSize="2785.***********,8087.***********" />
      <sap2010:ViewStateData Id="Sequence_28" sap:VirtualizedContainerService.HintSize="2807.***********,8211.***********">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="2847.***********,8331.***********" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>