{"name": "M3InvoiceProcessingGenAIV2", "description": "M3InvoiceProcessingGenAIV2", "main": "MainSequence.xaml", "dependencies": {"mscorlib": "[*******]", "PresentationFramework": "[*******]", "System.Xaml": "[*******]", "System": "[*******]", "System.Activities": "[*******]", "System.Activities.Presentation": "[*******]", "YDock": "[*******]", "System.Windows.Forms": "[*******]", "WindowsBase": "[*******]", "NLog": "[*******]", "PresentationCore": "[*******]", "Newtonsoft.Json": "[********]", "System.Drawing": "[*******]", "System.Workflow.Activities": "[*******]", "System.Core": "[*******]", "UiRecorder": "[*******]", "System.IO.Compression": "[*******]", "Microsoft.WindowsAPICodePack.Shell": "[*******]", "Microsoft.Web.WebView2.Core": "[1.0.818.41]", "Microsoft.Web.WebView2.WinForms": "[1.0.818.41]", "System.Net.Http": "[*******]", "System.Configuration": "[*******]", "System.Activities.Core.Presentation": "[*******]", "Microsoft.CSharp": "[*******]", "System.Xml": "[*******]", "System.Workflow.ComponentModel": "[*******]", "System.Xml.Linq": "[*******]", "System.IO.Compression.FileSystem": "[*******]", "RestSharp": "[106.12.0.0]"}, "packages": {"Infor.RPA.Utilities": "[*******]", "Infor.RPA.Utility.Editor": "[*******]", "Infor.Activities.Web": "[*******]", "Infor.Activities.Desktop": "[*******]", "Infor.Activities.Debug": "[*******]", "Infor.Activities.Email": "[*******]", "Infor.Activities.Excel": "[*******]", "Infor.Activities.HTTPRequests": "[*******]", "Infor.Activities.IONAPI": "[*******]", "Infor.Activities.OCR": "[*******]", "Infor.RPA.OCR": "[*******]", "Infor.Activities.OneDrive": "[*******]", "Infor.Activities.Sys": "[*******]", "Infor.Activities.Datatable": "[*******]", "Infor.Activities.SharePointList": "[*******]", "Infor.Activities.Workflow": "[*******]", "Infor.Activities.ExcelOnline": "[*******]", "Infor.RPA.Security": "[*******]", "Infor.RPA.Commons": "[*******]"}, "schemaVersion": "1.0", "studioVersion": "2025.05.5\r\n.688", "sourceFiles": [{"fileName": "AddCharge.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "AddHead.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "AddLine.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "approval.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "ApprovalGUID.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "APResp.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "CallColeman.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "CheckApproval.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "CreateDirectories.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "DivisionInfo.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "DivisiontoGLCode.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "DivisionVendorTolerance.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "ExportMI.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "ExtractFromDatalake - Copy.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "GenAI - Classification.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "GenAI.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "GenAI_UnExpInv.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "getGenAIPrompt.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "GetOCRValues.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "getvendordetails.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "LinesExtractionWithPO.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "MainSequence.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "MoveFileToSuccessFailureFolder.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "OneDeliveryNotFound.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "Outlook_parallel.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "preprocess.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "ProcessDeliveryNote.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "ProcessDocument.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "ProcessExpenseInvoice - Copy.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "ProcessExpenseInvoice.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "ProcessExpenseWithPO.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "ProcessPOInvoice.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "ProcessPOInvoiceAPI.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "ProcessPOInvoiceAPI_Ind.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "ReadFilesFromFolder.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "RowsFromDeliveryNote.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "SendNotification.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "SendtoApprovalWorkflow.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "SendToIDM.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "SendToWidgetIDM.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "SentToInvoiceProcessingResults_Attributes.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "SentToInvoiceProcessingResults_Headers.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "SentToInvoiceProcessingResults_LineData.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "Split.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "SupplierInfo.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "vendorID.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "VendorIDAddressMatch.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}, {"fileName": "VerifyInvoiceExists.xaml", "filePath": "C:\\Users\\<USER>\\AppData\\Local\\InforRPA\\M3InvoiceProcessingGenAIV2"}], "resources": [], "projectType": "standard", "expressionLanguage": "VBscript", "backgroundProcess": false, "attended": true, "tenantMetadata": [{"tenantId": "MANDALA_DEM", "processId": "68678AF7-8BA8-4A33-8E79-E2F3482F8F38", "projectVersion": "1.0.15", "globalPackageNamespace": null, "iprRegistered": false}, {"tenantId": "M3CEDEVAPPAIS_DEV", "processId": "1886725C-390D-4BAC-8D0A-616A32B3F6B4", "projectVersion": "1.0.1", "globalPackageNamespace": null, "iprRegistered": false}, {"tenantId": "OLDCASTLE_DEV", "processId": "DFDF13FA-A711-4021-AB51-BCAC0F0FEBC2", "projectVersion": "1.0.37", "globalPackageNamespace": null, "iprRegistered": false}, {"tenantId": "YXH9SKXAKYFXSCVF_TST", "processId": "6D98713B-BEB3-45C6-BE73-77CC0BFBA429", "projectVersion": "1.0.8", "globalPackageNamespace": null, "iprRegistered": false}, {"tenantId": "ZGG6FMGYXSUKG7CA_TST", "processId": "4203367F-BF05-4059-8918-0E50DD4C1F83", "projectVersion": "1.0.4", "globalPackageNamespace": null, "iprRegistered": false}, {"tenantId": "ZGG6FMGYXSUKG7CA_PRD", "processId": "F13A4C4A-561F-4A9D-A014-E3597EE6BF3D", "projectVersion": "1.0.8", "globalPackageNamespace": null, "iprRegistered": false}, {"tenantId": "OLDCASTLE_PRD", "processId": "A9A77D16-96D0-423C-9CA3-751128456206", "projectVersion": "1.0.18", "globalPackageNamespace": null, "iprRegistered": false}]}