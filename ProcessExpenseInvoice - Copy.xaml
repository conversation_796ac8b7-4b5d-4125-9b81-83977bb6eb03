﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow" this:Workflow.expenseAccountCodes="11200|NA|NA|NA|NA|NA|NA"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:this="clr-namespace:RehostedWorkflowDesigner"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="commentStatus" Type="OutArgument(x:String)" />
    <x:Property Name="division" Type="InOutArgument(x:String)" />
    <x:Property Name="company" Type="OutArgument(x:String)" />
    <x:Property Name="ocrValues" Type="InArgument(s:String[])" />
    <x:Property Name="authUser" Type="InArgument(x:String)" />
    <x:Property Name="expenseAccountCodes" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
    <x:Property Name="vendorName" Type="OutArgument(x:String)" />
    <x:Property Name="status" Type="OutArgument(x:String)" />
    <x:Property Name="vendorId" Type="InOutArgument(x:String)" />
    <x:Property Name="approvalRequired" Type="InArgument(x:Boolean)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="checkAmountBussinessRule" Type="InArgument(x:String)" />
    <x:Property Name="approvalWorkflow" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="GLCode" Type="InArgument(x:String)" />
    <x:Property Name="correlationID" Type="OutArgument(x:String)" />
    <x:Property Name="ocrLineValues" Type="InArgument(scg:List(scg:List(x:String)))" />
    <x:Property Name="InYear" Type="OutArgument(x:String)" />
    <x:Property Name="AYear" Type="OutArgument(x:String)" />
    <x:Property Name="approvalList" Type="OutArgument(scg:List(x:String))" />
    <x:Property Name="APResp" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>System</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System.Globalization</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_1">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:Int32" Name="StatusCode1" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj1" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode0" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj0" />
      <Variable x:TypeArguments="njl:JToken" Name="out0" />
      <Variable x:TypeArguments="x:String" Name="cucd" />
      <Variable x:TypeArguments="x:String" Name="pyme" />
      <Variable x:TypeArguments="x:String" Name="tepy" />
      <Variable x:TypeArguments="x:String" Name="cuam" />
      <Variable x:TypeArguments="x:String" Name="sino" />
      <Variable x:TypeArguments="x:String" Name="ivdate" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode2" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj2" />
      <Variable x:TypeArguments="x:String" Name="amt" />
      <Variable x:TypeArguments="s:String[]" Name="expenseCodeList" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="invoiceExistsOcrWorkflowOutput" />
      <Variable x:TypeArguments="x:Int32" Name="invoiceExistsOcrWorkflowStatus" />
      <Variable x:TypeArguments="x:Boolean" Name="InvoiceAlreadyExists" />
      <Variable x:TypeArguments="njl:JToken" Name="out1" />
    </Sequence.Variables>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_166">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(x:String)">[approvalList]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(x:String)">[new list(Of string)]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_38">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[vendorId.ToUpper()]</InArgument>
      </Assign.Value>
    </Assign>
    <Sequence DisplayName="Fiscal year Sequence" sap2010:WorkflowViewState.IdRef="Sequence_49">
      <Sequence.Variables>
        <Variable x:TypeArguments="iru:ResponseObject" Name="respForFiscalYear" />
        <Variable x:TypeArguments="x:Int32" Name="respForFiscalYearCode" />
      </Sequence.Variables>
      <Assign DisplayName="Assign ivdate" sap2010:WorkflowViewState.IdRef="Assign_112">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[ivdate]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">[DateTime.ParseExact(ocrValues(1),"dd/MM/yyyy",Nothing).ToString("yyyyMMdd")]</InArgument>
        </Assign.Value>
      </Assign>
      <Assign sap2010:WorkflowViewState.IdRef="Assign_113">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[InYear]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">[ivdate.SubString(0,4)]</InArgument>
        </Assign.Value>
      </Assign>
      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Get Fiscal year IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_22" Response="[respForFiscalYear]" StatusCode="[respForFiscalYearCode]" Url="[tenantID+&quot;M3/m3api-rest/v2/execute/CRS900MI/GetSysCalDate&quot;]">
        <iai:IONAPIRequestWizard.Headers>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>Accept</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>application/json</x:String>
            </scg:List>
          </scg:List>
        </iai:IONAPIRequestWizard.Headers>
        <iai:IONAPIRequestWizard.QueryParameters>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>YMD8</x:String>
              <x:String>DIVI</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>ivdate</x:String>
              <x:String>division</x:String>
            </scg:List>
          </scg:List>
        </iai:IONAPIRequestWizard.QueryParameters>
      </iai:IONAPIRequestWizard>
      <If Condition="[respForFiscalYearCode = 200]" sap2010:WorkflowViewState.IdRef="If_33">
        <If.Then>
          <If Condition="[respForFiscalYear.ReadAsJson(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_32">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_47">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_114">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[AYear]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[ivdate.SubString(0,4)]</InArgument>
                  </Assign.Value>
                </Assign>
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_48">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_115">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[AYear]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[respForFiscalYear.ReadAsJson("results")(0)("records")(0)("CYP1").ToString.Substring(0,4)]</InArgument>
                  </Assign.Value>
                </Assign>
              </Sequence>
            </If.Else>
          </If>
        </If.Then>
      </If>
    </Sequence>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_45">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[company]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[miscValues("company").ToString]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_65">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[division]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[division]</InArgument>
      </Assign.Value>
    </Assign>
    <If Condition="[ocrValues(3) = &quot;&quot; OR OCrValues(3)= &quot;0&quot;]" sap2010:WorkflowViewState.IdRef="If_57">
      <If.Then>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_165">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[ocrValues(3)]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[ocrValues(2)]</InArgument>
          </Assign.Value>
        </Assign>
      </If.Then>
    </If>
    <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;SupInvoiceNo&quot;,ocrValues(0)},{&quot;division&quot;,division},{&quot;AYear&quot;,AYear},{&quot;vendorID&quot;,vendorID}}]" ContinueOnError="True" DisplayName="VerifyInvoiceExists Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_3" OutputArguments="[invoiceExistsOcrWorkflowOutput]" ResponseCode="[invoiceExistsOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\VerifyInvoiceExists.xaml&quot;]" />
    <Assign sap2010:WorkflowViewState.IdRef="Assign_66">
      <Assign.To>
        <OutArgument x:TypeArguments="x:Boolean">[InvoiceAlreadyExists]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:Boolean">[CType(invoiceExistsOcrWorkflowOutput("invoiceExist"),boolean)]</InArgument>
      </Assign.Value>
    </Assign>
    <If Condition="[NOT InvoiceAlreadyExists]" sap2010:WorkflowViewState.IdRef="If_20">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_30">
          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for vendor name" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_2" Response="[respObj0]" StatusCode="[StatusCode0]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/CRS620MI/GetBasicData&quot;]">
            <iai:IONAPIRequestWizard.Headers>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>Accept</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>application/json</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.Headers>
            <iai:IONAPIRequestWizard.QueryParameters>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="8">
                  <x:String>extendedresult</x:String>
                  <x:String>format</x:String>
                  <x:String>righttrim</x:String>
                  <x:String>excludeempty</x:String>
                  <x:String>dateformat</x:String>
                  <x:String>SUNO</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="8">
                  <x:String>false</x:String>
                  <x:String>PRETTY</x:String>
                  <x:String>true</x:String>
                  <x:String>false</x:String>
                  <x:String>YMD8</x:String>
                  <x:String>vendorId</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.QueryParameters>
          </iai:IONAPIRequestWizard>
          <If Condition="[StatusCode0 = 200]" sap2010:WorkflowViewState.IdRef="If_1">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_2">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_1">
                  <Assign.To>
                    <OutArgument x:TypeArguments="njl:JToken">[out0]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj0.ReadAsText)]</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[out0(&quot;results&quot;)(0)(&quot;records&quot;).ToString &lt;&gt; &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_2">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_6">
                      <Sequence.Variables>
                        <Variable x:TypeArguments="x:String" Name="geoc" />
                        <Variable x:TypeArguments="x:String" Name="discountTerms" />
                      </Sequence.Variables>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_4">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_4">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[company]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("CONO").ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_5">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[tepy]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("TEPY").ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_6">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[pyme]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("PYME").ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_7">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[cucd]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("CUCD").ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_51">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[vendorName]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("SUNM").ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_37">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[geoc]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("GEOC").ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_170">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[discountTerms]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("TECD").ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <If Condition="[miscValues(&quot;BusinessRuleAPResp&quot;).ToString.ToUpper() &lt;&gt; &quot;NA&quot;]" sap2010:WorkflowViewState.IdRef="If_61">
                          <If.Then>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_72">
                              <Sequence.Variables>
                                <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="APRespRespDict" />
                                <Variable x:TypeArguments="x:Int32" Name="APRespRespCode" />
                              </Sequence.Variables>
                              <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;division&quot;,division},{&quot;company&quot;,company},{&quot;VendorID&quot;,vendorID}}]" ContinueOnError="True" DisplayName="AP resp Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_8" OutputArguments="[APRespRespDict]" ResponseCode="[APRespRespCode]" WorkflowFile="[projectPath+&quot;\APResp.xaml&quot;]" />
                              <If Condition="[APRespRespCode = 200]" sap2010:WorkflowViewState.IdRef="If_59">
                                <If.Then>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_168">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[APResp]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[CType(APRespRespDict("APResp"), String)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </If.Then>
                              </If>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_169">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[authUser]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[APResp]</InArgument>
                                </Assign.Value>
                              </Assign>
                            </Sequence>
                          </If.Then>
                          <If.Else>
                            <If Condition="[authUser.ToUpper() = &quot;SUPPLIERAUTHUSER&quot;]" sap2010:WorkflowViewState.IdRef="If_60">
                              <If.Then>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_73">
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_11">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[authUser]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("RESP").ToString]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </Sequence>
                              </If.Then>
                            </If>
                          </If.Else>
                        </If>
                      </Sequence>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_5">
                        <Sequence.Variables>
                          <Variable x:TypeArguments="x:Boolean" Name="InvoiceAlreadyExists" />
                          <Variable x:TypeArguments="x:String" Name="inbnValue" />
                          <Variable x:TypeArguments="x:String" Name="SupAcho" />
                          <Variable x:TypeArguments="x:Int32" Name="StatusCode4" />
                          <Variable x:TypeArguments="iru:ResponseObject" Name="respObj4" />
                          <Variable x:TypeArguments="njl:JToken" Name="out4" />
                          <Variable x:TypeArguments="x:String" Name="bkid" />
                          <Variable x:TypeArguments="s:DateTime" Name="ivdate1" />
                        </Sequence.Variables>
                        <Assign DisplayName="Assign SupAcho" sap2010:WorkflowViewState.IdRef="Assign_43">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[SupAcho]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">["ACHO:"+vendorId + " AND BKTP: 03"]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for bkid" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_6" Response="[respObj4]" StatusCode="[StatusCode4]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/CRS692MI/SearchBnkAcc&quot;]">
                          <iai:IONAPIRequestWizard.Headers>
                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                <x:String>Accept</x:String>
                              </scg:List>
                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                <x:String>application/json</x:String>
                              </scg:List>
                            </scg:List>
                          </iai:IONAPIRequestWizard.Headers>
                          <iai:IONAPIRequestWizard.QueryParameters>
                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                              <scg:List x:TypeArguments="x:String" Capacity="8">
                                <x:String>SQRY</x:String>
                                <x:String>dateformat</x:String>
                                <x:String>excludeempty</x:String>
                                <x:String>righttrim</x:String>
                                <x:String>format</x:String>
                                <x:String>extendedresult</x:String>
                              </scg:List>
                              <scg:List x:TypeArguments="x:String" Capacity="8">
                                <x:String>SupAcho</x:String>
                                <x:String>YMD8</x:String>
                                <x:String>false</x:String>
                                <x:String>true</x:String>
                                <x:String>PRETTY</x:String>
                                <x:String>false</x:String>
                              </scg:List>
                            </scg:List>
                          </iai:IONAPIRequestWizard.QueryParameters>
                        </iai:IONAPIRequestWizard>
                        <If Condition="[StatusCode4 = 200]" sap2010:WorkflowViewState.IdRef="If_11">
                          <If.Then>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_21">
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_39">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="njl:JToken">[out4]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj4.ReadAsText)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <If Condition="[out4(&quot;results&quot;)(0)(&quot;records&quot;).ToString &lt;&gt; &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_10">
                                <If.Then>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_19">
                                    <Assign DisplayName="Assign bkid" sap2010:WorkflowViewState.IdRef="Assign_40">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[bkid]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">[(out4("results")(0)("records")(0)("BKID")).ToString]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                  </Sequence>
                                </If.Then>
                                <If.Else>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_20">
                                    <Assign DisplayName="Assign bkid" sap2010:WorkflowViewState.IdRef="Assign_41">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[bkid]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">
                                          <Literal x:TypeArguments="x:String" Value="" />
                                        </InArgument>
                                      </Assign.Value>
                                    </Assign>
                                  </Sequence>
                                </If.Else>
                              </If>
                            </Sequence>
                          </If.Then>
                        </If>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_10">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[cuam]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[ocrValues(2)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_9">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[sino]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[ocrValues(0)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign DisplayName="Assign ivdate" sap2010:WorkflowViewState.IdRef="Assign_64">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[ivdate]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[DateTime.ParseExact(ocrValues(1), "dd/MM/yyyy", Nothing).ToString("yyyyMMdd")]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_70">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[System.DateTime.Now.ToString("yyyyMMddHHmmssfff").tostring + " -"+ ocrValues(0) + "-" + division + "-" + vendorID]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_1">
                          <TryCatch.Try>
                            <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_110">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[correlationID.Substring(0,35)]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </TryCatch.Try>
                          <TryCatch.Catches>
                            <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_1">
                              <ActivityAction x:TypeArguments="s:Exception">
                                <ActivityAction.Argument>
                                  <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                </ActivityAction.Argument>
                                <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_111">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[correlationID]</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </ActivityAction>
                            </Catch>
                          </TryCatch.Catches>
                        </TryCatch>
                        <If Condition="[miscValues(&quot;handleCashDiscount&quot;).ToString.tolower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_62">
                          <If.Then>
                            <If Condition="[miscValues(&quot;mandateGEOC&quot;).ToString.tolower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_23">
                              <If.Then>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_32">
                                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_15" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                    <iai:IONAPIRequestWizard.Headers>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>Accept</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>application/json</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </iai:IONAPIRequestWizard.Headers>
                                    <iai:IONAPIRequestWizard.QueryParameters>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="32">
                                          <x:String>SUNO</x:String>
                                          <x:String>IVDT</x:String>
                                          <x:String>DIVI</x:String>
                                          <x:String>SINO</x:String>
                                          <x:String>CUCD</x:String>
                                          <x:String>TEPY</x:String>
                                          <x:String>PYME</x:String>
                                          <x:String>CUAM</x:String>
                                          <x:String>IMCD</x:String>
                                          <x:String>CRTP</x:String>
                                          <x:String>dateformat</x:String>
                                          <x:String>excludeempty</x:String>
                                          <x:String>righttrim</x:String>
                                          <x:String>format</x:String>
                                          <x:String>extendedresult</x:String>
                                          <x:String>APCD</x:String>
                                          <x:String>GEOC</x:String>
                                          <x:String>CORI</x:String>
                                          <x:String>TECD</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="32">
                                          <x:String>vendorId</x:String>
                                          <x:String>ivdate</x:String>
                                          <x:String>division</x:String>
                                          <x:String>sino</x:String>
                                          <x:String>cucd</x:String>
                                          <x:String>tepy</x:String>
                                          <x:String>pyme</x:String>
                                          <x:String>cuam</x:String>
                                          <x:String>0</x:String>
                                          <x:String>1</x:String>
                                          <x:String>YMD8</x:String>
                                          <x:String>false</x:String>
                                          <x:String>true</x:String>
                                          <x:String>PRETTY</x:String>
                                          <x:String>false</x:String>
                                          <x:String>authUser</x:String>
                                          <x:String>geoc</x:String>
                                          <x:String>correlationID</x:String>
                                          <x:String>discountTerms</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </iai:IONAPIRequestWizard.QueryParameters>
                                  </iai:IONAPIRequestWizard>
                                  <If Condition="[StatusCode1 = 200  and respObj1.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" sap2010:WorkflowViewState.IdRef="If_24">
                                    <If.Then>
                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_35">
                                        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_16" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                          <iai:IONAPIRequestWizard.Headers>
                                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                <x:String>Accept</x:String>
                                              </scg:List>
                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                <x:String>application/json</x:String>
                                              </scg:List>
                                            </scg:List>
                                          </iai:IONAPIRequestWizard.Headers>
                                          <iai:IONAPIRequestWizard.QueryParameters>
                                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                              <scg:List x:TypeArguments="x:String" Capacity="32">
                                                <x:String>SUNO</x:String>
                                                <x:String>IVDT</x:String>
                                                <x:String>DIVI</x:String>
                                                <x:String>SINO</x:String>
                                                <x:String>CUCD</x:String>
                                                <x:String>TEPY</x:String>
                                                <x:String>PYME</x:String>
                                                <x:String>CUAM</x:String>
                                                <x:String>IMCD</x:String>
                                                <x:String>CRTP</x:String>
                                                <x:String>dateformat</x:String>
                                                <x:String>excludeempty</x:String>
                                                <x:String>righttrim</x:String>
                                                <x:String>format</x:String>
                                                <x:String>extendedresult</x:String>
                                                <x:String>APCD</x:String>
                                                <x:String>BKID</x:String>
                                                <x:String>GEOC</x:String>
                                                <x:String>CORI</x:String>
                                                <x:String>TECD</x:String>
                                              </scg:List>
                                              <scg:List x:TypeArguments="x:String" Capacity="32">
                                                <x:String>vendorId</x:String>
                                                <x:String>ivdate</x:String>
                                                <x:String>division</x:String>
                                                <x:String>sino</x:String>
                                                <x:String>cucd</x:String>
                                                <x:String>tepy</x:String>
                                                <x:String>pyme</x:String>
                                                <x:String>cuam</x:String>
                                                <x:String>0</x:String>
                                                <x:String>1</x:String>
                                                <x:String>YMD8</x:String>
                                                <x:String>false</x:String>
                                                <x:String>true</x:String>
                                                <x:String>PRETTY</x:String>
                                                <x:String>false</x:String>
                                                <x:String>authUser</x:String>
                                                <x:String>bkid</x:String>
                                                <x:String>geoc</x:String>
                                                <x:String>correlationID</x:String>
                                                <x:String>discountTerms</x:String>
                                              </scg:List>
                                            </scg:List>
                                          </iai:IONAPIRequestWizard.QueryParameters>
                                        </iai:IONAPIRequestWizard>
                                      </Sequence>
                                    </If.Then>
                                  </If>
                                </Sequence>
                              </If.Then>
                              <If.Else>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_34">
                                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_17" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                    <iai:IONAPIRequestWizard.Headers>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>Accept</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>application/json</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </iai:IONAPIRequestWizard.Headers>
                                    <iai:IONAPIRequestWizard.QueryParameters>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="32">
                                          <x:String>SUNO</x:String>
                                          <x:String>IVDT</x:String>
                                          <x:String>DIVI</x:String>
                                          <x:String>SINO</x:String>
                                          <x:String>CUCD</x:String>
                                          <x:String>TEPY</x:String>
                                          <x:String>PYME</x:String>
                                          <x:String>CUAM</x:String>
                                          <x:String>IMCD</x:String>
                                          <x:String>CRTP</x:String>
                                          <x:String>dateformat</x:String>
                                          <x:String>excludeempty</x:String>
                                          <x:String>righttrim</x:String>
                                          <x:String>format</x:String>
                                          <x:String>extendedresult</x:String>
                                          <x:String>APCD</x:String>
                                          <x:String>CORI</x:String>
                                          <x:String>TECD</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="32">
                                          <x:String>vendorId</x:String>
                                          <x:String>ivdate</x:String>
                                          <x:String>division</x:String>
                                          <x:String>sino</x:String>
                                          <x:String>cucd</x:String>
                                          <x:String>tepy</x:String>
                                          <x:String>pyme</x:String>
                                          <x:String>cuam</x:String>
                                          <x:String>0</x:String>
                                          <x:String>1</x:String>
                                          <x:String>YMD8</x:String>
                                          <x:String>false</x:String>
                                          <x:String>true</x:String>
                                          <x:String>PRETTY</x:String>
                                          <x:String>false</x:String>
                                          <x:String>authUser</x:String>
                                          <x:String>correlationID</x:String>
                                          <x:String>discountTerms</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </iai:IONAPIRequestWizard.QueryParameters>
                                  </iai:IONAPIRequestWizard>
                                  <If Condition="[StatusCode1 = 200  and respObj1.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" sap2010:WorkflowViewState.IdRef="If_25">
                                    <If.Then>
                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_36">
                                        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_18" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                          <iai:IONAPIRequestWizard.Headers>
                                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                <x:String>Accept</x:String>
                                              </scg:List>
                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                <x:String>application/json</x:String>
                                              </scg:List>
                                            </scg:List>
                                          </iai:IONAPIRequestWizard.Headers>
                                          <iai:IONAPIRequestWizard.QueryParameters>
                                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                              <scg:List x:TypeArguments="x:String" Capacity="32">
                                                <x:String>SUNO</x:String>
                                                <x:String>IVDT</x:String>
                                                <x:String>DIVI</x:String>
                                                <x:String>SINO</x:String>
                                                <x:String>CUCD</x:String>
                                                <x:String>TEPY</x:String>
                                                <x:String>PYME</x:String>
                                                <x:String>CUAM</x:String>
                                                <x:String>IMCD</x:String>
                                                <x:String>CRTP</x:String>
                                                <x:String>dateformat</x:String>
                                                <x:String>excludeempty</x:String>
                                                <x:String>righttrim</x:String>
                                                <x:String>format</x:String>
                                                <x:String>extendedresult</x:String>
                                                <x:String>APCD</x:String>
                                                <x:String>BKID</x:String>
                                                <x:String>CORI</x:String>
                                                <x:String>TECD</x:String>
                                              </scg:List>
                                              <scg:List x:TypeArguments="x:String" Capacity="32">
                                                <x:String>vendorId</x:String>
                                                <x:String>ivdate</x:String>
                                                <x:String>division</x:String>
                                                <x:String>sino</x:String>
                                                <x:String>cucd</x:String>
                                                <x:String>tepy</x:String>
                                                <x:String>pyme</x:String>
                                                <x:String>cuam</x:String>
                                                <x:String>0</x:String>
                                                <x:String>1</x:String>
                                                <x:String>YMD8</x:String>
                                                <x:String>false</x:String>
                                                <x:String>true</x:String>
                                                <x:String>PRETTY</x:String>
                                                <x:String>false</x:String>
                                                <x:String>authUser</x:String>
                                                <x:String>bkid</x:String>
                                                <x:String>correlationID</x:String>
                                                <x:String>discountTerms</x:String>
                                              </scg:List>
                                            </scg:List>
                                          </iai:IONAPIRequestWizard.QueryParameters>
                                        </iai:IONAPIRequestWizard>
                                      </Sequence>
                                    </If.Then>
                                  </If>
                                </Sequence>
                              </If.Else>
                            </If>
                          </If.Then>
                          <If.Else>
                            <If Condition="[miscValues(&quot;mandateGEOC&quot;).ToString.tolower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_65">
                              <If.Then>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_75">
                                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_26" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                    <iai:IONAPIRequestWizard.Headers>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>Accept</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>application/json</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </iai:IONAPIRequestWizard.Headers>
                                    <iai:IONAPIRequestWizard.QueryParameters>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="32">
                                          <x:String>SUNO</x:String>
                                          <x:String>IVDT</x:String>
                                          <x:String>DIVI</x:String>
                                          <x:String>SINO</x:String>
                                          <x:String>CUCD</x:String>
                                          <x:String>TEPY</x:String>
                                          <x:String>PYME</x:String>
                                          <x:String>CUAM</x:String>
                                          <x:String>IMCD</x:String>
                                          <x:String>CRTP</x:String>
                                          <x:String>dateformat</x:String>
                                          <x:String>excludeempty</x:String>
                                          <x:String>righttrim</x:String>
                                          <x:String>format</x:String>
                                          <x:String>extendedresult</x:String>
                                          <x:String>APCD</x:String>
                                          <x:String>GEOC</x:String>
                                          <x:String>CORI</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="32">
                                          <x:String>vendorId</x:String>
                                          <x:String>ivdate</x:String>
                                          <x:String>division</x:String>
                                          <x:String>sino</x:String>
                                          <x:String>cucd</x:String>
                                          <x:String>tepy</x:String>
                                          <x:String>pyme</x:String>
                                          <x:String>cuam</x:String>
                                          <x:String>0</x:String>
                                          <x:String>1</x:String>
                                          <x:String>YMD8</x:String>
                                          <x:String>false</x:String>
                                          <x:String>true</x:String>
                                          <x:String>PRETTY</x:String>
                                          <x:String>false</x:String>
                                          <x:String>authUser</x:String>
                                          <x:String>geoc</x:String>
                                          <x:String>correlationID</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </iai:IONAPIRequestWizard.QueryParameters>
                                  </iai:IONAPIRequestWizard>
                                  <If Condition="[StatusCode1 = 200  and respObj1.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" sap2010:WorkflowViewState.IdRef="If_63">
                                    <If.Then>
                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_74">
                                        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_27" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                          <iai:IONAPIRequestWizard.Headers>
                                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                <x:String>Accept</x:String>
                                              </scg:List>
                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                <x:String>application/json</x:String>
                                              </scg:List>
                                            </scg:List>
                                          </iai:IONAPIRequestWizard.Headers>
                                          <iai:IONAPIRequestWizard.QueryParameters>
                                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                              <scg:List x:TypeArguments="x:String" Capacity="32">
                                                <x:String>SUNO</x:String>
                                                <x:String>IVDT</x:String>
                                                <x:String>DIVI</x:String>
                                                <x:String>SINO</x:String>
                                                <x:String>CUCD</x:String>
                                                <x:String>TEPY</x:String>
                                                <x:String>PYME</x:String>
                                                <x:String>CUAM</x:String>
                                                <x:String>IMCD</x:String>
                                                <x:String>CRTP</x:String>
                                                <x:String>dateformat</x:String>
                                                <x:String>excludeempty</x:String>
                                                <x:String>righttrim</x:String>
                                                <x:String>format</x:String>
                                                <x:String>extendedresult</x:String>
                                                <x:String>APCD</x:String>
                                                <x:String>BKID</x:String>
                                                <x:String>GEOC</x:String>
                                                <x:String>CORI</x:String>
                                              </scg:List>
                                              <scg:List x:TypeArguments="x:String" Capacity="32">
                                                <x:String>vendorId</x:String>
                                                <x:String>ivdate</x:String>
                                                <x:String>division</x:String>
                                                <x:String>sino</x:String>
                                                <x:String>cucd</x:String>
                                                <x:String>tepy</x:String>
                                                <x:String>pyme</x:String>
                                                <x:String>cuam</x:String>
                                                <x:String>0</x:String>
                                                <x:String>1</x:String>
                                                <x:String>YMD8</x:String>
                                                <x:String>false</x:String>
                                                <x:String>true</x:String>
                                                <x:String>PRETTY</x:String>
                                                <x:String>false</x:String>
                                                <x:String>authUser</x:String>
                                                <x:String>bkid</x:String>
                                                <x:String>geoc</x:String>
                                                <x:String>correlationID</x:String>
                                              </scg:List>
                                            </scg:List>
                                          </iai:IONAPIRequestWizard.QueryParameters>
                                        </iai:IONAPIRequestWizard>
                                      </Sequence>
                                    </If.Then>
                                  </If>
                                </Sequence>
                              </If.Then>
                              <If.Else>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_77">
                                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_28" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                    <iai:IONAPIRequestWizard.Headers>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>Accept</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>application/json</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </iai:IONAPIRequestWizard.Headers>
                                    <iai:IONAPIRequestWizard.QueryParameters>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="32">
                                          <x:String>SUNO</x:String>
                                          <x:String>IVDT</x:String>
                                          <x:String>DIVI</x:String>
                                          <x:String>SINO</x:String>
                                          <x:String>CUCD</x:String>
                                          <x:String>TEPY</x:String>
                                          <x:String>PYME</x:String>
                                          <x:String>CUAM</x:String>
                                          <x:String>IMCD</x:String>
                                          <x:String>CRTP</x:String>
                                          <x:String>dateformat</x:String>
                                          <x:String>excludeempty</x:String>
                                          <x:String>righttrim</x:String>
                                          <x:String>format</x:String>
                                          <x:String>extendedresult</x:String>
                                          <x:String>APCD</x:String>
                                          <x:String>CORI</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="32">
                                          <x:String>vendorId</x:String>
                                          <x:String>ivdate</x:String>
                                          <x:String>division</x:String>
                                          <x:String>sino</x:String>
                                          <x:String>cucd</x:String>
                                          <x:String>tepy</x:String>
                                          <x:String>pyme</x:String>
                                          <x:String>cuam</x:String>
                                          <x:String>0</x:String>
                                          <x:String>1</x:String>
                                          <x:String>YMD8</x:String>
                                          <x:String>false</x:String>
                                          <x:String>true</x:String>
                                          <x:String>PRETTY</x:String>
                                          <x:String>false</x:String>
                                          <x:String>authUser</x:String>
                                          <x:String>correlationID</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </iai:IONAPIRequestWizard.QueryParameters>
                                  </iai:IONAPIRequestWizard>
                                  <If Condition="[StatusCode1 = 200  and respObj1.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" sap2010:WorkflowViewState.IdRef="If_64">
                                    <If.Then>
                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_76">
                                        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_29" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                          <iai:IONAPIRequestWizard.Headers>
                                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                <x:String>Accept</x:String>
                                              </scg:List>
                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                <x:String>application/json</x:String>
                                              </scg:List>
                                            </scg:List>
                                          </iai:IONAPIRequestWizard.Headers>
                                          <iai:IONAPIRequestWizard.QueryParameters>
                                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                              <scg:List x:TypeArguments="x:String" Capacity="32">
                                                <x:String>SUNO</x:String>
                                                <x:String>IVDT</x:String>
                                                <x:String>DIVI</x:String>
                                                <x:String>SINO</x:String>
                                                <x:String>CUCD</x:String>
                                                <x:String>TEPY</x:String>
                                                <x:String>PYME</x:String>
                                                <x:String>CUAM</x:String>
                                                <x:String>IMCD</x:String>
                                                <x:String>CRTP</x:String>
                                                <x:String>dateformat</x:String>
                                                <x:String>excludeempty</x:String>
                                                <x:String>righttrim</x:String>
                                                <x:String>format</x:String>
                                                <x:String>extendedresult</x:String>
                                                <x:String>APCD</x:String>
                                                <x:String>BKID</x:String>
                                                <x:String>CORI</x:String>
                                              </scg:List>
                                              <scg:List x:TypeArguments="x:String" Capacity="32">
                                                <x:String>vendorId</x:String>
                                                <x:String>ivdate</x:String>
                                                <x:String>division</x:String>
                                                <x:String>sino</x:String>
                                                <x:String>cucd</x:String>
                                                <x:String>tepy</x:String>
                                                <x:String>pyme</x:String>
                                                <x:String>cuam</x:String>
                                                <x:String>0</x:String>
                                                <x:String>1</x:String>
                                                <x:String>YMD8</x:String>
                                                <x:String>false</x:String>
                                                <x:String>true</x:String>
                                                <x:String>PRETTY</x:String>
                                                <x:String>false</x:String>
                                                <x:String>authUser</x:String>
                                                <x:String>bkid</x:String>
                                                <x:String>correlationID</x:String>
                                              </scg:List>
                                            </scg:List>
                                          </iai:IONAPIRequestWizard.QueryParameters>
                                        </iai:IONAPIRequestWizard>
                                      </Sequence>
                                    </If.Then>
                                  </If>
                                </Sequence>
                              </If.Else>
                            </If>
                          </If.Else>
                        </If>
                        <If Condition="[StatusCode1 = 200]" sap2010:WorkflowViewState.IdRef="If_5">
                          <If.Then>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_10">
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_12">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj1.ReadAsText)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_4">
                                <If.Then>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_8">
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_13">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:Boolean">[InvoiceAlreadyExists]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_14">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">[(out1("results")(0)("errorMessage")).ToString]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_15">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                  </Sequence>
                                </If.Then>
                                <If.Else>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_9">
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_16">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:Boolean">[InvoiceAlreadyExists]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_17">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[inbnValue]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">[(out1("results")(0)("records")(0)("INBN")).ToString]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_18">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">["Invoice Header Created"]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_6" Line="[commentStatus]" Source="[logfile]" />
                                  </Sequence>
                                </If.Else>
                              </If>
                              <If Condition="[NOT InvoiceAlreadyExists]" sap2010:WorkflowViewState.IdRef="If_6">
                                <If.Then>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_12">
                                    <Sequence.Variables>
                                      <Variable x:TypeArguments="x:String" Name="accRule" />
                                      <Variable x:TypeArguments="x:String" Name="productGr" />
                                      <Variable x:TypeArguments="x:String" Name="orderNo" />
                                      <Variable x:TypeArguments="x:String" Name="partnerCo" />
                                      <Variable x:TypeArguments="x:String" Name="finGrp" />
                                      <Variable x:TypeArguments="x:String" Name="costCtr" />
                                      <Variable x:TypeArguments="x:String" Name="account" />
                                      <Variable x:TypeArguments="x:String" Name="AIT1" />
                                      <Variable x:TypeArguments="x:String" Name="AIT2" />
                                      <Variable x:TypeArguments="x:String" Name="AIT3" />
                                      <Variable x:TypeArguments="x:String" Name="AIT4" />
                                      <Variable x:TypeArguments="x:String" Name="AIT5" />
                                      <Variable x:TypeArguments="x:String" Name="AIT6" />
                                      <Variable x:TypeArguments="x:String" Name="AIT7" />
                                    </Sequence.Variables>
                                    <If Condition="[GLCode = &quot;DISTRIBUTED&quot; AND ocrLineValues.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_29">
                                      <If.Then>
                                        <ForEach x:TypeArguments="scg:List(x:String)" DisplayName="ForEach&lt;List&lt;String&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_7" Values="[ocrLineValues]">
                                          <ActivityAction x:TypeArguments="scg:List(x:String)">
                                            <ActivityAction.Argument>
                                              <DelegateInArgument x:TypeArguments="scg:List(x:String)" Name="item" />
                                            </ActivityAction.Argument>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_40">
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_88">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[AIT1]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[item(1).Split("~"c)(0)]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_90">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[AIT2]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[item(1).Split("~"c)(1)]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_91">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[AIT3]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[item(1).Split("~"c)(2)]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_92">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[AIT4]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[item(1).Split("~"c)(3)]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_89">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[AIT5]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[item(1).Split("~"c)(4)]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_93">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[AIT6]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[item(1).Split("~"c)(5)]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_94">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[AIT7]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[item(1).Split("~"c)(6)]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_28">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[amt]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[item(4)]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_4" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                <iai:IONAPIRequestWizard.Headers>
                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>Accept</x:String>
                                                    </scg:List>
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>application/json</x:String>
                                                    </scg:List>
                                                  </scg:List>
                                                </iai:IONAPIRequestWizard.Headers>
                                                <iai:IONAPIRequestWizard.QueryParameters>
                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                    <scg:List x:TypeArguments="x:String" Capacity="16">
                                                      <x:String>INBN</x:String>
                                                      <x:String>RDTP</x:String>
                                                      <x:String>DIVI</x:String>
                                                      <x:String>NLAM</x:String>
                                                      <x:String>AIT1</x:String>
                                                      <x:String>AIT2</x:String>
                                                      <x:String>AIT3</x:String>
                                                      <x:String>AIT4</x:String>
                                                      <x:String>AIT5</x:String>
                                                      <x:String>AIT6</x:String>
                                                      <x:String>AIT7</x:String>
                                                    </scg:List>
                                                    <scg:List x:TypeArguments="x:String" Capacity="16">
                                                      <x:String>inbnValue</x:String>
                                                      <x:String>8</x:String>
                                                      <x:String>division</x:String>
                                                      <x:String>amt</x:String>
                                                      <x:String>AIT1</x:String>
                                                      <x:String>AIT2</x:String>
                                                      <x:String>AIT3</x:String>
                                                      <x:String>AIT4</x:String>
                                                      <x:String>AIT5</x:String>
                                                      <x:String>AIT6</x:String>
                                                      <x:String>AIT7</x:String>
                                                    </scg:List>
                                                  </scg:List>
                                                </iai:IONAPIRequestWizard.QueryParameters>
                                              </iai:IONAPIRequestWizard>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_72">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_26">
                                                <If.Then>
                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_37">
                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_74">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="x:String" xml:space="preserve">["Error occured while adding the line.  "  +  (out1("results")(0)("errorMessage")).ToString]</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_75">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_5" Line="[commentStatus]" Source="[logfile]" />
                                                  </Sequence>
                                                </If.Then>
                                                <If.Else>
                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_38">
                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_78">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="x:String">["Invoice Line created"]</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_79">
                                                      <Assign.To>
                                                        <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                      </Assign.To>
                                                      <Assign.Value>
                                                        <InArgument x:TypeArguments="x:String">["SUCCESS"]</InArgument>
                                                      </Assign.Value>
                                                    </Assign>
                                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_7" Line="[commentStatus]" Source="[logfile]" />
                                                  </Sequence>
                                                </If.Else>
                                              </If>
                                            </Sequence>
                                          </ActivityAction>
                                        </ForEach>
                                      </If.Then>
                                      <If.Else>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_43">
                                          <If Condition="[GLCode = &quot;DISTRIBUTED&quot;]" sap2010:WorkflowViewState.IdRef="If_30">
                                            <If.Then>
                                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_8" Line="[commentStatus]" Source="[logfile]" />
                                            </If.Then>
                                            <If.Else>
                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_44">
                                                <If Condition="[ocrLineValues.Count&gt;0]" DisplayName="If GLCode did not obtain from excep" sap2010:WorkflowViewState.IdRef="If_39">
                                                  <If.Then>
                                                    <If Condition="[ocrLineValues(0)(1).Contains(&quot;~&quot;)]" sap2010:WorkflowViewState.IdRef="If_40">
                                                      <If.Else>
                                                        <If Condition="[vendorId &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_48">
                                                          <If.Then>
                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_63">
                                                              <Sequence.Variables>
                                                                <Variable x:TypeArguments="scg:List(x:String)" Name="linelist" />
                                                                <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="vendorResponseDictionary" />
                                                                <Variable x:TypeArguments="x:Int32" Name="vendorResponseCode" />
                                                                <Variable x:TypeArguments="x:String" Name="invoiceType" />
                                                              </Sequence.Variables>
                                                              <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;vendorID&quot;,vendorID},{&quot;company&quot;,company},{&quot;division&quot;,division}}]" ContinueOnError="True" DisplayName="SupplierInfo Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_4" OutputArguments="[vendorResponseDictionary]" ResponseCode="[vendorResponseCode]" WorkflowFile="[projectPath+&quot;\SupplierInfo.xaml&quot;]" />
                                                              <If Condition="[vendorResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_43">
                                                                <If.Then>
                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_58">
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_143">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[invoiceType]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("invoiceType"), String)]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <If Condition="[invoiceType.ToLower.contains(&quot;non po&quot;) OR invoiceType.ToLower.contains(&quot;expense&quot;)]" sap2010:WorkflowViewState.IdRef="If_42">
                                                                      <If.Then>
                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_57">
                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_144">
                                                                            <Assign.To>
                                                                              <OutArgument x:TypeArguments="scg:List(scg:List(x:String))">[ocrLineValues]</OutArgument>
                                                                            </Assign.To>
                                                                            <Assign.Value>
                                                                              <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[New List(Of List(Of String))]</InArgument>
                                                                            </Assign.Value>
                                                                          </Assign>
                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_145">
                                                                            <Assign.To>
                                                                              <OutArgument x:TypeArguments="scg:List(x:String)">[linelist]</OutArgument>
                                                                            </Assign.To>
                                                                            <Assign.Value>
                                                                              <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
                                                                            </Assign.Value>
                                                                          </Assign>
                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_146">
                                                                            <Assign.To>
                                                                              <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                            </Assign.To>
                                                                            <Assign.Value>
                                                                              <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("GLCode"), String)]</InArgument>
                                                                            </Assign.Value>
                                                                          </Assign>
                                                                          <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_2">
                                                                            <TryCatch.Try>
                                                                              <If Condition="[invoiceType.ToLower.contains(&quot;non po&quot;) OR invoiceType.ToLower.contains(&quot;expense&quot;)]" sap2010:WorkflowViewState.IdRef="If_41">
                                                                                <If.Then>
                                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_56">
                                                                                    <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_1" MethodName="Add">
                                                                                      <InvokeMethod.TargetObject>
                                                                                        <InArgument x:TypeArguments="scg:List(x:String)">[linelist]</InArgument>
                                                                                      </InvokeMethod.TargetObject>
                                                                                      <InArgument x:TypeArguments="x:String">
                                                                                        <Literal x:TypeArguments="x:String" Value="" />
                                                                                      </InArgument>
                                                                                    </InvokeMethod>
                                                                                    <InvokeMethod DisplayName="Dimensions InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_2" MethodName="Add">
                                                                                      <InvokeMethod.TargetObject>
                                                                                        <InArgument x:TypeArguments="scg:List(x:String)">[linelist]</InArgument>
                                                                                      </InvokeMethod.TargetObject>
                                                                                      <InArgument x:TypeArguments="x:String">[GLCode]</InArgument>
                                                                                    </InvokeMethod>
                                                                                    <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_3" MethodName="Add">
                                                                                      <InvokeMethod.TargetObject>
                                                                                        <InArgument x:TypeArguments="scg:List(x:String)">[linelist]</InArgument>
                                                                                      </InvokeMethod.TargetObject>
                                                                                      <InArgument x:TypeArguments="x:String">
                                                                                        <Literal x:TypeArguments="x:String" Value="" />
                                                                                      </InArgument>
                                                                                    </InvokeMethod>
                                                                                    <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_4" MethodName="Add">
                                                                                      <InvokeMethod.TargetObject>
                                                                                        <InArgument x:TypeArguments="scg:List(x:String)">[linelist]</InArgument>
                                                                                      </InvokeMethod.TargetObject>
                                                                                      <InArgument x:TypeArguments="x:String">
                                                                                        <Literal x:TypeArguments="x:String" Value="" />
                                                                                      </InArgument>
                                                                                    </InvokeMethod>
                                                                                    <InvokeMethod DisplayName="Amount InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_5" MethodName="Add">
                                                                                      <InvokeMethod.TargetObject>
                                                                                        <InArgument x:TypeArguments="scg:List(x:String)">[linelist]</InArgument>
                                                                                      </InvokeMethod.TargetObject>
                                                                                      <InArgument x:TypeArguments="x:String">[ocrValues(3)]</InArgument>
                                                                                    </InvokeMethod>
                                                                                    <InvokeMethod DisplayName="Assign to ocrValueLines InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_6" MethodName="Add">
                                                                                      <InvokeMethod.TargetObject>
                                                                                        <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[ocrLineValues]</InArgument>
                                                                                      </InvokeMethod.TargetObject>
                                                                                      <InArgument x:TypeArguments="scg:List(x:String)">[linelist]</InArgument>
                                                                                    </InvokeMethod>
                                                                                  </Sequence>
                                                                                </If.Then>
                                                                              </If>
                                                                            </TryCatch.Try>
                                                                            <TryCatch.Catches>
                                                                              <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_2">
                                                                                <ActivityAction x:TypeArguments="s:Exception">
                                                                                  <ActivityAction.Argument>
                                                                                    <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                  </ActivityAction.Argument>
                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_147">
                                                                                    <Assign.To>
                                                                                      <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                    </Assign.To>
                                                                                    <Assign.Value>
                                                                                      <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("GLCode"), String)]</InArgument>
                                                                                    </Assign.Value>
                                                                                  </Assign>
                                                                                </ActivityAction>
                                                                              </Catch>
                                                                            </TryCatch.Catches>
                                                                          </TryCatch>
                                                                        </Sequence>
                                                                      </If.Then>
                                                                    </If>
                                                                  </Sequence>
                                                                </If.Then>
                                                              </If>
                                                              <If Condition="[vendorResponseCode &lt;&gt; 200 OR GLCode = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_47">
                                                                <If.Then>
                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_62">
                                                                    <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;vendorID&quot;,vendorID},{&quot;Division&quot;,division},{&quot;businessRule&quot;,miscValues(&quot;businessRuleForDivisionGLCode&quot;).ToString}}]" ContinueOnError="True" DisplayName="DivisiontoGLCode Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_5" OutputArguments="[vendorResponseDictionary]" ResponseCode="[vendorResponseCode]" WorkflowFile="[projectPath+&quot;\DivisiontoGLCode.xaml&quot;]" />
                                                                    <If Condition="[vendorResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_46">
                                                                      <If.Then>
                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_61">
                                                                          <Sequence.Variables>
                                                                            <Variable x:TypeArguments="x:String" Name="comb" />
                                                                          </Sequence.Variables>
                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_148">
                                                                            <Assign.To>
                                                                              <OutArgument x:TypeArguments="x:String">[comb]</OutArgument>
                                                                            </Assign.To>
                                                                            <Assign.Value>
                                                                              <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("comb"), String)]</InArgument>
                                                                            </Assign.Value>
                                                                          </Assign>
                                                                          <If Condition="[comb &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_45">
                                                                            <If.Then>
                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_60">
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_149">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("AIT1"), String)]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_150">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[invoiceType]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">Expense Invoice</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_151">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="scg:List(scg:List(x:String))">[ocrLineValues]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[New List(Of List(Of String))]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_152">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="scg:List(x:String)">[linelist]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_3">
                                                                                  <TryCatch.Try>
                                                                                    <If Condition="[invoiceType.ToLower.contains(&quot;non po&quot;) OR invoiceType.ToLower.contains(&quot;expense&quot;)]" sap2010:WorkflowViewState.IdRef="If_44">
                                                                                      <If.Then>
                                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_59">
                                                                                          <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_7" MethodName="Add">
                                                                                            <InvokeMethod.TargetObject>
                                                                                              <InArgument x:TypeArguments="scg:List(x:String)">[linelist]</InArgument>
                                                                                            </InvokeMethod.TargetObject>
                                                                                            <InArgument x:TypeArguments="x:String">
                                                                                              <Literal x:TypeArguments="x:String" Value="" />
                                                                                            </InArgument>
                                                                                          </InvokeMethod>
                                                                                          <InvokeMethod DisplayName="Dimensions InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_8" MethodName="Add">
                                                                                            <InvokeMethod.TargetObject>
                                                                                              <InArgument x:TypeArguments="scg:List(x:String)">[linelist]</InArgument>
                                                                                            </InvokeMethod.TargetObject>
                                                                                            <InArgument x:TypeArguments="x:String">[comb]</InArgument>
                                                                                          </InvokeMethod>
                                                                                          <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_9" MethodName="Add">
                                                                                            <InvokeMethod.TargetObject>
                                                                                              <InArgument x:TypeArguments="scg:List(x:String)">[linelist]</InArgument>
                                                                                            </InvokeMethod.TargetObject>
                                                                                            <InArgument x:TypeArguments="x:String">
                                                                                              <Literal x:TypeArguments="x:String" Value="" />
                                                                                            </InArgument>
                                                                                          </InvokeMethod>
                                                                                          <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_10" MethodName="Add">
                                                                                            <InvokeMethod.TargetObject>
                                                                                              <InArgument x:TypeArguments="scg:List(x:String)">[linelist]</InArgument>
                                                                                            </InvokeMethod.TargetObject>
                                                                                            <InArgument x:TypeArguments="x:String">
                                                                                              <Literal x:TypeArguments="x:String" Value="" />
                                                                                            </InArgument>
                                                                                          </InvokeMethod>
                                                                                          <InvokeMethod DisplayName="Amount InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_11" MethodName="Add">
                                                                                            <InvokeMethod.TargetObject>
                                                                                              <InArgument x:TypeArguments="scg:List(x:String)">[linelist]</InArgument>
                                                                                            </InvokeMethod.TargetObject>
                                                                                            <InArgument x:TypeArguments="x:String">[ocrValues(3)]</InArgument>
                                                                                          </InvokeMethod>
                                                                                          <InvokeMethod DisplayName="Assign to ocrValueLines InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_12" MethodName="Add">
                                                                                            <InvokeMethod.TargetObject>
                                                                                              <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[ocrLineValues]</InArgument>
                                                                                            </InvokeMethod.TargetObject>
                                                                                            <InArgument x:TypeArguments="scg:List(x:String)">[linelist]</InArgument>
                                                                                          </InvokeMethod>
                                                                                        </Sequence>
                                                                                      </If.Then>
                                                                                    </If>
                                                                                  </TryCatch.Try>
                                                                                  <TryCatch.Catches>
                                                                                    <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_3">
                                                                                      <ActivityAction x:TypeArguments="s:Exception">
                                                                                        <ActivityAction.Argument>
                                                                                          <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                        </ActivityAction.Argument>
                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_153">
                                                                                          <Assign.To>
                                                                                            <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                          </Assign.To>
                                                                                          <Assign.Value>
                                                                                            <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("AIT1"), String)]</InArgument>
                                                                                          </Assign.Value>
                                                                                        </Assign>
                                                                                      </ActivityAction>
                                                                                    </Catch>
                                                                                  </TryCatch.Catches>
                                                                                </TryCatch>
                                                                              </Sequence>
                                                                            </If.Then>
                                                                          </If>
                                                                        </Sequence>
                                                                      </If.Then>
                                                                    </If>
                                                                  </Sequence>
                                                                </If.Then>
                                                              </If>
                                                            </Sequence>
                                                          </If.Then>
                                                        </If>
                                                      </If.Else>
                                                    </If>
                                                  </If.Then>
                                                  <If.Else>
                                                    <If Condition="[vendorId &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_56">
                                                      <If.Then>
                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_71">
                                                          <Sequence.Variables>
                                                            <Variable x:TypeArguments="scg:List(x:String)" Name="linelist" />
                                                            <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="vendorResponseDictionary" />
                                                            <Variable x:TypeArguments="x:Int32" Name="vendorResponseCode" />
                                                            <Variable x:TypeArguments="x:String" Name="invoiceType" />
                                                          </Sequence.Variables>
                                                          <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;vendorID&quot;,vendorID},{&quot;company&quot;,company},{&quot;division&quot;,division}}]" ContinueOnError="True" DisplayName="SupplierInfo Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_6" OutputArguments="[vendorResponseDictionary]" ResponseCode="[vendorResponseCode]" WorkflowFile="[projectPath+&quot;\SupplierInfo.xaml&quot;]" />
                                                          <If Condition="[vendorResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_51">
                                                            <If.Then>
                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_66">
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_154">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:String">[invoiceType]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("invoiceType"), String)]</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                                <If Condition="[invoiceType.ToLower.contains(&quot;non po&quot;) OR invoiceType.ToLower.contains(&quot;expense&quot;)]" sap2010:WorkflowViewState.IdRef="If_50">
                                                                  <If.Then>
                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_65">
                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_155">
                                                                        <Assign.To>
                                                                          <OutArgument x:TypeArguments="scg:List(scg:List(x:String))">[ocrLineValues]</OutArgument>
                                                                        </Assign.To>
                                                                        <Assign.Value>
                                                                          <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[New List(Of List(Of String))]</InArgument>
                                                                        </Assign.Value>
                                                                      </Assign>
                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_156">
                                                                        <Assign.To>
                                                                          <OutArgument x:TypeArguments="scg:List(x:String)">[linelist]</OutArgument>
                                                                        </Assign.To>
                                                                        <Assign.Value>
                                                                          <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
                                                                        </Assign.Value>
                                                                      </Assign>
                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_157">
                                                                        <Assign.To>
                                                                          <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                        </Assign.To>
                                                                        <Assign.Value>
                                                                          <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("GLCode"), String)]</InArgument>
                                                                        </Assign.Value>
                                                                      </Assign>
                                                                      <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_4">
                                                                        <TryCatch.Try>
                                                                          <If Condition="[invoiceType.ToLower.contains(&quot;non po&quot;) OR invoiceType.ToLower.contains(&quot;expense&quot;)]" sap2010:WorkflowViewState.IdRef="If_49">
                                                                            <If.Then>
                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_64">
                                                                                <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_13" MethodName="Add">
                                                                                  <InvokeMethod.TargetObject>
                                                                                    <InArgument x:TypeArguments="scg:List(x:String)">[linelist]</InArgument>
                                                                                  </InvokeMethod.TargetObject>
                                                                                  <InArgument x:TypeArguments="x:String">
                                                                                    <Literal x:TypeArguments="x:String" Value="" />
                                                                                  </InArgument>
                                                                                </InvokeMethod>
                                                                                <InvokeMethod DisplayName="Dimensions InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_14" MethodName="Add">
                                                                                  <InvokeMethod.TargetObject>
                                                                                    <InArgument x:TypeArguments="scg:List(x:String)">[linelist]</InArgument>
                                                                                  </InvokeMethod.TargetObject>
                                                                                  <InArgument x:TypeArguments="x:String">[GLCode]</InArgument>
                                                                                </InvokeMethod>
                                                                                <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_15" MethodName="Add">
                                                                                  <InvokeMethod.TargetObject>
                                                                                    <InArgument x:TypeArguments="scg:List(x:String)">[linelist]</InArgument>
                                                                                  </InvokeMethod.TargetObject>
                                                                                  <InArgument x:TypeArguments="x:String">
                                                                                    <Literal x:TypeArguments="x:String" Value="" />
                                                                                  </InArgument>
                                                                                </InvokeMethod>
                                                                                <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_16" MethodName="Add">
                                                                                  <InvokeMethod.TargetObject>
                                                                                    <InArgument x:TypeArguments="scg:List(x:String)">[linelist]</InArgument>
                                                                                  </InvokeMethod.TargetObject>
                                                                                  <InArgument x:TypeArguments="x:String">
                                                                                    <Literal x:TypeArguments="x:String" Value="" />
                                                                                  </InArgument>
                                                                                </InvokeMethod>
                                                                                <InvokeMethod DisplayName="Amount InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_17" MethodName="Add">
                                                                                  <InvokeMethod.TargetObject>
                                                                                    <InArgument x:TypeArguments="scg:List(x:String)">[linelist]</InArgument>
                                                                                  </InvokeMethod.TargetObject>
                                                                                  <InArgument x:TypeArguments="x:String">[ocrValues(3)]</InArgument>
                                                                                </InvokeMethod>
                                                                                <InvokeMethod DisplayName="Assign to ocrValueLines InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_18" MethodName="Add">
                                                                                  <InvokeMethod.TargetObject>
                                                                                    <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[ocrLineValues]</InArgument>
                                                                                  </InvokeMethod.TargetObject>
                                                                                  <InArgument x:TypeArguments="scg:List(x:String)">[linelist]</InArgument>
                                                                                </InvokeMethod>
                                                                              </Sequence>
                                                                            </If.Then>
                                                                          </If>
                                                                        </TryCatch.Try>
                                                                        <TryCatch.Catches>
                                                                          <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_4">
                                                                            <ActivityAction x:TypeArguments="s:Exception">
                                                                              <ActivityAction.Argument>
                                                                                <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                              </ActivityAction.Argument>
                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_158">
                                                                                <Assign.To>
                                                                                  <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                </Assign.To>
                                                                                <Assign.Value>
                                                                                  <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("GLCode"), String)]</InArgument>
                                                                                </Assign.Value>
                                                                              </Assign>
                                                                            </ActivityAction>
                                                                          </Catch>
                                                                        </TryCatch.Catches>
                                                                      </TryCatch>
                                                                    </Sequence>
                                                                  </If.Then>
                                                                </If>
                                                              </Sequence>
                                                            </If.Then>
                                                          </If>
                                                          <If Condition="[vendorResponseCode &lt;&gt; 200 OR NOT OcrLineValues.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_55">
                                                            <If.Then>
                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_70">
                                                                <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;vendorID&quot;,vendorID},{&quot;Division&quot;,division},{&quot;businessRule&quot;,miscValues(&quot;businessRuleForDivisionGLCode&quot;).ToString}}]" ContinueOnError="True" DisplayName="DivisiontoGLCode Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_7" OutputArguments="[vendorResponseDictionary]" ResponseCode="[vendorResponseCode]" WorkflowFile="[projectPath+&quot;\DivisiontoGLCode.xaml&quot;]" />
                                                                <If Condition="[vendorResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_54">
                                                                  <If.Then>
                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_69">
                                                                      <Sequence.Variables>
                                                                        <Variable x:TypeArguments="x:String" Name="comb" />
                                                                      </Sequence.Variables>
                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_159">
                                                                        <Assign.To>
                                                                          <OutArgument x:TypeArguments="x:String">[comb]</OutArgument>
                                                                        </Assign.To>
                                                                        <Assign.Value>
                                                                          <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("comb"), String)]</InArgument>
                                                                        </Assign.Value>
                                                                      </Assign>
                                                                      <If Condition="[comb &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_53">
                                                                        <If.Then>
                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_68">
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_160">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("AIT1"), String)]</InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_161">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="x:String">[invoiceType]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="x:String">Expense Invoice</InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_162">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="scg:List(scg:List(x:String))">[ocrLineValues]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[New List(Of List(Of String))]</InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_163">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="scg:List(x:String)">[linelist]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                            <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_5">
                                                                              <TryCatch.Try>
                                                                                <If Condition="[invoiceType.ToLower.contains(&quot;non po&quot;) OR invoiceType.ToLower.contains(&quot;expense&quot;)]" sap2010:WorkflowViewState.IdRef="If_52">
                                                                                  <If.Then>
                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_67">
                                                                                      <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_19" MethodName="Add">
                                                                                        <InvokeMethod.TargetObject>
                                                                                          <InArgument x:TypeArguments="scg:List(x:String)">[linelist]</InArgument>
                                                                                        </InvokeMethod.TargetObject>
                                                                                        <InArgument x:TypeArguments="x:String">
                                                                                          <Literal x:TypeArguments="x:String" Value="" />
                                                                                        </InArgument>
                                                                                      </InvokeMethod>
                                                                                      <InvokeMethod DisplayName="Dimensions InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_20" MethodName="Add">
                                                                                        <InvokeMethod.TargetObject>
                                                                                          <InArgument x:TypeArguments="scg:List(x:String)">[linelist]</InArgument>
                                                                                        </InvokeMethod.TargetObject>
                                                                                        <InArgument x:TypeArguments="x:String">[comb]</InArgument>
                                                                                      </InvokeMethod>
                                                                                      <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_21" MethodName="Add">
                                                                                        <InvokeMethod.TargetObject>
                                                                                          <InArgument x:TypeArguments="scg:List(x:String)">[linelist]</InArgument>
                                                                                        </InvokeMethod.TargetObject>
                                                                                        <InArgument x:TypeArguments="x:String">
                                                                                          <Literal x:TypeArguments="x:String" Value="" />
                                                                                        </InArgument>
                                                                                      </InvokeMethod>
                                                                                      <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_22" MethodName="Add">
                                                                                        <InvokeMethod.TargetObject>
                                                                                          <InArgument x:TypeArguments="scg:List(x:String)">[linelist]</InArgument>
                                                                                        </InvokeMethod.TargetObject>
                                                                                        <InArgument x:TypeArguments="x:String">
                                                                                          <Literal x:TypeArguments="x:String" Value="" />
                                                                                        </InArgument>
                                                                                      </InvokeMethod>
                                                                                      <InvokeMethod DisplayName="Amount InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_23" MethodName="Add">
                                                                                        <InvokeMethod.TargetObject>
                                                                                          <InArgument x:TypeArguments="scg:List(x:String)">[linelist]</InArgument>
                                                                                        </InvokeMethod.TargetObject>
                                                                                        <InArgument x:TypeArguments="x:String">[ocrValues(3)]</InArgument>
                                                                                      </InvokeMethod>
                                                                                      <InvokeMethod DisplayName="Assign to ocrValueLines InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_24" MethodName="Add">
                                                                                        <InvokeMethod.TargetObject>
                                                                                          <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[ocrLineValues]</InArgument>
                                                                                        </InvokeMethod.TargetObject>
                                                                                        <InArgument x:TypeArguments="scg:List(x:String)">[linelist]</InArgument>
                                                                                      </InvokeMethod>
                                                                                    </Sequence>
                                                                                  </If.Then>
                                                                                </If>
                                                                              </TryCatch.Try>
                                                                              <TryCatch.Catches>
                                                                                <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_5">
                                                                                  <ActivityAction x:TypeArguments="s:Exception">
                                                                                    <ActivityAction.Argument>
                                                                                      <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                    </ActivityAction.Argument>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_164">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("AIT1"), String)]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                  </ActivityAction>
                                                                                </Catch>
                                                                              </TryCatch.Catches>
                                                                            </TryCatch>
                                                                          </Sequence>
                                                                        </If.Then>
                                                                      </If>
                                                                    </Sequence>
                                                                  </If.Then>
                                                                </If>
                                                              </Sequence>
                                                            </If.Then>
                                                          </If>
                                                        </Sequence>
                                                      </If.Then>
                                                    </If>
                                                  </If.Else>
                                                </If>
                                                <If Condition="[ocrLineValues.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_35">
                                                  <If.Then>
                                                    <ForEach x:TypeArguments="scg:List(x:String)" DisplayName="ForEach&lt;List&lt;String&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_10" Values="[ocrLineValues]">
                                                      <ActivityAction x:TypeArguments="scg:List(x:String)">
                                                        <ActivityAction.Argument>
                                                          <DelegateInArgument x:TypeArguments="scg:List(x:String)" Name="item" />
                                                        </ActivityAction.Argument>
                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_52">
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_116">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:String">[AIT1]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:String">[item(1).Split("~"c)(0)]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_117">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:String">[AIT2]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:String">[item(1).Split("~"c)(1)]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_118">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:String">[AIT3]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:String">[item(1).Split("~"c)(2)]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_119">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:String">[AIT4]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:String">[item(1).Split("~"c)(3)]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_120">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:String">[AIT5]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:String">[item(1).Split("~"c)(4)]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_121">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:String">[AIT6]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:String">[item(1).Split("~"c)(5)]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_122">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:String">[AIT7]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:String">[item(1).Split("~"c)(6)]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_123">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:String">[amt]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:String">[item(4)]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_23" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                            <iai:IONAPIRequestWizard.Headers>
                                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                  <x:String>Accept</x:String>
                                                                </scg:List>
                                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                  <x:String>application/json</x:String>
                                                                </scg:List>
                                                              </scg:List>
                                                            </iai:IONAPIRequestWizard.Headers>
                                                            <iai:IONAPIRequestWizard.QueryParameters>
                                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                  <x:String>INBN</x:String>
                                                                  <x:String>RDTP</x:String>
                                                                  <x:String>DIVI</x:String>
                                                                  <x:String>NLAM</x:String>
                                                                  <x:String>AIT1</x:String>
                                                                  <x:String>AIT2</x:String>
                                                                  <x:String>AIT3</x:String>
                                                                  <x:String>AIT4</x:String>
                                                                  <x:String>AIT5</x:String>
                                                                  <x:String>AIT6</x:String>
                                                                  <x:String>AIT7</x:String>
                                                                </scg:List>
                                                                <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                  <x:String>inbnValue</x:String>
                                                                  <x:String>8</x:String>
                                                                  <x:String>division</x:String>
                                                                  <x:String>amt</x:String>
                                                                  <x:String>AIT1</x:String>
                                                                  <x:String>AIT2</x:String>
                                                                  <x:String>AIT3</x:String>
                                                                  <x:String>AIT4</x:String>
                                                                  <x:String>AIT5</x:String>
                                                                  <x:String>AIT6</x:String>
                                                                  <x:String>AIT7</x:String>
                                                                </scg:List>
                                                              </scg:List>
                                                            </iai:IONAPIRequestWizard.QueryParameters>
                                                          </iai:IONAPIRequestWizard>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_124">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_36">
                                                            <If.Then>
                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_50">
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_125">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:String" xml:space="preserve">["Error occured while adding the line.  "  +  (out1("results")(0)("errorMessage")).ToString]</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_126">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_11" Line="[commentStatus]" Source="[logfile]" />
                                                              </Sequence>
                                                            </If.Then>
                                                            <If.Else>
                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_51">
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_127">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:String">["Invoice Line created"]</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_128">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:String">["SUCCESS"]</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_12" Line="[commentStatus]" Source="[logfile]" />
                                                              </Sequence>
                                                            </If.Else>
                                                          </If>
                                                        </Sequence>
                                                      </ActivityAction>
                                                    </ForEach>
                                                  </If.Then>
                                                  <If.Else>
                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_53">
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_129">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[AIT1]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">[GLCode]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_130">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[AIT2]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">
                                                            <Literal x:TypeArguments="x:String" Value="" />
                                                          </InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_131">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[AIT3]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">
                                                            <Literal x:TypeArguments="x:String" Value="" />
                                                          </InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_132">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[AIT4]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">
                                                            <Literal x:TypeArguments="x:String" Value="" />
                                                          </InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_133">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[AIT5]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">
                                                            <Literal x:TypeArguments="x:String" Value="" />
                                                          </InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_134">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[AIT6]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">
                                                            <Literal x:TypeArguments="x:String" Value="" />
                                                          </InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_135">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[AIT7]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">
                                                            <Literal x:TypeArguments="x:String" Value="" />
                                                          </InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_136">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[amt]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">[ocrValues(3)]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_24" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                        <iai:IONAPIRequestWizard.Headers>
                                                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                            <scg:List x:TypeArguments="x:String" Capacity="4">
                                                              <x:String>Accept</x:String>
                                                            </scg:List>
                                                            <scg:List x:TypeArguments="x:String" Capacity="4">
                                                              <x:String>application/json</x:String>
                                                            </scg:List>
                                                          </scg:List>
                                                        </iai:IONAPIRequestWizard.Headers>
                                                        <iai:IONAPIRequestWizard.QueryParameters>
                                                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                            <scg:List x:TypeArguments="x:String" Capacity="16">
                                                              <x:String>INBN</x:String>
                                                              <x:String>RDTP</x:String>
                                                              <x:String>DIVI</x:String>
                                                              <x:String>NLAM</x:String>
                                                              <x:String>AIT1</x:String>
                                                              <x:String>AIT2</x:String>
                                                              <x:String>AIT3</x:String>
                                                              <x:String>AIT4</x:String>
                                                              <x:String>AIT5</x:String>
                                                              <x:String>AIT6</x:String>
                                                              <x:String>AIT7</x:String>
                                                            </scg:List>
                                                            <scg:List x:TypeArguments="x:String" Capacity="16">
                                                              <x:String>inbnValue</x:String>
                                                              <x:String>8</x:String>
                                                              <x:String>division</x:String>
                                                              <x:String>amt</x:String>
                                                              <x:String>AIT1</x:String>
                                                              <x:String>AIT2</x:String>
                                                              <x:String>AIT3</x:String>
                                                              <x:String>AIT4</x:String>
                                                              <x:String>AIT5</x:String>
                                                              <x:String>AIT6</x:String>
                                                              <x:String>AIT7</x:String>
                                                            </scg:List>
                                                          </scg:List>
                                                        </iai:IONAPIRequestWizard.QueryParameters>
                                                      </iai:IONAPIRequestWizard>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_137">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_37">
                                                        <If.Then>
                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_54">
                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_138">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="x:String" xml:space="preserve">["Error occured while adding the line.  "  +  (out1("results")(0)("errorMessage")).ToString]</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_139">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_13" Line="[commentStatus]" Source="[logfile]" />
                                                          </Sequence>
                                                        </If.Then>
                                                        <If.Else>
                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_55">
                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_140">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="x:String">["Invoice Line created"]</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_141">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="x:String">["SUCCESS"]</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_14" Line="[commentStatus]" Source="[logfile]" />
                                                          </Sequence>
                                                        </If.Else>
                                                      </If>
                                                    </Sequence>
                                                  </If.Else>
                                                </If>
                                              </Sequence>
                                            </If.Else>
                                          </If>
                                        </Sequence>
                                      </If.Else>
                                    </If>
                                    <If Condition="[CInt(ocrValues(5)) &lt;&gt; 0]" sap2010:WorkflowViewState.IdRef="If_28">
                                      <If.Then>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_41">
                                          <Sequence.Variables>
                                            <Variable x:TypeArguments="x:String" Name="vat" />
                                            <Variable x:TypeArguments="iru:ResponseObject" Name="respout1" />
                                            <Variable x:TypeArguments="x:String" Name="vatCode" />
                                          </Sequence.Variables>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_95">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[vat]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[ocrValues(5)]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_142">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[vatCode]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[miscValues("vatCodeConfig").ToString]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <If Condition="[miscValues(&quot;vatCodeConfig&quot;).ToString = &quot;NA&quot;]" sap2010:WorkflowViewState.IdRef="If_38">
                                            <If.Then>
                                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_20" Response="[respout1]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                <iai:IONAPIRequestWizard.Headers>
                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>Accept</x:String>
                                                    </scg:List>
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>application/json</x:String>
                                                    </scg:List>
                                                  </scg:List>
                                                </iai:IONAPIRequestWizard.Headers>
                                                <iai:IONAPIRequestWizard.QueryParameters>
                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>INBN</x:String>
                                                      <x:String>RDTP</x:String>
                                                      <x:String>DIVI</x:String>
                                                      <x:String>GLAM</x:String>
                                                    </scg:List>
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>inbnValue</x:String>
                                                      <x:String>3</x:String>
                                                      <x:String>division</x:String>
                                                      <x:String>vat</x:String>
                                                    </scg:List>
                                                  </scg:List>
                                                </iai:IONAPIRequestWizard.QueryParameters>
                                              </iai:IONAPIRequestWizard>
                                            </If.Then>
                                            <If.Else>
                                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_25" Response="[respout1]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                <iai:IONAPIRequestWizard.Headers>
                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>Accept</x:String>
                                                    </scg:List>
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>application/json</x:String>
                                                    </scg:List>
                                                  </scg:List>
                                                </iai:IONAPIRequestWizard.Headers>
                                                <iai:IONAPIRequestWizard.QueryParameters>
                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                    <scg:List x:TypeArguments="x:String" Capacity="8">
                                                      <x:String>INBN</x:String>
                                                      <x:String>RDTP</x:String>
                                                      <x:String>DIVI</x:String>
                                                      <x:String>VTA1</x:String>
                                                      <x:String>VTCD</x:String>
                                                    </scg:List>
                                                    <scg:List x:TypeArguments="x:String" Capacity="8">
                                                      <x:String>inbnValue</x:String>
                                                      <x:String>3</x:String>
                                                      <x:String>division</x:String>
                                                      <x:String>vat</x:String>
                                                      <x:String>vatCode</x:String>
                                                    </scg:List>
                                                  </scg:List>
                                                </iai:IONAPIRequestWizard.QueryParameters>
                                              </iai:IONAPIRequestWizard>
                                            </If.Else>
                                          </If>
                                        </Sequence>
                                      </If.Then>
                                    </If>
                                  </Sequence>
                                </If.Then>
                              </If>
                              <If Condition="[inbnValue = &quot;&quot;]" DisplayName="If validation" sap2010:WorkflowViewState.IdRef="If_15">
                                <If.Then>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_52">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </If.Then>
                                <If.Else>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_25">
                                    <Sequence.Variables>
                                      <Variable x:TypeArguments="x:String" Name="SuccessMoved" />
                                      <Variable x:TypeArguments="x:Int32" Name="respout1" />
                                    </Sequence.Variables>
                                    <If Condition="[approvalRequired]" sap2010:WorkflowViewState.IdRef="If_18">
                                      <If.Then>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_28">
                                          <Sequence.Variables>
                                            <Variable x:TypeArguments="x:Boolean" Name="approvalRequest" />
                                            <Variable x:TypeArguments="x:Int32" Name="sendApprovalResponseCode" />
                                            <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="checkApprovalResponseDictionary" />
                                            <Variable x:TypeArguments="x:Int32" Name="checkApprovalResponseCode" />
                                            <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="sendApprovalResponseDictionary" />
                                          </Sequence.Variables>
                                          <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;amount&quot;,ocrValues(2)},{&quot;tenantID&quot;,tenantID},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule}}]" ContinueOnError="True" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_1" OutputArguments="[checkApprovalResponseDictionary]" ResponseCode="[checkApprovalResponseCode]" WorkflowFile="[projectPath+&quot;\CheckApproval.xaml&quot;]" />
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_57">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:Boolean">[approvalRequest]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:Boolean">[CType(checkApprovalResponseDictionary("approvalRequest"), Boolean)]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <If Condition="[approvalRequest]" sap2010:WorkflowViewState.IdRef="If_17">
                                            <If.Then>
                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_27">
                                                <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;amount&quot;,ocrValues(2)},{&quot;tenantID&quot;,tenantID},{&quot;division&quot;,DIVISION},{&quot;inbn&quot;,inbnValue},{&quot;invoiceNumber&quot;,ocrValues(0)},{&quot;poNumber&quot;,ocrValues(4)},{&quot;vendorName&quot;,vendorId},{&quot;approvalWorkflow&quot;,approvalWorkflow}}]" ContinueOnError="True" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_2" OutputArguments="[sendApprovalResponseDictionary]" ResponseCode="[sendApprovalResponseCode]" WorkflowFile="[projectPath+&quot;\SendtoApprovalWorkflow.xaml&quot;]" />
                                                <If Condition="[sendApprovalResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_16">
                                                  <If.Then>
                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_26">
                                                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_4" Line="Supllier invoice is sent for approval since the invoice amount is large" Source="[logfile]" />
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_58">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">["Invoice created and Sent for approval"]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                    </Sequence>
                                                  </If.Then>
                                                </If>
                                              </Sequence>
                                            </If.Then>
                                            <If.Else>
                                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" Response="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_9" StatusCode="[respout1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS455MI/ValidByBatchNo&quot;]">
                                                <iai:IONAPIRequestWizard.Headers>
                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>Accept</x:String>
                                                    </scg:List>
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>application/json</x:String>
                                                    </scg:List>
                                                  </scg:List>
                                                </iai:IONAPIRequestWizard.Headers>
                                                <iai:IONAPIRequestWizard.QueryParameters>
                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>INBN</x:String>
                                                      <x:String>DIVI</x:String>
                                                    </scg:List>
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>inbnValue</x:String>
                                                      <x:String>division</x:String>
                                                    </scg:List>
                                                  </scg:List>
                                                </iai:IONAPIRequestWizard.QueryParameters>
                                              </iai:IONAPIRequestWizard>
                                            </If.Else>
                                          </If>
                                        </Sequence>
                                      </If.Then>
                                      <If.Else>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_42">
                                          <If Condition="[miscValues(&quot;AutomateValidation&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_34">
                                            <If.Then>
                                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_10" Response="[respObj2]" StatusCode="[respout1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS455MI/ValidByBatchNo&quot;]">
                                                <iai:IONAPIRequestWizard.Headers>
                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>Accept</x:String>
                                                    </scg:List>
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>application/json</x:String>
                                                    </scg:List>
                                                  </scg:List>
                                                </iai:IONAPIRequestWizard.Headers>
                                                <iai:IONAPIRequestWizard.QueryParameters>
                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>INBN</x:String>
                                                      <x:String>DIVI</x:String>
                                                    </scg:List>
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>inbnValue</x:String>
                                                      <x:String>division</x:String>
                                                    </scg:List>
                                                  </scg:List>
                                                </iai:IONAPIRequestWizard.QueryParameters>
                                              </iai:IONAPIRequestWizard>
                                            </If.Then>
                                          </If>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_96">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">SUCCESS</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                        </Sequence>
                                      </If.Else>
                                    </If>
                                    <If Condition="[respOut1 = 200]" sap2010:WorkflowViewState.IdRef="If_19">
                                      <If.Then>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_59">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">["Invoice created and validated"]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                      </If.Then>
                                    </If>
                                  </Sequence>
                                </If.Else>
                              </If>
                            </Sequence>
                          </If.Then>
                          <If.Else>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_11">
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_19">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">["Received an IONAPI error while adding Invoice header."]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_20">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_2" Line="[commentStatus]" Source="[logfile]" />
                            </Sequence>
                          </If.Else>
                        </If>
                      </Sequence>
                    </Sequence>
                  </If.Then>
                  <If.Else>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_23">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_49">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[(out0("results")(0)("errorMessage")).ToString]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_50">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_3" Line="[commentStatus]" Source="[logfile]" />
                    </Sequence>
                  </If.Else>
                </If>
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_3">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_2">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching Vendor name for the Vendor Id " + vendorId+"."]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_3">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="[commentStatus]" Source="[logfile]" />
              </Sequence>
            </If.Else>
          </If>
        </Sequence>
      </If.Then>
      <If.Else>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_29">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_67">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["Invoice Number " + ocrValues(0) + " already exists."]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_68">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
            </Assign.Value>
          </Assign>
        </Sequence>
      </If.Else>
    </If>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_166" sap:VirtualizedContainerService.HintSize="4004,60" />
      <sap2010:ViewStateData Id="Assign_38" sap:VirtualizedContainerService.HintSize="4004,60" />
      <sap2010:ViewStateData Id="Assign_112" sap:VirtualizedContainerService.HintSize="680,62" />
      <sap2010:ViewStateData Id="Assign_113" sap:VirtualizedContainerService.HintSize="680,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_22" sap:VirtualizedContainerService.HintSize="680,22" />
      <sap2010:ViewStateData Id="Assign_114" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_47" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_115" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_48" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_32" sap:VirtualizedContainerService.HintSize="554,340" />
      <sap2010:ViewStateData Id="If_33" sap:VirtualizedContainerService.HintSize="680,494" />
      <sap2010:ViewStateData Id="Sequence_49" sap:VirtualizedContainerService.HintSize="4004,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_45" sap:VirtualizedContainerService.HintSize="4004,60" />
      <sap2010:ViewStateData Id="Assign_65" sap:VirtualizedContainerService.HintSize="4004,60" />
      <sap2010:ViewStateData Id="Assign_165" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_57" sap:VirtualizedContainerService.HintSize="4004,208" />
      <sap2010:ViewStateData Id="InvokeWorkflow_3" sap:VirtualizedContainerService.HintSize="4004,22" />
      <sap2010:ViewStateData Id="Assign_66" sap:VirtualizedContainerService.HintSize="4004,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_2" sap:VirtualizedContainerService.HintSize="3693,22" />
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="3382,60" />
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="975,60" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="975,60" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="975,60" />
      <sap2010:ViewStateData Id="Assign_7" sap:VirtualizedContainerService.HintSize="975,60" />
      <sap2010:ViewStateData Id="Assign_51" sap:VirtualizedContainerService.HintSize="975,60" />
      <sap2010:ViewStateData Id="Assign_37" sap:VirtualizedContainerService.HintSize="975,60" />
      <sap2010:ViewStateData Id="Assign_170" sap:VirtualizedContainerService.HintSize="975,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_8" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_168" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_59" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Assign_169" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Sequence_72" sap:VirtualizedContainerService.HintSize="486,494">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_11" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_73" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_60" sap:VirtualizedContainerService.HintSize="464,332" />
      <sap2010:ViewStateData Id="If_61" sap:VirtualizedContainerService.HintSize="975,642" />
      <sap2010:ViewStateData Id="Sequence_4" sap:VirtualizedContainerService.HintSize="3071,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_43" sap:VirtualizedContainerService.HintSize="3049,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_6" sap:VirtualizedContainerService.HintSize="3049,22" />
      <sap2010:ViewStateData Id="Assign_39" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_40" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_19" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_41" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_20" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_10" sap:VirtualizedContainerService.HintSize="553,332" />
      <sap2010:ViewStateData Id="Sequence_21" sap:VirtualizedContainerService.HintSize="575,556">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_11" sap:VirtualizedContainerService.HintSize="3049,704" />
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="3049,60" />
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="3049,60" />
      <sap2010:ViewStateData Id="Assign_64" sap:VirtualizedContainerService.HintSize="3049,60" />
      <sap2010:ViewStateData Id="Assign_70" sap:VirtualizedContainerService.HintSize="3049,60" />
      <sap2010:ViewStateData Id="Assign_110" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_111" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Catch`1_1" sap:VirtualizedContainerService.HintSize="404,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_1" sap:VirtualizedContainerService.HintSize="3049,287" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_15" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_16" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_35" sap:VirtualizedContainerService.HintSize="222,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_24" sap:VirtualizedContainerService.HintSize="464,294" />
      <sap2010:ViewStateData Id="Sequence_32" sap:VirtualizedContainerService.HintSize="486,480">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_17" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_18" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_36" sap:VirtualizedContainerService.HintSize="222,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_25" sap:VirtualizedContainerService.HintSize="464,294" />
      <sap2010:ViewStateData Id="Sequence_34" sap:VirtualizedContainerService.HintSize="486,480">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_23" sap:VirtualizedContainerService.HintSize="997,628" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_26" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_27" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_74" sap:VirtualizedContainerService.HintSize="222,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_63" sap:VirtualizedContainerService.HintSize="464,294" />
      <sap2010:ViewStateData Id="Sequence_75" sap:VirtualizedContainerService.HintSize="486,480">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_28" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_29" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_76" sap:VirtualizedContainerService.HintSize="222,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_64" sap:VirtualizedContainerService.HintSize="464,294" />
      <sap2010:ViewStateData Id="Sequence_77" sap:VirtualizedContainerService.HintSize="486,480">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_65" sap:VirtualizedContainerService.HintSize="997,628" />
      <sap2010:ViewStateData Id="If_62" sap:VirtualizedContainerService.HintSize="3049,776" />
      <sap2010:ViewStateData Id="Assign_12" sap:VirtualizedContainerService.HintSize="2738,60" />
      <sap2010:ViewStateData Id="Assign_13" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_14" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_15" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_8" sap:VirtualizedContainerService.HintSize="264,390">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_16" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_17" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_18" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_6" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_9" sap:VirtualizedContainerService.HintSize="264,452">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="2738,600" />
      <sap2010:ViewStateData Id="Assign_88" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_90" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_91" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_92" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_89" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_93" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_94" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_28" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_4" sap:VirtualizedContainerService.HintSize="554,22" />
      <sap2010:ViewStateData Id="Assign_72" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_74" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_75" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_5" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_37" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_78" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_79" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_7" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_38" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_26" sap:VirtualizedContainerService.HintSize="554,504" />
      <sap2010:ViewStateData Id="Sequence_40" sap:VirtualizedContainerService.HintSize="576,1608">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_7" sap:VirtualizedContainerService.HintSize="606,1756" />
      <sap2010:ViewStateData Id="Append_Line_8" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_4" sap:VirtualizedContainerService.HintSize="778.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_143" sap:VirtualizedContainerService.HintSize="630.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_144" sap:VirtualizedContainerService.HintSize="483,60" />
      <sap2010:ViewStateData Id="Assign_145" sap:VirtualizedContainerService.HintSize="483,60" />
      <sap2010:ViewStateData Id="Assign_146" sap:VirtualizedContainerService.HintSize="483,60" />
      <sap2010:ViewStateData Id="InvokeMethod_1" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="InvokeMethod_2" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="InvokeMethod_3" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="InvokeMethod_4" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="InvokeMethod_5" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="InvokeMethod_6" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="Sequence_56" sap:VirtualizedContainerService.HintSize="240,1104">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_41" sap:VirtualizedContainerService.HintSize="464,1252">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_147" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_2" sap:VirtualizedContainerService.HintSize="468.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_2" sap:VirtualizedContainerService.HintSize="483,1480" />
      <sap2010:ViewStateData Id="Sequence_57" sap:VirtualizedContainerService.HintSize="505,1904">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_42" sap:VirtualizedContainerService.HintSize="631,2052">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_58" sap:VirtualizedContainerService.HintSize="653,2278">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_43" sap:VirtualizedContainerService.HintSize="779,2426" />
      <sap2010:ViewStateData Id="InvokeWorkflow_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Assign_148" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_149" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_150" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_151" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_152" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_7" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="InvokeMethod_8" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="InvokeMethod_9" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="InvokeMethod_10" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="InvokeMethod_11" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="InvokeMethod_12" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="Sequence_59" sap:VirtualizedContainerService.HintSize="239.333333333333,1128">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_44" sap:VirtualizedContainerService.HintSize="464,1282">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_153" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_3" sap:VirtualizedContainerService.HintSize="468.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_3" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_60" sap:VirtualizedContainerService.HintSize="264,584.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_45" sap:VirtualizedContainerService.HintSize="464,738.666666666667" />
      <sap2010:ViewStateData Id="Sequence_61" sap:VirtualizedContainerService.HintSize="486,964.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_46" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_62" sap:VirtualizedContainerService.HintSize="222,238.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_47" sap:VirtualizedContainerService.HintSize="778.666666666667,392.666666666667" />
      <sap2010:ViewStateData Id="Sequence_63" sap:VirtualizedContainerService.HintSize="801,3045">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_48" sap:VirtualizedContainerService.HintSize="926,3193" />
      <sap2010:ViewStateData Id="If_40" sap:VirtualizedContainerService.HintSize="1051,3341" />
      <sap2010:ViewStateData Id="InvokeWorkflow_6" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_154" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_155" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_156" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_157" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="InvokeMethod_13" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="InvokeMethod_14" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="InvokeMethod_15" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="InvokeMethod_16" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="InvokeMethod_17" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="InvokeMethod_18" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="Sequence_64" sap:VirtualizedContainerService.HintSize="239.333333333333,1128">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_49" sap:VirtualizedContainerService.HintSize="464,1282">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_158" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_4" sap:VirtualizedContainerService.HintSize="468.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_4" sap:VirtualizedContainerService.HintSize="482.666666666667,1520" />
      <sap2010:ViewStateData Id="Sequence_65" sap:VirtualizedContainerService.HintSize="504.666666666667,1950">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_50" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_66" sap:VirtualizedContainerService.HintSize="264,275">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_51" sap:VirtualizedContainerService.HintSize="464,423" />
      <sap2010:ViewStateData Id="InvokeWorkflow_7" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Assign_159" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_160" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_161" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_162" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_163" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_19" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="InvokeMethod_20" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="InvokeMethod_21" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="InvokeMethod_22" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="InvokeMethod_23" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="InvokeMethod_24" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="Sequence_67" sap:VirtualizedContainerService.HintSize="239.333333333333,1128">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_52" sap:VirtualizedContainerService.HintSize="464,1282">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_164" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_5" sap:VirtualizedContainerService.HintSize="468.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_5" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_68" sap:VirtualizedContainerService.HintSize="264,584.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_53" sap:VirtualizedContainerService.HintSize="464,738.666666666667" />
      <sap2010:ViewStateData Id="Sequence_69" sap:VirtualizedContainerService.HintSize="486,964.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_54" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_70" sap:VirtualizedContainerService.HintSize="222,239">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_55" sap:VirtualizedContainerService.HintSize="464,387" />
      <sap2010:ViewStateData Id="Sequence_71" sap:VirtualizedContainerService.HintSize="486,1036">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_56" sap:VirtualizedContainerService.HintSize="611,1184" />
      <sap2010:ViewStateData Id="If_39" sap:VirtualizedContainerService.HintSize="1687,3489">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_116" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_117" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_118" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_119" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_120" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_121" sap:VirtualizedContainerService.HintSize="554,60" />
      <sap2010:ViewStateData Id="Assign_122" sap:VirtualizedContainerService.HintSize="554,60" />
      <sap2010:ViewStateData Id="Assign_123" sap:VirtualizedContainerService.HintSize="554,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_23" sap:VirtualizedContainerService.HintSize="554,22" />
      <sap2010:ViewStateData Id="Assign_124" sap:VirtualizedContainerService.HintSize="554,60" />
      <sap2010:ViewStateData Id="Assign_125" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_126" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_11" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_50" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_127" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_128" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_12" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_51" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_36" sap:VirtualizedContainerService.HintSize="554,494" />
      <sap2010:ViewStateData Id="Sequence_52" sap:VirtualizedContainerService.HintSize="576,1590">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_10" sap:VirtualizedContainerService.HintSize="606,1738" />
      <sap2010:ViewStateData Id="Assign_129" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_130" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_131" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_132" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_133" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_134" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_135" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_136" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_24" sap:VirtualizedContainerService.HintSize="554,22" />
      <sap2010:ViewStateData Id="Assign_137" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_138" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_139" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_13" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_54" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_140" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_141" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_14" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_55" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_37" sap:VirtualizedContainerService.HintSize="554,504" />
      <sap2010:ViewStateData Id="Sequence_53" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_35" sap:VirtualizedContainerService.HintSize="1687,1886">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_44" sap:VirtualizedContainerService.HintSize="1709,5539">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_30" sap:VirtualizedContainerService.HintSize="1934,5687" />
      <sap2010:ViewStateData Id="Sequence_43" sap:VirtualizedContainerService.HintSize="1956,5811">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_29" sap:VirtualizedContainerService.HintSize="2587,5959" />
      <sap2010:ViewStateData Id="Assign_95" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_142" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_20" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_25" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_38" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Sequence_41" sap:VirtualizedContainerService.HintSize="486,532">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_28" sap:VirtualizedContainerService.HintSize="2587,680" />
      <sap2010:ViewStateData Id="Sequence_12" sap:VirtualizedContainerService.HintSize="2609,6803">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_6" sap:VirtualizedContainerService.HintSize="2738,6951" />
      <sap2010:ViewStateData Id="Assign_52" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_1" sap:VirtualizedContainerService.HintSize="711,22" />
      <sap2010:ViewStateData Id="Assign_57" sap:VirtualizedContainerService.HintSize="711,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_2" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Append_Line_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_58" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_26" sap:VirtualizedContainerService.HintSize="264,246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_16" sap:VirtualizedContainerService.HintSize="464,394" />
      <sap2010:ViewStateData Id="Sequence_27" sap:VirtualizedContainerService.HintSize="486,580">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_9" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_17" sap:VirtualizedContainerService.HintSize="711,728" />
      <sap2010:ViewStateData Id="Sequence_28" sap:VirtualizedContainerService.HintSize="733,1014">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_10" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_34" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Assign_96" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Sequence_42" sap:VirtualizedContainerService.HintSize="486,432">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_18" sap:VirtualizedContainerService.HintSize="1244,1162">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_59" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_19" sap:VirtualizedContainerService.HintSize="1244,208" />
      <sap2010:ViewStateData Id="Sequence_25" sap:VirtualizedContainerService.HintSize="1266,1534">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_15" sap:VirtualizedContainerService.HintSize="2738,1682" />
      <sap2010:ViewStateData Id="Sequence_10" sap:VirtualizedContainerService.HintSize="2760,9537">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_19" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_20" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_11" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_5" sap:VirtualizedContainerService.HintSize="3049,9685" />
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="3071,12258">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_6" sap:VirtualizedContainerService.HintSize="3093,12473">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_49" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_50" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_23" sap:VirtualizedContainerService.HintSize="264,348">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="3382,12621" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="3404,12845">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="3693,12993">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_30" sap:VirtualizedContainerService.HintSize="3715,13179">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_67" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_68" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_29" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_20" sap:VirtualizedContainerService.HintSize="4004,13327" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="4026,14352">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="4066,14432" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>