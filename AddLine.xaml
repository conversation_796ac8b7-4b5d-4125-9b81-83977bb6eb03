﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="logfile" Type="InArgument(x:String)" />
    <x:Property Name="commentStatus" Type="OutArgument(x:String)" />
    <x:Property Name="status" Type="OutArgument(x:String)" />
    <x:Property Name="vatCodeConfig" Type="InArgument(x:String)" />
    <x:Property Name="M3TotalTableRows" Type="InArgument(scg:List(s:String[]))" />
    <x:Property Name="inbnValue" Type="InArgument(x:String)" />
    <x:Property Name="division" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>System.Windows.Controls</x:String>
      <x:String>System.Windows.Documents</x:String>
      <x:String>System.Windows.Shapes</x:String>
      <x:String>System.Windows.Shell</x:String>
      <x:String>System.Windows.Navigation</x:String>
      <x:String>System.Windows.Data</x:String>
      <x:String>System.Windows</x:String>
      <x:String>System.Windows.Controls.Primitives</x:String>
      <x:String>System.Windows.Media.Animation</x:String>
      <x:String>System.Windows.Input</x:String>
      <x:String>System.Windows.Media</x:String>
      <x:String>System.Diagnostics</x:String>
      <x:String>System.Windows.Automation</x:String>
      <x:String>System.Windows.Media.TextFormatting</x:String>
      <x:String>System.Windows.Ink</x:String>
      <x:String>System.Windows.Media.Effects</x:String>
      <x:String>System.Windows.Media.Imaging</x:String>
      <x:String>System.Windows.Media.Media3D</x:String>
      <x:String>System.Windows.Controls.Ribbon</x:String>
      <x:String>System.Windows.Forms.Integration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>System</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Windows.Controls.Ribbon</AssemblyReference>
      <AssemblyReference>WindowsFormsIntegration</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_1">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:Int32" Name="StatusCode7" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj7" />
      <Variable x:TypeArguments="njl:JToken" Name="out7" />
      <Variable x:TypeArguments="x:String" Name="itno" />
      <Variable x:TypeArguments="x:Decimal" Name="totAmt" />
      <Variable x:TypeArguments="x:Decimal" Name="totQty" />
      <Variable x:TypeArguments="x:String" Name="diffAmt" />
      <Variable x:TypeArguments="x:String" Name="pnls" />
    </Sequence.Variables>
    <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_3" Values="[M3TotalTableRows]">
      <ActivityAction x:TypeArguments="s:String[]">
        <ActivityAction.Argument>
          <DelegateInArgument x:TypeArguments="s:String[]" Name="rows" />
        </ActivityAction.Argument>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_7">
          <Sequence.Variables>
            <Variable x:TypeArguments="x:String" Name="inbn" />
            <Variable x:TypeArguments="x:String" Name="puno" />
            <Variable x:TypeArguments="x:String" Name="pnli" />
            <Variable x:TypeArguments="x:String" Name="repn" />
            <Variable x:TypeArguments="x:String" Name="ivqa" />
            <Variable x:TypeArguments="x:String" Name="grpr" />
          </Sequence.Variables>
          <Assign DisplayName="Assign inbn" sap2010:WorkflowViewState.IdRef="Assign_89">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[inbn]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[inbnValue]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_90">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[puno]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[rows(6)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_91">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[pnli]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[rows(0)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="Assign itno" sap2010:WorkflowViewState.IdRef="Assign_92">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[itno]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[rows(5)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_93">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[repn]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[rows(1)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_94">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[ivqa]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[rows(10)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_97">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[pnls]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[rows(11)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_95">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[grpr]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[Math.Round(convert.ToDecimal(rows(2)),4).ToString]</InArgument>
            </Assign.Value>
          </Assign>
          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_5" Response="[respObj7]" StatusCode="[StatusCode7]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/PPS200MI/GetLine?dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
            <iai:IONAPIRequestWizard.Headers>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>Accept</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>application/json</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.Headers>
            <iai:IONAPIRequestWizard.QueryParameters>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>PNLI</x:String>
                  <x:String>PUNO</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>pnli</x:String>
                  <x:String>puno</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.QueryParameters>
          </iai:IONAPIRequestWizard>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_1">
            <Assign.To>
              <OutArgument x:TypeArguments="njl:JToken">[out7]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj7.ReadAsText)]</InArgument>
            </Assign.Value>
          </Assign>
          <If Condition="[out7(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_5">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_2">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_2">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["Purchase price UOM and purchase order UOM are not available."]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_3">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="[commentStatus]" Source="[logfile]" />
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_5">
                <Sequence.Variables>
                  <Variable x:TypeArguments="njl:JToken" Name="out8" />
                  <Variable x:TypeArguments="x:Int32" Name="StatusCode8" />
                  <Variable x:TypeArguments="iru:ResponseObject" Name="respObj8" />
                  <Variable x:TypeArguments="x:Int32" Name="m" />
                  <Variable x:TypeArguments="x:String" Name="ppun" />
                  <Variable x:TypeArguments="x:String" Name="puun" />
                </Sequence.Variables>
                <Assign DisplayName="Assign ppun" sap2010:WorkflowViewState.IdRef="Assign_4">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[ppun]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out7("results")(0)("records")(0)("PPUN").ToString]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="Assign puun" sap2010:WorkflowViewState.IdRef="Assign_5">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[puun]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out7("results")(0)("records")(0)("PUUN").ToString]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_6">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[puno]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[rows(6)]</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[m=0 AND vatCodeConfig = &quot;LINE&quot;]" sap2010:WorkflowViewState.IdRef="If_3">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_3">
                      <Sequence.Variables>
                        <Variable x:TypeArguments="iru:ResponseObject" Name="vatResp" />
                        <Variable x:TypeArguments="njl:JToken" Name="vatOut" />
                        <Variable x:TypeArguments="x:String" Name="vatCode" />
                      </Sequence.Variables>
                      <If Condition="[Out7(&quot;results&quot;)(0)(&quot;records&quot;)(0)(&quot;VTCD&quot;).ToString &lt;&gt; &quot;&quot; AND Out7(&quot;results&quot;)(0)(&quot;records&quot;)(0)(&quot;VTCD&quot;).ToString &lt;&gt; &quot;0&quot;]" sap2010:WorkflowViewState.IdRef="If_1">
                        <If.Then>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_8">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[vatCode]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[Out7("results")(0)("records")(0)("VTCD").ToString]</InArgument>
                            </Assign.Value>
                          </Assign>
                        </If.Then>
                      </If>
                    </Sequence>
                  </If.Then>
                </If>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_9">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:Int32">[m]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:Int32">[m+1]</InArgument>
                  </Assign.Value>
                </Assign>
                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_4" Response="[respObj8]" StatusCode="[StatusCode8]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                  <iai:IONAPIRequestWizard.Headers>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>Accept</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>application/json</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.Headers>
                  <iai:IONAPIRequestWizard.QueryParameters>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="16">
                        <x:String>INBN</x:String>
                        <x:String>RDTP</x:String>
                        <x:String>DIVI</x:String>
                        <x:String>GRPR</x:String>
                        <x:String>ITNO</x:String>
                        <x:String>IVQA</x:String>
                        <x:String>PNLI</x:String>
                        <x:String>PUNO</x:String>
                        <x:String>RELP</x:String>
                        <x:String>REPN</x:String>
                        <x:String>PNLS</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="16">
                        <x:String>inbn</x:String>
                        <x:String>1</x:String>
                        <x:String>division</x:String>
                        <x:String>grpr</x:String>
                        <x:String>itno</x:String>
                        <x:String>ivqa</x:String>
                        <x:String>pnli</x:String>
                        <x:String>puno</x:String>
                        <x:String>1</x:String>
                        <x:String>repn</x:String>
                        <x:String>pnls</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.QueryParameters>
                </iai:IONAPIRequestWizard>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_10">
                  <Assign.To>
                    <OutArgument x:TypeArguments="njl:JToken">[out8]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj8.ReadAsText)]</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[out8(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_4">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_8">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_96">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[(out8("results")(0)("errorMessage")).ToString]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_3" Line="[commentStatus]" Source="[logfile]" />
                    </Sequence>
                  </If.Then>
                  <If.Else>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_4">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_11">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">Invoice Line has been created</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_2" Line="[commentStatus]" Source="[logfile]" />
                    </Sequence>
                  </If.Else>
                </If>
              </Sequence>
            </If.Else>
          </If>
        </Sequence>
      </ActivityAction>
    </ForEach>
    <sads:DebugSymbol.Symbol>d1NDOlxVc2Vyc1xkbWl0dGFwYWxsaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYyXEFkZExpbmUueGFtbFteA4MDDgIBAWkFgQMPAgECaYYBaZoBAwGFAW4J/wIUAgEDdwt+FAMBgAF/C4YBFAIBe4cBC44BFAIBdo8BC5YBFAIBcZcBC54BFAIBbJ8BC6YBFAIBZ6cBC64BFAIBYq8BC7YBFAIBXrcBC84BJQIBV88BC9YBFAIBU9cBC/4CEAIBBHw2fEEDAYMBeTd5PQMBgQGEATaEAT8CAX6BATeBAT0CAXyMATaMAT8CAXmJATeJAT0CAXeUATaUAT8CAXSRATeRAT0CAXKcATacAT8CAW+ZATeZAT0CAW2kATakAUACAWqhATehAT0CAWisATasAUACAWWpATepAT0CAWO0ATa0AWkCAWGxATexAT0CAV+3AZACtwGcAgIBXLcBvAK3AeUDAgFatwGoArcBtwICAVjUATjUAVsCAVbRATnRAT8CAVTXARnXAWgCAQXZAQ/rARoCAUXuAQ/8AhoCAQbaARHhARoCAU/iARHpARoCAUvqARHqAcQBAgFG9wER/gEaAgFB/wERhgIaAgE9hwIRjgIaAgE4jwIRpQIWAgEtpgIRrQIaAgEorgIR1wIrAgEh2AIR3wIaAgEd4AIR+wIWAgEH3wE83wF8AgFS3AE93AFMAgFQ5wE85wFDAgFO5AE95AFFAgFM6gGdAeoBrgECAUnqAbYB6gHBAQIBR/wBPPwBbwIBRPkBPfkBQwIBQoQCPIQCbwIBQIECPYECQwIBPowCPIwCRQIBO4kCPYkCQwIBOY8CH48CSwIBLpECFaMCIAIBMasCO6sCQAIBK6gCPKgCPwIBKa4CpwKuArMCAgEmrgLTAq4ClgMCASSuAr8CrgLOAgIBIt0CPt0CYQIBINoCP9oCRQIBHuACH+ACbgIBCOICFewCIAIBE+8CFfkCIAIBCZcCF6ICHAIBMuMCF+oCIAIBGesCF+sCygECARTwAhf3AiACAQ/4Ahf4AsoBAgEKlwIllwL5AQIBM5kCG6ACJAIBNOgCQugCcQIBHOUCQ+UCUgIBGusCowHrArQBAgEX6wK8AesCxwECARX1AkL1Al8CARLyAkPyAlICARD4AqMB+AK0AQIBDfgCvAH4AscBAgELngJGngJ5AgE3mwJHmwJQAgE1</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_89" sap:VirtualizedContainerService.HintSize="864,60" />
      <sap2010:ViewStateData Id="Assign_90" sap:VirtualizedContainerService.HintSize="864,60" />
      <sap2010:ViewStateData Id="Assign_91" sap:VirtualizedContainerService.HintSize="864,60" />
      <sap2010:ViewStateData Id="Assign_92" sap:VirtualizedContainerService.HintSize="864,60" />
      <sap2010:ViewStateData Id="Assign_93" sap:VirtualizedContainerService.HintSize="864,60" />
      <sap2010:ViewStateData Id="Assign_94" sap:VirtualizedContainerService.HintSize="864,60" />
      <sap2010:ViewStateData Id="Assign_97" sap:VirtualizedContainerService.HintSize="864,60" />
      <sap2010:ViewStateData Id="Assign_95" sap:VirtualizedContainerService.HintSize="864,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_5" sap:VirtualizedContainerService.HintSize="864,22" />
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="864,60" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="486,332">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="553,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_4" sap:VirtualizedContainerService.HintSize="553,22" />
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_96" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_8" sap:VirtualizedContainerService.HintSize="264,246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_11" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_4" sap:VirtualizedContainerService.HintSize="264,246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="553,394" />
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="575,1171">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_5" sap:VirtualizedContainerService.HintSize="864,1319" />
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="886,2405">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_3" sap:VirtualizedContainerService.HintSize="916,2553" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="938,2677">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="978,2757" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>