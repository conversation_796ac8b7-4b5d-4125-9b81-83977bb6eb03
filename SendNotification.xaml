﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow" this:Workflow.invoiceNumber="123" this:Workflow.poNumber="po1" this:Workflow.invoiceDate="12/12/2024" this:Workflow.total="100" this:Workflow.status="FAILURE" this:Workflow.userIdentifier="TO_APRESP" this:Workflow.distributionType="USER" this:Workflow.emailSubject="TEST" this:Workflow.emailReceivedTime="[System.DateTime.Now.ToString(&quot;yyyy/MM/dd HH:mm:ss&quot;)]" this:Workflow.tenantID="https://mingle-ionapi.inforcloudsuite.com/MANDALA_DEM/" this:Workflow.subTotal="100" this:Workflow.charges="10" this:Workflow.APResp="HARRAG"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:this="clr-namespace:RehostedWorkflowDesigner"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="invoiceNumber" Type="InArgument(x:String)" />
    <x:Property Name="poNumber" Type="InArgument(x:String)" />
    <x:Property Name="invoiceDate" Type="InArgument(x:String)" />
    <x:Property Name="total" Type="InArgument(x:String)" />
    <x:Property Name="tax" Type="InArgument(x:String)" />
    <x:Property Name="drillbackLink" Type="InArgument(x:String)" />
    <x:Property Name="status" Type="InArgument(x:String)" />
    <x:Property Name="statusComments" Type="InArgument(x:String)" />
    <x:Property Name="userIdentifier" Type="InArgument(x:String)" />
    <x:Property Name="distributionType" Type="InArgument(x:String)" />
    <x:Property Name="emailSubject" Type="InArgument(x:String)" />
    <x:Property Name="emailReceivedTime" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="subTotal" Type="InArgument(x:String)" />
    <x:Property Name="charges" Type="InArgument(x:String)" />
    <x:Property Name="discount" Type="InArgument(x:String)" />
    <x:Property Name="deliveryNote" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="APResp" Type="InArgument(x:String)" />
  </x:Members>
  <this:Workflow.tax>
    <InArgument x:TypeArguments="x:String">
      <Literal x:TypeArguments="x:String" Value="" />
    </InArgument>
  </this:Workflow.tax>
  <this:Workflow.statusComments>
    <InArgument x:TypeArguments="x:String">
      <Literal x:TypeArguments="x:String" Value="" />
    </InArgument>
  </this:Workflow.statusComments>
  <this:Workflow.discount>
    <InArgument x:TypeArguments="x:String">
      <Literal x:TypeArguments="x:String" Value="" />
    </InArgument>
  </this:Workflow.discount>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>System.Text.RegularExpressions</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_1">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="message" />
      <Variable x:TypeArguments="x:String" Name="drillbackUrl" />
      <Variable x:TypeArguments="x:String" Name="notificationRequestStr" />
      <Variable x:TypeArguments="njl:JToken" Name="notificationToken" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="notificationResponse" />
      <Variable x:TypeArguments="x:Int32" Name="notificationResponseCode" />
      <Variable x:TypeArguments="x:String" Name="EmailSend" />
      <Variable x:TypeArguments="x:String" Name="body" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="resp" />
    </Sequence.Variables>
    <If Condition="[userIdentifier.ToUpper = &quot;TO_APRESP&quot;]" sap2010:WorkflowViewState.IdRef="If_6">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_2">
          <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_3" Template="{}{&#xD;&#xA;  'erpPersonIds': [&#xD;&#xA;    '{{%APResp%}}'&#xD;&#xA;  ]&#xD;&#xA;}" Text="[body]">
            <ias:Template_Apply.Values>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>APResp</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>APResp</x:String>
                </scg:List>
              </scg:List>
            </ias:Template_Apply.Values>
          </ias:Template_Apply>
          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_3" PostData="[body]" Response="[notificationResponse]" StatusCode="[notificationResponseCode]" Url="[tenantID+ &quot;ifsservice/usermgt/v2/users/search/erppersonids&quot;]">
            <iai:IONAPIRequestWizard.Headers>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>Accept</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>application/json</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.Headers>
            <iai:IONAPIRequestWizard.QueryParameters>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="0" />
                <scg:List x:TypeArguments="x:String" Capacity="0" />
              </scg:List>
            </iai:IONAPIRequestWizard.QueryParameters>
          </iai:IONAPIRequestWizard>
          <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_1">
            <TryCatch.Try>
              <If Condition="[notificationResponse.ReadAsJson(&quot;response&quot;)(&quot;userlist&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_7">
                <If.Then>
                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_3">
                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" QueryParameters="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_4" Response="[resp]" Url="[tenantID+ &quot;ifsservice/usermgt/v2/users/me&quot;]" />
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_12">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[userIdentifier]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[resp.ReadAsJson("response")("userlist")(0)("id").ToString]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </Sequence>
                </If.Then>
                <If.Else>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_10">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[userIdentifier]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[notificationResponse.ReadAsJson("response")("userlist")(0)("id").ToString]</InArgument>
                    </Assign.Value>
                  </Assign>
                </If.Else>
              </If>
            </TryCatch.Try>
            <TryCatch.Catches>
              <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_1">
                <ActivityAction x:TypeArguments="s:Exception">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                  </ActivityAction.Argument>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_13">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[userIdentifier]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">
                        <Literal x:TypeArguments="x:String" Value="" />
                      </InArgument>
                    </Assign.Value>
                  </Assign>
                </ActivityAction>
              </Catch>
            </TryCatch.Catches>
          </TryCatch>
        </Sequence>
      </If.Then>
    </If>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_9">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[EmailSend]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">false</InArgument>
      </Assign.Value>
    </Assign>
    <If Condition="[miscValues(&quot;SendEmail&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_4">
      <If.Then>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_8">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[EmailSend]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">true</InArgument>
          </Assign.Value>
        </Assign>
      </If.Then>
    </If>
    <If Condition="[emailSubject.contains(&quot;'&quot;)]" sap2010:WorkflowViewState.IdRef="If_2">
      <If.Then>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_3">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[emailSubject]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[emailSubject.Replace("'","")]</InArgument>
          </Assign.Value>
        </Assign>
      </If.Then>
    </If>
    <If Condition="[userIdentifier.ToUpper = &quot;TO_APRESP&quot;]" sap2010:WorkflowViewState.IdRef="If_5">
      <If.Then>
        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_2" PostData="[notificationToken.tostring]" Response="[notificationResponse]" ResponseCode="[notificationResponseCode]" Url="[tenantID+ &quot;ifsservice/usermgt/v2/users/search/erppersonids&quot;]">
          <iai:IONAPIRequestWizard.Headers>
            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
              <scg:List x:TypeArguments="x:String" Capacity="4">
                <x:String>Accept</x:String>
              </scg:List>
              <scg:List x:TypeArguments="x:String" Capacity="4">
                <x:String>application/json</x:String>
              </scg:List>
            </scg:List>
          </iai:IONAPIRequestWizard.Headers>
          <iai:IONAPIRequestWizard.QueryParameters>
            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
              <scg:List x:TypeArguments="x:String" Capacity="4">
                <x:String>logicalId</x:String>
              </scg:List>
              <scg:List x:TypeArguments="x:String" Capacity="4">
                <x:String>lid://infor.rpa.1</x:String>
              </scg:List>
            </scg:List>
          </iai:IONAPIRequestWizard.QueryParameters>
        </iai:IONAPIRequestWizard>
      </If.Then>
    </If>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_1">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[message]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">["RPA M3 IP" +" - " + status + "( "+statusComments+")"]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_2">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[drillbackUrl]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[tenantId+ "?" + drillbackLink]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_5">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[deliveryNote]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[deliveryNote.Replace(Environment.NewLine, "").Replace(" ","")]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_7">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[deliveryNote]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[Regex.Replace(deliveryNote, "[^a-zA-Z0-9,]", "").ToString]</InArgument>
      </Assign.Value>
    </Assign>
    <If Condition="[deliveryNote &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_3">
      <If.Then>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_4">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[deliveryNote]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[deliveryNote.Replace("""","").Replace("\r\n","").Replace("vbLf", "")]</InArgument>
          </Assign.Value>
        </Assign>
      </If.Then>
    </If>
    <If Condition="[status = &quot;SUCCESS&quot;]" sap2010:WorkflowViewState.IdRef="If_1">
      <If.Then>
        <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_1" Template="{}{ 'message': '{{%msg%}}',  'parameters': [{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_STATUS', 'value':  '{{%status%}}', 'label': 'Status', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_Subject', 'value':  '{{%Subject%}}', 'label': 'Subject', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_RcvdTime', 'value':  '{{%RcvdTime%}}', 'label': 'Recieved Time', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_INVNO', 'value':  '{{%InvNumber%}}', 'label': 'Invoice Number', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_PONO', 'value':  '{{%PONumber%}}', 'label': 'PO Number', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_INVDate', 'value':  '{{%InvDate%}}', 'label': 'Invoice Date', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_SubTotal', 'value':  '{{%subTotal%}}', 'label': 'Sub Total', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_Tax', 'value':  '{{%Tax%}}', 'label': 'Tax', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_Charges', 'value':  '{{%Charges%}}', 'label': 'Shipping Charges', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_Discount', 'value':  '{{%Discount%}}', 'label': 'Discount', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_Total', 'value':  '{{%Total%}}', 'label': 'Total', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_DeliveryNote', 'value':  '{{%DeliveryNote%}}', 'label': 'Delivery Note Numbers', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_Remarks', 'value':  '{{%Remarks%}}', 'label': 'Remarks', 'readOnly': true }],'views': [{'name':'LinkToDashboard','label': 'Invoice','properties': [{'name': '{{%drill%}}','value': '{{%totalDrill%}}'}]}], 'category': 'RPA', 'distribution': [{ 'identifier': '{{%userIdentifier%}}','type': '{{%distributionType%}}','sendMail': {{%EmailSend%}} }] }" Text="[notificationRequestStr]">
          <ias:Template_Apply.Values>
            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
              <scg:List x:TypeArguments="x:String" Capacity="32">
                <x:String>status</x:String>
                <x:String>userIdentifier</x:String>
                <x:String>distributionType</x:String>
                <x:String>InvNumber</x:String>
                <x:String>PONumber</x:String>
                <x:String>InvDate</x:String>
                <x:String>subTotal</x:String>
                <x:String>Tax</x:String>
                <x:String>Total</x:String>
                <x:String>Remarks</x:String>
                <x:String>RcvdTime</x:String>
                <x:String>Subject</x:String>
                <x:String>msg</x:String>
                <x:String>drill</x:String>
                <x:String>totalDrill</x:String>
                <x:String>Charges</x:String>
                <x:String>Discount</x:String>
                <x:String>DeliveryNote</x:String>
                <x:String>EmailSend</x:String>
              </scg:List>
              <scg:List x:TypeArguments="x:String" Capacity="32">
                <x:String>status</x:String>
                <x:String>userIdentifier</x:String>
                <x:String>distributionType</x:String>
                <x:String>invoiceNumber</x:String>
                <x:String>poNumber</x:String>
                <x:String>invoiceDate</x:String>
                <x:String>subTotal</x:String>
                <x:String>tax</x:String>
                <x:String>total</x:String>
                <x:String>statusComments</x:String>
                <x:String>emailReceivedTime</x:String>
                <x:String>emailSubject</x:String>
                <x:String>message</x:String>
                <x:String>drillbackLink</x:String>
                <x:String>drillbackUrl</x:String>
                <x:String>charges</x:String>
                <x:String>discount</x:String>
                <x:String>deliveryNote</x:String>
                <x:String>EmailSend</x:String>
              </scg:List>
            </scg:List>
          </ias:Template_Apply.Values>
        </ias:Template_Apply>
      </If.Then>
      <If.Else>
        <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_2" Template="{}{ 'message': '{{%msg%}}',  'parameters': [{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_STATUS', 'value':  '{{%status%}}', 'label': 'Status', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_Subject', 'value':  '{{%Subject%}}', 'label': 'Subject', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_RcvdTime', 'value':  '{{%RcvdTime%}}', 'label': 'Recieved Time', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_INVNO', 'value':  '{{%InvNumber%}}', 'label': 'Invoice Number', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_PONO', 'value':  '{{%PONumber%}}', 'label': 'PO Number', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_INVDate', 'value':  '{{%InvDate%}}', 'label': 'Invoice Date', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_SubTotal', 'value':  '{{%subTotal%}}', 'label': 'Sub Total', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_Tax', 'value':  '{{%Tax%}}', 'label': 'Tax', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_Charges', 'value':  '{{%Charges%}}', 'label': 'Shipping Charges', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_Discount', 'value':  '{{%Discount%}}', 'label': 'Discount', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_Total', 'value':  '{{%Total%}}', 'label': 'Total', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_DeliveryNote', 'value':  '{{%DeliveryNote%}}', 'label': 'Delivery Note Numbers', 'readOnly': true },{ 'dataType': 'STRING',  'name': 'RPA_PROOFOFDELIVERY_Remarks', 'value':  '{{%Remarks%}}', 'label': 'Remarks', 'readOnly': true }], 'category': 'RPA', 'distribution': [{ 'identifier': '{{%userIdentifier%}}','type': '{{%distributionType%}}','sendMail': {{%EmailSend%}} }] }" Text="[notificationRequestStr]">
          <ias:Template_Apply.Values>
            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
              <scg:List x:TypeArguments="x:String" Capacity="32">
                <x:String>status</x:String>
                <x:String>userIdentifier</x:String>
                <x:String>distributionType</x:String>
                <x:String>InvNumber</x:String>
                <x:String>PONumber</x:String>
                <x:String>InvDate</x:String>
                <x:String>subTotal</x:String>
                <x:String>Tax</x:String>
                <x:String>Total</x:String>
                <x:String>Remarks</x:String>
                <x:String>RcvdTime</x:String>
                <x:String>Subject</x:String>
                <x:String>msg</x:String>
                <x:String>drill</x:String>
                <x:String>totalDrill</x:String>
                <x:String>Charges</x:String>
                <x:String>Discount</x:String>
                <x:String>DeliveryNote</x:String>
                <x:String>EmailSend</x:String>
              </scg:List>
              <scg:List x:TypeArguments="x:String" Capacity="32">
                <x:String>status</x:String>
                <x:String>userIdentifier</x:String>
                <x:String>distributionType</x:String>
                <x:String>invoiceNumber</x:String>
                <x:String>poNumber</x:String>
                <x:String>invoiceDate</x:String>
                <x:String>subTotal</x:String>
                <x:String>tax</x:String>
                <x:String>total</x:String>
                <x:String>statusComments</x:String>
                <x:String>emailReceivedTime</x:String>
                <x:String>emailSubject</x:String>
                <x:String>message</x:String>
                <x:String>drillbackLink</x:String>
                <x:String>drillbackUrl</x:String>
                <x:String>charges</x:String>
                <x:String>discount</x:String>
                <x:String>deliveryNote</x:String>
                <x:String>EmailSend</x:String>
              </scg:List>
            </scg:List>
          </ias:Template_Apply.Values>
        </ias:Template_Apply>
      </If.Else>
    </If>
    <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_1" JTokenObject="[notificationToken]" JTokenString="[notificationRequestStr]" />
    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_1" PostData="[notificationToken.tostring]" Response="[notificationResponse]" ResponseCode="[notificationResponseCode]" Url="[tenantID+ &quot;IONSERVICES/process/application/v1/pulse/notification/create&quot;]">
      <iai:IONAPIRequestWizard.Headers>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>Accept</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>application/json</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.Headers>
      <iai:IONAPIRequestWizard.QueryParameters>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>logicalId</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>lid://infor.rpa.1</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.QueryParameters>
    </iai:IONAPIRequestWizard>
    <sads:DebugSymbol.Symbol>d1xDOlxVc2Vyc1xkbWl0dGFwYWxsaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYyXFNlbmROb3RpZmljYXRpb24ueGFtbGEBsAEBuwEBEQFzAXcBEAHMBAHPBAEPAZMCAZ0CAQ4BhwMBxwMBDSgHKDYBDAHmBAHtBAELMgcyNgEKAbAEAbQEAQktBy02AQgBvgIBwwIBBwHsAQH0AQEGAeACAeUCAQUB4AMBlwQBBAHRAQHVAQEDAZABAZQBAQJmA6oDDgIBAXIFxQEKAgFUxgEFzQEOAgFQzgEF2QEKAgFJ2gEF5QEKAgFB5gEF/wEKAgE2gAIFhwIOAgEwiAIFjwIOAgEqkAIFlwIOAgEmmAIFnwIOAgEioAIFqwIKAgEarAIFkQMKAgEPkgMFkgPmAQIBCpMDBagDHwIBAnITckUCAVV0CcMBFAIBV8sBMMsBNQIBU8gBMcgBPAIBUc4BE84BXAIBStABCdcBEgIBTNoBE9oBOwIBQtwBCeMBEgIBROYBE+YBRQIBN+gBCf0BIwIBOYUCMIUCZwIBM4ICMYICOgIBMY0CMI0CTwIBLYoCMYoCPwIBK5UCMJUCbwIBKZICMZICPwIBJ50CMJ0CawIBJZoCMZoCPwIBI6ACE6ACOQIBG6ICCakCEgIBHawCE6wCMwIBEK4CCd0CHgIBFuACCY8DHgIBEpIDyQGSA+MBAgENkgOmAZIDuwECAQuTA/UBkwOTAgIBCZMDwwKTA98CAgEHkwOdApMDtQICAQWTA+QCkwO6AwIBA3ULgAEgAgF2gQELkgElAgFtkwELwgEWAgFY1QE01QE4AgFP0gE10gFAAgFN4QE04QFSAgFH3gE13gFDAgFF6AH5AegBlwICAUDoAccC6AHjAgIBPugBoQLoAbkCAgE86AHoAugBsQMCATqnAjSnAnoCASCkAjWkAkMCAR6uAqcRrgLBEQIBGK4CpwGuAqERAgEX4AKqEOACxBACARTgAqcB4AKkEAIBE3WFAnWNAgIBeHWpAXX/AQIBd4EB/QGBAYUCAgF0gQGPAoEBpwICAXKBAdQCgQGdAwIBcIEBswKBAc8CAgFulQEPrQEUAgFdtQETvgEcAgFZlQEdlQGGAQIBXpcBE6EBHgIBY6QBE6sBHAIBX7sBGbsBSAIBXLcBP7cBTwIBWpgBFZgBpgMCAWiZARWgAR4CAWSpAT6pAYkBAgFipgE/pgFPAgFgmAHeApgB5gICAWuYAesCmAGjAwIBaZ4BQJ4BewIBZ5sBQZsBUQIBZQ==</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Template_Apply_3" sap:VirtualizedContainerService.HintSize="414,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_3" sap:VirtualizedContainerService.HintSize="414,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_12" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_7" sap:VirtualizedContainerService.HintSize="532,402">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_13" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_1" sap:VirtualizedContainerService.HintSize="400,132.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_1" sap:VirtualizedContainerService.HintSize="414,304.666666666667" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="436,552.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_6" sap:VirtualizedContainerService.HintSize="562,706.666666666667" />
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="562,62" />
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="562,216" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="562,216" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_2" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_5" sap:VirtualizedContainerService.HintSize="562,214" />
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="562,62" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="562,62" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="562,62" />
      <sap2010:ViewStateData Id="Assign_7" sap:VirtualizedContainerService.HintSize="562,62" />
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="562,216" />
      <sap2010:ViewStateData Id="Template_Apply_1" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Template_Apply_2" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="464,214" />
      <sap2010:ViewStateData Id="DeserializeJSON_1" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_1" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="584,2740.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="624,3380.66666666667" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>