﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:sl="clr-namespace:System.Linq;assembly=System.Core"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="ocrValues" Type="InArgument(s:String[])" />
    <x:Property Name="colemanAPI" Type="InArgument(x:String)" />
    <x:Property Name="commentStatus" Type="OutArgument(x:String)" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="status" Type="OutArgument(x:String)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="vendorId" Type="OutArgument(x:String)" />
    <x:Property Name="vendorName" Type="OutArgument(x:String)" />
    <x:Property Name="company" Type="OutArgument(x:String)" />
    <x:Property Name="chargeCode" Type="InArgument(x:String)" />
    <x:Property Name="discountCode" Type="InArgument(x:String)" />
    <x:Property Name="authUser" Type="InArgument(x:String)" />
    <x:Property Name="division" Type="OutArgument(x:String)" />
    <x:Property Name="vatCodeConfig" Type="InArgument(x:String)" />
    <x:Property Name="poDiscountsHandlingConfig" Type="InArgument(x:Boolean)" />
    <x:Property Name="businessRule" Type="InArgument(x:String)" />
    <x:Property Name="ocrText" Type="InArgument(njl:JToken)" />
    <x:Property Name="matchVendorItemCode" Type="InArgument(x:Boolean)" />
    <x:Property Name="documentPath" Type="InArgument(x:String)" />
    <x:Property Name="approvalRequired" Type="InArgument(x:Boolean)" />
    <x:Property Name="approvalWorkflow" Type="InArgument(x:String)" />
    <x:Property Name="checkAmountBussinessRule" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.Text.RegularExpressions</x:String>
      <x:String>System.Globalization</x:String>
      <x:String>Microsoft.VisualBasic</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="ProcessPOInvoiceAPISequence" sap2010:WorkflowViewState.IdRef="Sequence_17">
    <Sequence.Variables>
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj1" />
      <Variable x:TypeArguments="njl:JToken" Name="out1" />
      <Variable x:TypeArguments="x:String" Name="req1" />
      <Variable x:TypeArguments="x:String" Name="pono" />
      <Variable x:TypeArguments="x:Int32" Name="count1" />
      <Variable x:TypeArguments="x:String" Name="req2" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj2" />
      <Variable x:TypeArguments="njl:JToken" Name="out2" />
      <Variable x:TypeArguments="scg:List(s:String[])" Name="M3TotalTableRows" />
      <Variable x:TypeArguments="x:Int32" Name="i" />
      <Variable x:TypeArguments="s:String[]" Name="M3Values" />
      <Variable x:TypeArguments="x:String" Name="cono" />
      <Variable x:TypeArguments="x:String" Name="cucd" />
      <Variable x:TypeArguments="x:String" Name="SupplierNo" />
      <Variable x:TypeArguments="x:String" Name="APIString" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode1" />
      <Variable x:TypeArguments="x:String" Name="pyme" />
      <Variable x:TypeArguments="scg:List(x:String)" Name="invoicesItemNumbers" />
      <Variable x:TypeArguments="x:Boolean" Name="allLinesReceived" />
      <Variable x:TypeArguments="scg:IEnumerable(sl:IGrouping(x:String, s:String[]))" Name="transDateGroups" />
      <Variable x:TypeArguments="scg:Dictionary(x:String, scg:List(s:String[]))" Name="transDateDictionary" />
    </Sequence.Variables>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_1">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[pono]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[ocrValues(4)]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_136">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">
          <Literal x:TypeArguments="x:String" Value="" />
        </InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_137">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[vendorName]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">
          <Literal x:TypeArguments="x:String" Value="" />
        </InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_158">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[company]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">
          <Literal x:TypeArguments="x:String" Value="" />
        </InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_157">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[division]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">
          <Literal x:TypeArguments="x:String" Value="" />
        </InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_2">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[req1]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">["IACONO,IADIVI,IASUNO,IACUCD,IATEPY,IAPYME from MPHEAD where IAPUNO = '" + pono + "'"]</InArgument>
      </Assign.Value>
    </Assign>
    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="ExportMI for comp, div IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_1" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/EXPORTMI/Select?SEPC=~&amp;HDRS=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
      <iai:IONAPIRequestWizard.QueryParameters>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>QERY</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>req1</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.QueryParameters>
    </iai:IONAPIRequestWizard>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_240">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(x:String)">[invoicesItemNumbers]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)()]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_271">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(s:String[]))">[transDateDictionary]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(s:String[]))">[New Dictionary(Of String,List(Of String()))()]</InArgument>
      </Assign.Value>
    </Assign>
    <If Condition="[StatusCode1 = 200]" DisplayName="If" sap2010:WorkflowViewState.IdRef="If_14">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_22">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_3">
            <Assign.To>
              <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj1.ReadAsText)]</InArgument>
            </Assign.Value>
          </Assign>
          <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_11">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_1">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_7">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">PONOTAVAILABLE</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_111">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["PO number is not available"]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="[commentStatus]" Source="[logFile]" />
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_16">
                <Sequence.Variables>
                  <Variable x:TypeArguments="njl:JToken" Name="httpOut" />
                  <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="ColemanOutPut" />
                  <Variable x:TypeArguments="x:Int32" Name="ColemanRespCode" />
                  <Variable x:TypeArguments="x:String" Name="inbnValue" />
                  <Variable x:TypeArguments="x:Int32" Name="StatusCode2" />
                  <Variable x:TypeArguments="x:String" Name="tepy" />
                  <Variable x:TypeArguments="x:Int32" Name="StatusCode3" />
                  <Variable x:TypeArguments="iru:ResponseObject" Name="RespObj3" />
                  <Variable x:TypeArguments="njl:JToken" Name="out3" />
                  <Variable x:TypeArguments="x:String" Name="colAmount" />
                  <Variable x:TypeArguments="x:String" Name="discountHandling" />
                  <Variable x:TypeArguments="iru:ResponseObject" Name="poLinesResponseObject" />
                  <Variable x:TypeArguments="scg:List(x:String)" Name="notReceivedItems" />
                </Sequence.Variables>
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_19" Line="Extracted the company and division." Source="[logfile]" />
                <Assign sap2010:WorkflowViewState.IdRef="Assign_8">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[division]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(1).ToString()]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_141">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[company]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(0).ToString()]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_142">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(2).ToString()]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_235">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[colAmount]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[ocrValues(3)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_239">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[discountHandling]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">False</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[poDiscountsHandlingConfig]" sap2010:WorkflowViewState.IdRef="If_88">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_101">
                      <Sequence.Variables>
                        <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="vendorOut" />
                      </Sequence.Variables>
                      <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;vendorID&quot;,vendorId},{&quot;businessRulesAPIURL&quot;,TenantID +&quot;IONSERVICES/businessrules/decision/execute/&quot;+businessRule}}]" ContinueOnError="True" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_2" OutputArguments="[vendorOut]" WorkflowFile="[projectPath + &quot;\vendorIDMatch.xaml&quot;]" />
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_238">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[discountHandling]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[CType(vendorOut("discountHandling"), string)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <If Condition="[discountHandling = &quot;True&quot;]" sap2010:WorkflowViewState.IdRef="If_94">
                        <If.Then>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_102">
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_234">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[colAmount]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[ocrValues(2)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <If Condition="[ocrValues(6) &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_90">
                              <If.Then>
                                <If Condition="[Double.Parse(ocrValues(6))  &lt;&gt; 0]" sap2010:WorkflowViewState.IdRef="If_91">
                                  <If.Then>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_236">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[colAmount]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">[(Double.Parse(colAmount) - Double.Parse(ocrValues(6))).ToString]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                  </If.Then>
                                </If>
                              </If.Then>
                            </If>
                            <If Condition="[ocrValues(5) &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_93">
                              <If.Then>
                                <If Condition="[Double.Parse(ocrValues(5))  &lt;&gt; 0]" sap2010:WorkflowViewState.IdRef="If_92">
                                  <If.Then>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_237">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[colAmount]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">[(Double.Parse(colAmount) - Double.Parse(ocrValues(5))).ToString]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                  </If.Then>
                                </If>
                              </If.Then>
                            </If>
                          </Sequence>
                        </If.Then>
                      </If>
                    </Sequence>
                  </If.Then>
                </If>
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_56" Line="[&quot;VendorID : &quot; + vendorid]" Source="[logFile]" />
                <If Condition="False" sap2010:WorkflowViewState.IdRef="If_52">
                  <If.Then>
                    <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;)(0)(&quot;REPL&quot;).ToString().split(&quot;~&quot;C)(4).ToString().Length = 2]" sap2010:WorkflowViewState.IdRef="If_51">
                      <If.Then>
                        <Assign DisplayName="Assign tepy" sap2010:WorkflowViewState.IdRef="Assign_166">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[tepy]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">["0" + Convert.ToInt32(out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(4).ToString(),10).ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                      </If.Then>
                      <If.Else>
                        <Assign DisplayName="Assign tepy" sap2010:WorkflowViewState.IdRef="Assign_167">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[tepy]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(4).ToString()]</InArgument>
                          </Assign.Value>
                        </Assign>
                      </If.Else>
                    </If>
                  </If.Then>
                </If>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_168">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[tepy]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(4).ToString()]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_164">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[pyme]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(5).ToString()]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_163">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[cucd]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(3).ToString()]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_9">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["F2PNLI,F2REPN,F2SCOC,F2RPQA,F2TRDT,F2ITNO,F2CONO,F2CUCD,F2DIVI,F2SUNO,F2IVQA,F2PUNO,F2SUDO,F2RCAC,F2RPQT,F2SCOP,F2SERA,F2IVQT,F2ICAC from FGRECL00 where F2DIVI = " + DIVISION+" AND F2PUNO = " + pono + " and F2IMST != 9 "]</InArgument>
                  </Assign.Value>
                </Assign>
                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for vendor name" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_17" Response="[respObj3]" StatusCode="[StatusCode3]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/CRS620MI/GetBasicData&quot;]">
                  <iai:IONAPIRequestWizard.Headers>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="0" />
                      <scg:List x:TypeArguments="x:String" Capacity="0" />
                    </scg:List>
                  </iai:IONAPIRequestWizard.Headers>
                  <iai:IONAPIRequestWizard.QueryParameters>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="8">
                        <x:String>extendedresult</x:String>
                        <x:String>format</x:String>
                        <x:String>righttrim</x:String>
                        <x:String>excludeempty</x:String>
                        <x:String>dateformat</x:String>
                        <x:String>SUNO</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="8">
                        <x:String>false</x:String>
                        <x:String>PRETTY</x:String>
                        <x:String>true</x:String>
                        <x:String>false</x:String>
                        <x:String>YMD8</x:String>
                        <x:String>vendorId</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.QueryParameters>
                </iai:IONAPIRequestWizard>
                <If Condition="[StatusCode3 = 200]" sap2010:WorkflowViewState.IdRef="If_55">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_67">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_170">
                        <Assign.To>
                          <OutArgument x:TypeArguments="njl:JToken">[out3]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj3.ReadAsText)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <If Condition="[out3(&quot;results&quot;)(0)(&quot;records&quot;).ToString &lt;&gt; &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_56">
                        <If.Then>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_68">
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_171">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[vendorName]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[out3("results")(0)("records")(0)("SUNM").ToString]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <If Condition="[authUser.ToUpper() = &quot;SUPPLIERAUTHUSER&quot;]" sap2010:WorkflowViewState.IdRef="If_57">
                              <If.Then>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_69">
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_172">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[authUser]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[out3("results")(0)("records")(0)("RESP").ToString]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </Sequence>
                              </If.Then>
                            </If>
                          </Sequence>
                        </If.Then>
                        <If.Else>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_99">
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_227">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">["Vendor name is not obtained for the Supplier Number " + vendorId+"."]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_228">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_55" Line="[commentStatus]" Source="[logfile]" />
                          </Sequence>
                        </If.Else>
                      </If>
                    </Sequence>
                  </If.Then>
                  <If.Else>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_98">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_225">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching Vendor name for the Vendor Id " + vendorId+"."]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_226">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_54" Line="[commentStatus]" Source="[logfile]" />
                    </Sequence>
                  </If.Else>
                </If>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_256">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:Boolean">[allLinesReceived]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_283">
                  <Assign.To>
                    <OutArgument x:TypeArguments="scg:List(x:String)">[notReceivedItems]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)()]</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[matchVendorItemCode]" sap2010:WorkflowViewState.IdRef="If_117">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_124">
                      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_31" Response="[poLinesResponseObject]" Url="[TenantID +&quot;M3/m3api-rest/v2/execute/PPS200MI/LstLine?maxrecs=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;returncols=PUNO%2CITNO%2CSITE%2CORQA%2CRVQA&amp;format=PRETTY&amp;extendedresult=false&quot;]">
                        <iai:IONAPIRequestWizard.QueryParameters>
                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                            <scg:List x:TypeArguments="x:String" Capacity="4">
                              <x:String>PUNO</x:String>
                            </scg:List>
                            <scg:List x:TypeArguments="x:String" Capacity="4">
                              <x:String>pono</x:String>
                            </scg:List>
                          </scg:List>
                        </iai:IONAPIRequestWizard.QueryParameters>
                      </iai:IONAPIRequestWizard>
                      <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_7">
                        <TryCatch.Try>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_180">
                            <If Condition="[tenantID.contains(&quot;WHEELER&quot;) and (ocrText.tostring.contains(&quot;MANN+HUMMEL Filtration Technology US&quot;) OR ocrText.tostring.contains(&quot;CHAMPION LABORATORIES&quot;) OR OCRText.ToString.Contains(&quot;B.J. Maurer Ford&quot;) OR OCRText.ToString.Contains(&quot;bjmaurerford&quot;) OR OCRText.ToString.Contains(&quot;FREIGHTLINER WESTERN STAR OF NEW STANTON&quot;))]" sap2010:WorkflowViewState.IdRef="If_170">
                              <If.Then>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_188">
                                  <Sequence.Variables>
                                    <Variable x:TypeArguments="x:Int32" Name="extractTableResponseCode" />
                                    <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="extractTablesDictionary" />
                                  </Sequence.Variables>
                                  <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;ocrText&quot;,ocrText},{&quot;documentPath&quot;,documentPath}}]" ContinueOnError="True" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_5" OutputArguments="[extractTablesDictionary]" ResponseCode="[extractTableResponseCode]" WorkflowFile="[projectPath+&quot;\WheelerLines.xaml&quot;]" />
                                  <If Condition="[extractTableResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_261">
                                    <If.Then>
                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_282">
                                        <Sequence.Variables>
                                          <Variable x:TypeArguments="scg:List(x:String)" Name="ocrTablesItemCodes" />
                                        </Sequence.Variables>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_517">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="scg:List(x:String)">[ocrTablesItemCodes]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="scg:List(x:String)">[CType(extractTablesDictionary("ocrTablesItemCodes"), List(Of String))]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_33" Values="[poLinesResponseObject.readasjson(&quot;results&quot;)(0)(&quot;records&quot;)]">
                                          <ActivityAction x:TypeArguments="njl:JToken">
                                            <ActivityAction.Argument>
                                              <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
                                            </ActivityAction.Argument>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_186">
                                              <If Condition="[ocrTablesItemCodes.Any(Function(s) Regex.Replace(s, &quot;[^a-zA-Z0-9]&quot;, &quot;&quot;).Contains(Regex.Replace(item(&quot;ITNO&quot;).tostring.tolower, &quot;[^a-zA-Z0-9]&quot;, &quot;&quot;))) OR ocrTablesItemCodes.Any(Function(s) Regex.Replace(s, &quot;[^a-zA-Z0-9]&quot;, &quot;&quot;).Contains(Regex.Replace(item(&quot;SITE&quot;).tostring.tolower, &quot;[^a-zA-Z0-9]&quot;, &quot;&quot;)))]" sap2010:WorkflowViewState.IdRef="If_174">
                                                <If.Then>
                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_182">
                                                    <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_13" MethodName="Add">
                                                      <InvokeMethod.TargetObject>
                                                        <InArgument x:TypeArguments="scg:List(x:String)">[invoicesItemNumbers]</InArgument>
                                                      </InvokeMethod.TargetObject>
                                                      <InArgument x:TypeArguments="x:String">[item("ITNO").tostring]</InArgument>
                                                    </InvokeMethod>
                                                    <If Condition="[Integer.Parse(item(&quot;ORQA&quot;).tostring) &gt; Integer.Parse(item(&quot;RVQA&quot;).tostring)]" sap2010:WorkflowViewState.IdRef="If_171">
                                                      <If.Then>
                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_181">
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_370">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:Boolean">[allLinesReceived]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_14" MethodName="Add">
                                                            <InvokeMethod.TargetObject>
                                                              <InArgument x:TypeArguments="scg:List(x:String)">[notReceivedItems]</InArgument>
                                                            </InvokeMethod.TargetObject>
                                                            <InArgument x:TypeArguments="x:String">[item("ITNO").tostring]</InArgument>
                                                          </InvokeMethod>
                                                        </Sequence>
                                                      </If.Then>
                                                    </If>
                                                  </Sequence>
                                                </If.Then>
                                              </If>
                                            </Sequence>
                                          </ActivityAction>
                                        </ForEach>
                                      </Sequence>
                                    </If.Then>
                                  </If>
                                </Sequence>
                              </If.Then>
                              <If.Else>
                                <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_9" Values="[poLinesResponseObject.readasjson(&quot;results&quot;)(0)(&quot;records&quot;)]">
                                  <ActivityAction x:TypeArguments="njl:JToken">
                                    <ActivityAction.Argument>
                                      <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
                                    </ActivityAction.Argument>
                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_103">
                                      <If Condition="[ocrText(&quot;data&quot;).tostring.tolower.contains(&quot; &quot;+item(&quot;ITNO&quot;).tostring.tolower+&quot; &quot;) OR ocrText(&quot;data&quot;).tostring.tolower.contains(&quot; &quot;+item(&quot;SITE&quot;).tostring.tolower+&quot; &quot;)]" sap2010:WorkflowViewState.IdRef="If_96">
                                        <If.Then>
                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_117">
                                            <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_2" MethodName="Add">
                                              <InvokeMethod.TargetObject>
                                                <InArgument x:TypeArguments="scg:List(x:String)">[invoicesItemNumbers]</InArgument>
                                              </InvokeMethod.TargetObject>
                                              <InArgument x:TypeArguments="x:String">[item("ITNO").tostring]</InArgument>
                                            </InvokeMethod>
                                            <If Condition="[Integer.Parse(item(&quot;ORQA&quot;).tostring) &gt; Integer.Parse(item(&quot;RVQA&quot;).tostring)]" sap2010:WorkflowViewState.IdRef="If_110">
                                              <If.Then>
                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_134">
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_257">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:Boolean">[allLinesReceived]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_8" MethodName="Add">
                                                    <InvokeMethod.TargetObject>
                                                      <InArgument x:TypeArguments="scg:List(x:String)">[notReceivedItems]</InArgument>
                                                    </InvokeMethod.TargetObject>
                                                    <InArgument x:TypeArguments="x:String">[item("ITNO").tostring]</InArgument>
                                                  </InvokeMethod>
                                                </Sequence>
                                              </If.Then>
                                            </If>
                                          </Sequence>
                                        </If.Then>
                                        <If.Else>
                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_108">
                                            <Sequence.Variables>
                                              <Variable x:TypeArguments="x:String" Name="itemNumberWithPrefix" />
                                              <Variable x:TypeArguments="x:String" Name="itemSupNumberWithPrefix" />
                                            </Sequence.Variables>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_243">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[itemSupNumberWithPrefix]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(item("SITE").tostring, "(\d+)", " $1 ")]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_369">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[itemNumberWithPrefix]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(item("ITNO").tostring, "(\d+)", " $1 ")]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <If Condition="[ocrText(&quot;data&quot;).tostring.tolower.contains(&quot; &quot;+itemNumberWithPrefix.trim.tolower+&quot; &quot;) OR ocrText(&quot;data&quot;).tostring.tolower.contains(&quot; &quot;+itemSupNumberWithPrefix.trim.tolower+&quot; &quot;)]" sap2010:WorkflowViewState.IdRef="If_102">
                                              <If.Then>
                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_120">
                                                  <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_3" MethodName="Add">
                                                    <InvokeMethod.TargetObject>
                                                      <InArgument x:TypeArguments="scg:List(x:String)">[invoicesItemNumbers]</InArgument>
                                                    </InvokeMethod.TargetObject>
                                                    <InArgument x:TypeArguments="x:String">[item("ITNO").tostring]</InArgument>
                                                  </InvokeMethod>
                                                  <If Condition="[Integer.Parse(item(&quot;ORQA&quot;).tostring) &gt; Integer.Parse(item(&quot;RVQA&quot;).tostring)]" sap2010:WorkflowViewState.IdRef="If_111">
                                                    <If.Then>
                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_135">
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_258">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:Boolean">[allLinesReceived]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_9" MethodName="Add">
                                                          <InvokeMethod.TargetObject>
                                                            <InArgument x:TypeArguments="scg:List(x:String)">[notReceivedItems]</InArgument>
                                                          </InvokeMethod.TargetObject>
                                                          <InArgument x:TypeArguments="x:String">[item("ITNO").tostring]</InArgument>
                                                        </InvokeMethod>
                                                      </Sequence>
                                                    </If.Then>
                                                  </If>
                                                </Sequence>
                                              </If.Then>
                                            </If>
                                          </Sequence>
                                        </If.Else>
                                      </If>
                                    </Sequence>
                                  </ActivityAction>
                                </ForEach>
                              </If.Else>
                            </If>
                          </Sequence>
                        </TryCatch.Try>
                        <TryCatch.Catches>
                          <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_7">
                            <ActivityAction x:TypeArguments="s:Exception">
                              <ActivityAction.Argument>
                                <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                              </ActivityAction.Argument>
                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_65" Line="Error reading item codes" Source="[logfile]" />
                            </ActivityAction>
                          </Catch>
                        </TryCatch.Catches>
                      </TryCatch>
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_63" Line="[&quot;Invoice lines count : &quot;+invoicesItemNumbers.Count.tostring]" Source="[logfile]" />
                    </Sequence>
                  </If.Then>
                </If>
                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Export MI lines IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_2" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/EXPORTMI/Select?HDRS=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&amp;maxrecs=0&quot;]">
                  <iai:IONAPIRequestWizard.QueryParameters>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>QERY</x:String>
                        <x:String>SEPC</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>req2</x:String>
                        <x:String>~</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.QueryParameters>
                </iai:IONAPIRequestWizard>
                <If Condition="[StatusCode2 = 200]" sap2010:WorkflowViewState.IdRef="If_15">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_23">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_10">
                        <Assign.To>
                          <OutArgument x:TypeArguments="njl:JToken">[out2]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <If Condition="[out2(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_6">
                        <If.Then>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_2">
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_13">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">No items listed in the PO have been received.</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_14">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">PONOTRECEIVED</InArgument>
                              </Assign.Value>
                            </Assign>
                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_2" Line="[commentStatus]" Source="[logfile]" />
                          </Sequence>
                        </If.Then>
                        <If.Else>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_8">
                            <Sequence.Variables>
                              <Variable x:TypeArguments="x:Int32" Name="x" />
                            </Sequence.Variables>
                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_20" Line="[&quot;PO lines for the Purchase order number &quot; + pono + &quot; is extracted.&quot;]" Source="[logfile]" />
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_133">
                              <Assign.To>
                                <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="scg:List(s:String[])">[new list(OF string()) ()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_16">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Int32">[x]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Int32">1</InArgument>
                              </Assign.Value>
                            </Assign>
                            <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_3" Values="[out2(&quot;results&quot;)(0)(&quot;records&quot;)]">
                              <ActivityAction x:TypeArguments="njl:JToken">
                                <ActivityAction.Argument>
                                  <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
                                </ActivityAction.Argument>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_7">
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_6">
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_18">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="s:String[]">[M3Values]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="s:String[]">[New String(6) {}]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_19">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[m3Values(3)]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">[(Convert.toDecimal(item("REPL").ToString().split("~"C)(3).ToString()) - Convert.toDecimal(item("REPL").ToString().split("~"C)(10).ToString())).ToString()]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <If Condition="[CInt(m3Values(3)).ToString &gt; &quot;0&quot;]" sap2010:WorkflowViewState.IdRef="If_4">
                                      <If.Then>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_5">
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_20">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[m3Values(0)]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(0).ToString()]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_21">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[m3Values(1)]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(1).ToString()]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_22">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[m3Values(2)]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(2).ToString()]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_23">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[m3Values(4)]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(4).ToString()]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_24">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[m3Values(5)]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(5).ToString()]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_25">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[SupplierNo]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(9).ToString()]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_27">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[cono]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[item("REPL").ToString().split("~"C)(6).ToString()]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <If Condition="[matchVendorItemCode and invoicesItemNumbers.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_107">
                                            <If.Then>
                                              <If Condition="[invoicesItemNumbers.contains(m3Values(5))]" sap2010:WorkflowViewState.IdRef="If_99">
                                                <If.Then>
                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_107">
                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_106">
                                                      <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_1" MethodName="Add">
                                                        <InvokeMethod.TargetObject>
                                                          <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</InArgument>
                                                        </InvokeMethod.TargetObject>
                                                        <InArgument x:TypeArguments="s:String[]">[m3Values]</InArgument>
                                                      </InvokeMethod>
                                                    </Sequence>
                                                  </Sequence>
                                                </If.Then>
                                              </If>
                                            </If.Then>
                                            <If.Else>
                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_111">
                                                <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_4" MethodName="Add">
                                                  <InvokeMethod.TargetObject>
                                                    <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</InArgument>
                                                  </InvokeMethod.TargetObject>
                                                  <InArgument x:TypeArguments="s:String[]">[m3Values]</InArgument>
                                                </InvokeMethod>
                                              </Sequence>
                                            </If.Else>
                                          </If>
                                        </Sequence>
                                      </If.Then>
                                    </If>
                                  </Sequence>
                                </Sequence>
                              </ActivityAction>
                            </ForEach>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_269">
                              <Assign.To>
                                <OutArgument x:TypeArguments="scg:IEnumerable(sl:IGrouping(x:String, s:String[]))">[transDateGroups]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="scg:IEnumerable(sl:IGrouping(x:String, s:String[]))">[M3TotalTableRows.GroupBy(Function(item) item(4))]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <If Condition="[miscValues(&quot;groupByTransDate&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_269" />
                            <ForEach x:TypeArguments="sl:IGrouping(x:String, s:String[])" DisplayName="ForEach&lt;IGrouping&lt;String,String[]&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_15" Values="[transDateGroups]">
                              <ActivityAction x:TypeArguments="sl:IGrouping(x:String, s:String[])">
                                <ActivityAction.Argument>
                                  <DelegateInArgument x:TypeArguments="sl:IGrouping(x:String, s:String[])" Name="group" />
                                </ActivityAction.Argument>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_125">
                                  <If Condition="[matchVendorItemCode and invoicesItemNumbers.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_119">
                                    <If.Then>
                                      <If Condition="[group.ToList().Count &gt;=  invoicesItemNumbers.Count]" sap2010:WorkflowViewState.IdRef="If_118">
                                        <If.Then>
                                          <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_6" MethodName="Add">
                                            <InvokeMethod.TargetObject>
                                              <InArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(s:String[]))">[transDateDictionary]</InArgument>
                                            </InvokeMethod.TargetObject>
                                            <InArgument x:TypeArguments="x:String">[group.Key]</InArgument>
                                            <InArgument x:TypeArguments="scg:List(s:String[])">[group.ToList]</InArgument>
                                          </InvokeMethod>
                                        </If.Then>
                                      </If>
                                    </If.Then>
                                    <If.Else>
                                      <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_7" MethodName="Add">
                                        <InvokeMethod.TargetObject>
                                          <InArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(s:String[]))">[transDateDictionary]</InArgument>
                                        </InvokeMethod.TargetObject>
                                        <InArgument x:TypeArguments="x:String">[group.Key]</InArgument>
                                        <InArgument x:TypeArguments="scg:List(s:String[])">[group.ToList]</InArgument>
                                      </InvokeMethod>
                                    </If.Else>
                                  </If>
                                </Sequence>
                              </ActivityAction>
                            </ForEach>
                            <If Condition="False" sap2010:WorkflowViewState.IdRef="If_262">
                              <If.Then>
                                <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_13">
                                  <TryCatch.Try>
                                    <If Condition="[transDateDictionary.Count =0]" DisplayName="Not Items matching Invoice Items Count" sap2010:WorkflowViewState.IdRef="If_165">
                                      <If.Then>
                                        <ForEach x:TypeArguments="sl:IGrouping(x:String, s:String[])" DisplayName="ForEach&lt;IGrouping&lt;String,String[]&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_32" Values="[transDateGroups]">
                                          <ActivityAction x:TypeArguments="sl:IGrouping(x:String, s:String[])">
                                            <ActivityAction.Argument>
                                              <DelegateInArgument x:TypeArguments="sl:IGrouping(x:String, s:String[])" Name="group" />
                                            </ActivityAction.Argument>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_179">
                                              <If Condition="[matchVendorItemCode and invoicesItemNumbers.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_169">
                                                <If.Then>
                                                  <If Condition="[group.ToList().Count &gt;=  invoicesItemNumbers.Count]" sap2010:WorkflowViewState.IdRef="If_168">
                                                    <If.Else>
                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_178">
                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_177">
                                                          <Sequence.Variables>
                                                            <Variable x:TypeArguments="x:Decimal" Name="transDateAmount" />
                                                            <Variable x:TypeArguments="x:Decimal" Name="onePercent" />
                                                            <Variable x:TypeArguments="x:Decimal" Name="invoiceAmount" />
                                                          </Sequence.Variables>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_363">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:Decimal">[transDateAmount]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:Decimal">[0]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_31" Values="[group.ToList]">
                                                            <ActivityAction x:TypeArguments="s:String[]">
                                                              <ActivityAction.Argument>
                                                                <DelegateInArgument x:TypeArguments="s:String[]" Name="item" />
                                                              </ActivityAction.Argument>
                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_364">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="x:Decimal">[transDateAmount]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="x:Decimal" xml:space="preserve">[transDateAmount+( Decimal.Parse(item(2)) *  Decimal.Parse(item(3)))]</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                            </ActivityAction>
                                                          </ForEach>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_365">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:Decimal">[onePercent]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:Decimal">[transDateAmount * 0.01D]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_366">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:Decimal">[invoiceAmount]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:Decimal">[Decimal.parse(colAmount)]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                          <If Condition="[onePercent &gt; 10]" sap2010:WorkflowViewState.IdRef="If_166">
                                                            <If.Then>
                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_367">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="x:Decimal">[onePercent]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="x:Decimal">[10]</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                            </If.Then>
                                                          </If>
                                                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_100" Line="[&quot;Trans Date :&quot;+group.Key+&quot; Trans Date amount &quot;+transDateAmount.tostring+&quot; Sub Total&quot;+colAmount]" Source="[logFile]" />
                                                          <If Condition="[invoiceAmount &gt; transDateAmount - onePercent and  invoiceAmount &lt; transDateAmount + onePercent]" sap2010:WorkflowViewState.IdRef="If_167">
                                                            <If.Then>
                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_176">
                                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_101" Line="Partial Invoices Line amount matching with PO received line amount" Source="[logFile]" />
                                                                <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_12" MethodName="Add">
                                                                  <InvokeMethod.TargetObject>
                                                                    <InArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(s:String[]))">[transDateDictionary]</InArgument>
                                                                  </InvokeMethod.TargetObject>
                                                                  <InArgument x:TypeArguments="x:String">[group.Key]</InArgument>
                                                                  <InArgument x:TypeArguments="scg:List(s:String[])">[group.ToList]</InArgument>
                                                                </InvokeMethod>
                                                              </Sequence>
                                                            </If.Then>
                                                          </If>
                                                        </Sequence>
                                                      </Sequence>
                                                    </If.Else>
                                                  </If>
                                                </If.Then>
                                              </If>
                                            </Sequence>
                                          </ActivityAction>
                                        </ForEach>
                                      </If.Then>
                                    </If>
                                  </TryCatch.Try>
                                  <TryCatch.Catches>
                                    <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_13">
                                      <ActivityAction x:TypeArguments="s:Exception">
                                        <ActivityAction.Argument>
                                          <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                        </ActivityAction.Argument>
                                      </ActivityAction>
                                    </Catch>
                                  </TryCatch.Catches>
                                </TryCatch>
                              </If.Then>
                            </If>
                            <If Condition="[transDateDictionary.Count =0]" sap2010:WorkflowViewState.IdRef="If_128">
                              <If.Then>
                                <If Condition="[matchVendorItemCode and invoicesItemNumbers.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_106">
                                  <If.Then>
                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_130">
                                      <Sequence.Variables>
                                        <Variable x:TypeArguments="x:Int32" Name="count" />
                                      </Sequence.Variables>
                                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_64" Line="[&quot;Invoice lines count matches with PO Receipt not invoiced  : &quot;+M3TotalTableRows .Count.tostring]" Source="[logfile]" />
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_284">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:Int32">[count]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:Int32">1</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_10" Values="[invoicesItemNumbers]">
                                        <ActivityAction x:TypeArguments="x:String">
                                          <ActivityAction.Argument>
                                            <DelegateInArgument x:TypeArguments="x:String" Name="itemCode" />
                                          </ActivityAction.Argument>
                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_105">
                                            <Sequence.Variables>
                                              <Variable x:TypeArguments="x:Boolean" Name="itemCodeAvailable" />
                                              <Variable x:TypeArguments="x:String" Name="receivedDate" />
                                            </Sequence.Variables>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_241">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:Boolean">[itemCodeAvailable]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_280">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[receivedDate]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">
                                                  <Literal x:TypeArguments="x:String" Value="" />
                                                </InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_26" Values="[M3TotalTableRows]">
                                              <ActivityAction x:TypeArguments="s:String[]">
                                                <ActivityAction.Argument>
                                                  <DelegateInArgument x:TypeArguments="s:String[]" Name="recItem" />
                                                </ActivityAction.Argument>
                                                <If Condition="[recItem(5) = itemCode]" sap2010:WorkflowViewState.IdRef="If_98">
                                                  <If.Then>
                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_131">
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_242">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:Boolean">[itemCodeAvailable]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_281">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[receivedDate]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">[recItem(4)]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_72" Line="[Count.tostring+&quot;)&quot;+itemCode+&quot; is available in PO and Invoice. Received Date : &quot;+receivedDate]" Source="[logFile]" />
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_285">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:Int32">[count]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:Int32">[count+1]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                    </Sequence>
                                                  </If.Then>
                                                </If>
                                              </ActivityAction>
                                            </ForEach>
                                            <If Condition="[not itemCodeAvailable]" sap2010:WorkflowViewState.IdRef="If_101">
                                              <If.Then>
                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_136">
                                                  <Sequence.Variables>
                                                    <Variable x:TypeArguments="x:String" Name="variable2" />
                                                  </Sequence.Variables>
                                                  <If Condition="[notReceivedItems.Contains(itemCode)]" sap2010:WorkflowViewState.IdRef="If_125">
                                                    <If.Then>
                                                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_73" Line="[Count.tostring+&quot;)&quot;+itemCode+&quot; is not received&quot;]" Source="[logFile]" />
                                                    </If.Then>
                                                    <If.Else>
                                                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_74" Line="[Count.tostring+&quot;)&quot;+itemCode+&quot; is already invoiced&quot;]" Source="[logFile]" />
                                                    </If.Else>
                                                  </If>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_286">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:Int32">[count]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:Int32">[count+1]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                </Sequence>
                                              </If.Then>
                                            </If>
                                          </Sequence>
                                        </ActivityAction>
                                      </ForEach>
                                    </Sequence>
                                  </If.Then>
                                </If>
                              </If.Then>
                            </If>
                            <If Condition="[M3TotalTableRows.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_108">
                              <If.Then>
                                <If Condition="[transDateDictionary.Count&gt;0]" sap2010:WorkflowViewState.IdRef="If_10">
                                  <If.Then>
                                    <Sequence DisplayName="Trans Date For Loop" sap2010:WorkflowViewState.IdRef="Sequence_126">
                                      <Sequence.Variables>
                                        <Variable x:TypeArguments="x:Boolean" Name="optimizerSuccess" />
                                      </Sequence.Variables>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_272">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:Boolean">[optimizerSuccess]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <ForEach x:TypeArguments="scg:KeyValuePair(x:String, scg:List(s:String[]))" DisplayName="ForEach&lt;KeyValuePair&lt;String,List&lt;String[]&gt;&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_17" Values="[transDateDictionary]">
                                        <ActivityAction x:TypeArguments="scg:KeyValuePair(x:String, scg:List(s:String[]))">
                                          <ActivityAction.Argument>
                                            <DelegateInArgument x:TypeArguments="scg:KeyValuePair(x:String, scg:List(s:String[]))" Name="transDateItem" />
                                          </ActivityAction.Argument>
                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_127">
                                            <If Condition="[not optimizerSuccess]" sap2010:WorkflowViewState.IdRef="If_120">
                                              <If.Then>
                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_18">
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_278">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[APIString]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">
                                                        <Literal x:TypeArguments="x:String" Value="" />
                                                      </InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_273">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:Int32">[x]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:Int32">1</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_19" Values="[transDateItem.Value]">
                                                    <ActivityAction x:TypeArguments="s:String[]">
                                                      <ActivityAction.Argument>
                                                        <DelegateInArgument x:TypeArguments="s:String[]" Name="m3Values" />
                                                      </ActivityAction.Argument>
                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_133">
                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_132">
                                                          <If Condition="[m3Values(3).Contains(&quot;.&quot;)]" sap2010:WorkflowViewState.IdRef="If_121">
                                                            <If.Then>
                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_274">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="x:String">[m3Values(3)]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="x:String">[m3Values(3).SubString(0,m3Values(3).IndexOf("."))]</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                            </If.Then>
                                                          </If>
                                                          <If Condition="[x=1]" sap2010:WorkflowViewState.IdRef="If_123">
                                                            <If.Then>
                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_128">
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_275">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:String">[APIString]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:String">["[["+m3Values(0)+","+m3Values(1)+","+m3Values(2)+","+m3Values(3)+","+colAmount+",1]"]</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                              </Sequence>
                                                            </If.Then>
                                                            <If.Else>
                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_129">
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_277">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:String">[APIString]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:String">[APIString+",["+m3Values(0)+","+m3Values(1)+","+m3Values(2)+","+m3Values(3)+","+colAmount+",1]"]</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                              </Sequence>
                                                            </If.Else>
                                                          </If>
                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_282">
                                                            <Assign.To>
                                                              <OutArgument x:TypeArguments="x:Int32">[x]</OutArgument>
                                                            </Assign.To>
                                                            <Assign.Value>
                                                              <InArgument x:TypeArguments="x:Int32">[x+1]</InArgument>
                                                            </Assign.Value>
                                                          </Assign>
                                                        </Sequence>
                                                      </Sequence>
                                                    </ActivityAction>
                                                  </ForEach>
                                                  <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;ocrValues&quot;,ocrValues},{&quot;colemanAPI&quot;,colemanAPI},{&quot;APIString&quot;,APIString},{&quot;logfile&quot;,logfile}}]" ContinueOnError="True" DisplayName="Coleman Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_1" OutputArguments="[ColemanOutPut]" ResponseCode="[ColemanRespCode]" WorkflowFile="[projectPath+&quot;\CallColeman.xaml&quot;]" />
                                                  <If Condition="[ColemanRespCode = 200]" sap2010:WorkflowViewState.IdRef="If_12">
                                                    <If.Then>
                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_19">
                                                        <Sequence.Variables>
                                                          <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="sendApprovalResponseDictionary" />
                                                          <Variable x:TypeArguments="x:String" Name="vat" />
                                                        </Sequence.Variables>
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_53">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:String">[ColemanOutPut("comments").ToString()]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_54">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:String">[ColemanOutPut("Status").ToString()]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <If Condition="[Status = &quot;Success&quot;]" sap2010:WorkflowViewState.IdRef="If_130">
                                                          <If.Then>
                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_141">
                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_52">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="njl:JToken">[httpOut]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(ColemanOutPut("httpOut").ToString)]</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                              <If Condition="[NOT httpOut.ToString.Contains(&quot;Not Allocated&quot;)]" sap2010:WorkflowViewState.IdRef="If_162">
                                                                <If.Then>
                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_171">
                                                                    <Sequence.Variables>
                                                                      <Variable x:TypeArguments="njl:JToken" Name="out5" />
                                                                    </Sequence.Variables>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_296">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:Boolean">[optimizerSuccess]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_297">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[inbnValue]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">
                                                                          <Literal x:TypeArguments="x:String" Value="" />
                                                                        </InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_298">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[vat]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">
                                                                          <Literal x:TypeArguments="x:String" Value="" />
                                                                        </InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_170">
                                                                      <Sequence.Variables>
                                                                        <Variable x:TypeArguments="x:String" Name="SupAcho" />
                                                                        <Variable x:TypeArguments="x:Int32" Name="StatusCode4" />
                                                                        <Variable x:TypeArguments="iru:ResponseObject" Name="respObj4" />
                                                                        <Variable x:TypeArguments="njl:JToken" Name="out4" />
                                                                        <Variable x:TypeArguments="x:String" Name="ivdate" />
                                                                        <Variable x:TypeArguments="x:String" Name="sino" />
                                                                        <Variable x:TypeArguments="x:String" Name="cuam" />
                                                                        <Variable x:TypeArguments="x:Int32" Name="StatusCode5" />
                                                                      </Sequence.Variables>
                                                                      <Assign DisplayName="Assign SupAcho" sap2010:WorkflowViewState.IdRef="Assign_299">
                                                                        <Assign.To>
                                                                          <OutArgument x:TypeArguments="x:String">[SupAcho]</OutArgument>
                                                                        </Assign.To>
                                                                        <Assign.Value>
                                                                          <InArgument x:TypeArguments="x:String">["ACHO:"+supplierNo]</InArgument>
                                                                        </Assign.Value>
                                                                      </Assign>
                                                                      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for bkid" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_32" Response="[respObj4]" StatusCode="[StatusCode4]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/CRS692MI/SearchBnkAcc&quot;]">
                                                                        <iai:IONAPIRequestWizard.QueryParameters>
                                                                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                            <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                              <x:String>SQRY</x:String>
                                                                              <x:String>dateformat</x:String>
                                                                              <x:String>excludeempty</x:String>
                                                                              <x:String>righttrim</x:String>
                                                                              <x:String>format</x:String>
                                                                              <x:String>extendedresult</x:String>
                                                                            </scg:List>
                                                                            <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                              <x:String>SupAcho</x:String>
                                                                              <x:String>YMD8</x:String>
                                                                              <x:String>false</x:String>
                                                                              <x:String>true</x:String>
                                                                              <x:String>PRETTY</x:String>
                                                                              <x:String>false</x:String>
                                                                            </scg:List>
                                                                          </scg:List>
                                                                        </iai:IONAPIRequestWizard.QueryParameters>
                                                                      </iai:IONAPIRequestWizard>
                                                                      <If Condition="[StatusCode4 = 200]" sap2010:WorkflowViewState.IdRef="If_161">
                                                                        <If.Then>
                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_168">
                                                                            <Sequence.Variables>
                                                                              <Variable x:TypeArguments="x:String" Name="bkid" />
                                                                              <Variable x:TypeArguments="iru:ResponseObject" Name="respObj5" />
                                                                            </Sequence.Variables>
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_300">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="njl:JToken">[out4]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj4.ReadAsText)]</InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                            <If Condition="[out4(&quot;results&quot;)(0)(&quot;records&quot;).ToString &lt;&gt; &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_131">
                                                                              <If.Then>
                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_142">
                                                                                  <Assign DisplayName="Assign bkid" sap2010:WorkflowViewState.IdRef="Assign_301">
                                                                                    <Assign.To>
                                                                                      <OutArgument x:TypeArguments="x:String">[bkid]</OutArgument>
                                                                                    </Assign.To>
                                                                                    <Assign.Value>
                                                                                      <InArgument x:TypeArguments="x:String">[(out4("results")(0)("records")(0)("BKID")).ToString]</InArgument>
                                                                                    </Assign.Value>
                                                                                  </Assign>
                                                                                </Sequence>
                                                                              </If.Then>
                                                                              <If.Else>
                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_143">
                                                                                  <Assign DisplayName="Assign bkid" sap2010:WorkflowViewState.IdRef="Assign_302">
                                                                                    <Assign.To>
                                                                                      <OutArgument x:TypeArguments="x:String">[bkid]</OutArgument>
                                                                                    </Assign.To>
                                                                                    <Assign.Value>
                                                                                      <InArgument x:TypeArguments="x:String">
                                                                                        <Literal x:TypeArguments="x:String" Value="" />
                                                                                      </InArgument>
                                                                                    </Assign.Value>
                                                                                  </Assign>
                                                                                </Sequence>
                                                                              </If.Else>
                                                                            </If>
                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_167">
                                                                              <Sequence.Variables>
                                                                                <Variable x:TypeArguments="x:Boolean" Name="InvoiceAlreadyExists" />
                                                                              </Sequence.Variables>
                                                                              <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_14">
                                                                                <TryCatch.Try>
                                                                                  <Assign DisplayName="Assign ivdate" sap2010:WorkflowViewState.IdRef="Assign_303">
                                                                                    <Assign.To>
                                                                                      <OutArgument x:TypeArguments="x:String">[ivdate]</OutArgument>
                                                                                    </Assign.To>
                                                                                    <Assign.Value>
                                                                                      <InArgument x:TypeArguments="x:String">[DateTime.ParseExact(ocrValues(1), "dd/MM/yyyy", Nothing).ToString("yyyyMMdd")]</InArgument>
                                                                                    </Assign.Value>
                                                                                  </Assign>
                                                                                </TryCatch.Try>
                                                                                <TryCatch.Catches>
                                                                                  <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_14">
                                                                                    <ActivityAction x:TypeArguments="s:Exception">
                                                                                      <ActivityAction.Argument>
                                                                                        <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                      </ActivityAction.Argument>
                                                                                    </ActivityAction>
                                                                                  </Catch>
                                                                                </TryCatch.Catches>
                                                                              </TryCatch>
                                                                              <Assign DisplayName="Assign sino" sap2010:WorkflowViewState.IdRef="Assign_304">
                                                                                <Assign.To>
                                                                                  <OutArgument x:TypeArguments="x:String">[sino]</OutArgument>
                                                                                </Assign.To>
                                                                                <Assign.Value>
                                                                                  <InArgument x:TypeArguments="x:String">[ocrValues(0)]</InArgument>
                                                                                </Assign.Value>
                                                                              </Assign>
                                                                              <Assign DisplayName="Assign cuam" sap2010:WorkflowViewState.IdRef="Assign_305">
                                                                                <Assign.To>
                                                                                  <OutArgument x:TypeArguments="x:String">[cuam]</OutArgument>
                                                                                </Assign.To>
                                                                                <Assign.Value>
                                                                                  <InArgument x:TypeArguments="x:String">[ocrValues(2)]</InArgument>
                                                                                </Assign.Value>
                                                                              </Assign>
                                                                              <Assign DisplayName="Assign cuam" sap2010:WorkflowViewState.IdRef="Assign_306">
                                                                                <Assign.To>
                                                                                  <OutArgument x:TypeArguments="x:String">[cuam]</OutArgument>
                                                                                </Assign.To>
                                                                                <Assign.Value>
                                                                                  <InArgument x:TypeArguments="x:String">[cuam.Replace(",","")]</InArgument>
                                                                                </Assign.Value>
                                                                              </Assign>
                                                                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_33" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                                                                <iai:IONAPIRequestWizard.QueryParameters>
                                                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                    <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                                      <x:String>SUNO</x:String>
                                                                                      <x:String>IVDT</x:String>
                                                                                      <x:String>DIVI</x:String>
                                                                                      <x:String>SINO</x:String>
                                                                                      <x:String>CUCD</x:String>
                                                                                      <x:String>TEPY</x:String>
                                                                                      <x:String>PYME</x:String>
                                                                                      <x:String>CUAM</x:String>
                                                                                      <x:String>IMCD</x:String>
                                                                                      <x:String>CRTP</x:String>
                                                                                      <x:String>dateformat</x:String>
                                                                                      <x:String>excludeempty</x:String>
                                                                                      <x:String>righttrim</x:String>
                                                                                      <x:String>format</x:String>
                                                                                      <x:String>extendedresult</x:String>
                                                                                      <x:String>APCD</x:String>
                                                                                    </scg:List>
                                                                                    <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                                      <x:String>SupplierNo</x:String>
                                                                                      <x:String>ivdate</x:String>
                                                                                      <x:String>division</x:String>
                                                                                      <x:String>sino</x:String>
                                                                                      <x:String>cucd</x:String>
                                                                                      <x:String>tepy</x:String>
                                                                                      <x:String>pyme</x:String>
                                                                                      <x:String>cuam</x:String>
                                                                                      <x:String>1</x:String>
                                                                                      <x:String>1</x:String>
                                                                                      <x:String>YMD8</x:String>
                                                                                      <x:String>false</x:String>
                                                                                      <x:String>true</x:String>
                                                                                      <x:String>PRETTY</x:String>
                                                                                      <x:String>false</x:String>
                                                                                      <x:String>authUser</x:String>
                                                                                    </scg:List>
                                                                                  </scg:List>
                                                                                </iai:IONAPIRequestWizard.QueryParameters>
                                                                              </iai:IONAPIRequestWizard>
                                                                              <If Condition="[StatusCode5 = 200  and respObj5.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" sap2010:WorkflowViewState.IdRef="If_132">
                                                                                <If.Then>
                                                                                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_34" Response="[respObj5]" StatusCode="[StatusCode5]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                                                                    <iai:IONAPIRequestWizard.QueryParameters>
                                                                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                        <scg:List x:TypeArguments="x:String" Capacity="32">
                                                                                          <x:String>SUNO</x:String>
                                                                                          <x:String>IVDT</x:String>
                                                                                          <x:String>DIVI</x:String>
                                                                                          <x:String>SINO</x:String>
                                                                                          <x:String>CUCD</x:String>
                                                                                          <x:String>TEPY</x:String>
                                                                                          <x:String>PYME</x:String>
                                                                                          <x:String>CUAM</x:String>
                                                                                          <x:String>IMCD</x:String>
                                                                                          <x:String>CRTP</x:String>
                                                                                          <x:String>dateformat</x:String>
                                                                                          <x:String>excludeempty</x:String>
                                                                                          <x:String>righttrim</x:String>
                                                                                          <x:String>format</x:String>
                                                                                          <x:String>extendedresult</x:String>
                                                                                          <x:String>APCD</x:String>
                                                                                          <x:String>BKID</x:String>
                                                                                        </scg:List>
                                                                                        <scg:List x:TypeArguments="x:String" Capacity="32">
                                                                                          <x:String>SupplierNo</x:String>
                                                                                          <x:String>ivdate</x:String>
                                                                                          <x:String>division</x:String>
                                                                                          <x:String>sino</x:String>
                                                                                          <x:String>cucd</x:String>
                                                                                          <x:String>tepy</x:String>
                                                                                          <x:String>pyme</x:String>
                                                                                          <x:String>cuam</x:String>
                                                                                          <x:String>1</x:String>
                                                                                          <x:String>1</x:String>
                                                                                          <x:String>YMD8</x:String>
                                                                                          <x:String>false</x:String>
                                                                                          <x:String>true</x:String>
                                                                                          <x:String>PRETTY</x:String>
                                                                                          <x:String>false</x:String>
                                                                                          <x:String>authUser</x:String>
                                                                                          <x:String>bkid</x:String>
                                                                                        </scg:List>
                                                                                      </scg:List>
                                                                                    </iai:IONAPIRequestWizard.QueryParameters>
                                                                                  </iai:IONAPIRequestWizard>
                                                                                </If.Then>
                                                                              </If>
                                                                              <If Condition="[StatusCode5 = 200]" sap2010:WorkflowViewState.IdRef="If_160">
                                                                                <If.Then>
                                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_165">
                                                                                    <Sequence.Variables>
                                                                                      <Variable x:TypeArguments="x:Int32" Name="respout1" />
                                                                                      <Variable x:TypeArguments="x:String" Name="additionalChargeExcp" />
                                                                                      <Variable x:TypeArguments="x:Int32" Name="m" />
                                                                                    </Sequence.Variables>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_307">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="njl:JToken">[out5]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj5.ReadAsText)]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <If Condition="[out5(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_133">
                                                                                      <If.Then>
                                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_144">
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_308">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:Boolean">[InvoiceAlreadyExists]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_309">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:String">[(out5("results")(0)("errorMessage")).ToString]</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_310">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                        </Sequence>
                                                                                      </If.Then>
                                                                                      <If.Else>
                                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_145">
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_311">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:Boolean">[InvoiceAlreadyExists]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_312">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:String">[inbnValue]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:String">[(out5("results")(0)("records")(0)("INBN")).ToString]</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_313">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:String">["Invoice Header Created"]</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                        </Sequence>
                                                                                      </If.Else>
                                                                                    </If>
                                                                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_79" Line="[commentStatus]" Source="[logfile]" />
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_314">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:Int32">[m]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:Int32">0</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <If Condition="[NOT InvoiceAlreadyExists]" sap2010:WorkflowViewState.IdRef="If_155">
                                                                                      <If.Then>
                                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_162">
                                                                                          <Sequence.Variables>
                                                                                            <Variable x:TypeArguments="njl:JToken" Name="out5" />
                                                                                            <Variable x:TypeArguments="x:Int32" Name="respout" />
                                                                                            <Variable x:TypeArguments="scg:List(x:String)" Name="vatList" />
                                                                                            <Variable x:TypeArguments="x:String" Name="TaxCode" />
                                                                                            <Variable x:TypeArguments="x:String" Name="charge" />
                                                                                            <Variable x:TypeArguments="x:String" Name="discount" />
                                                                                            <Variable x:TypeArguments="x:String" Name="vatCode" />
                                                                                          </Sequence.Variables>
                                                                                          <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_28" Values="[httpOut]">
                                                                                            <ActivityAction x:TypeArguments="njl:JToken">
                                                                                              <ActivityAction.Argument>
                                                                                                <DelegateInArgument x:TypeArguments="njl:JToken" Name="out" />
                                                                                              </ActivityAction.Argument>
                                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_152">
                                                                                                <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_27" Values="[transDateItem.Value]">
                                                                                                  <ActivityAction x:TypeArguments="s:String[]">
                                                                                                    <ActivityAction.Argument>
                                                                                                      <DelegateInArgument x:TypeArguments="s:String[]" Name="rows" />
                                                                                                    </ActivityAction.Argument>
                                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_151">
                                                                                                      <If Condition="[out(&quot;line_number&quot;).ToString=rows(0) And out(&quot;rcd_number&quot;).ToString=rows(1)]" sap2010:WorkflowViewState.IdRef="If_139">
                                                                                                        <If.Then>
                                                                                                          <Sequence DisplayName="Add lines Sequence" sap2010:WorkflowViewState.IdRef="Sequence_150">
                                                                                                            <Sequence.Variables>
                                                                                                              <Variable x:TypeArguments="x:String" Name="puno" />
                                                                                                              <Variable x:TypeArguments="x:String" Name="inbn" />
                                                                                                              <Variable x:TypeArguments="x:String" Name="itno" />
                                                                                                              <Variable x:TypeArguments="x:String" Name="ppun" />
                                                                                                              <Variable x:TypeArguments="x:String" Name="puun" />
                                                                                                              <Variable x:TypeArguments="njl:JToken" Name="out4" />
                                                                                                              <Variable x:TypeArguments="njl:JToken" Name="out3" />
                                                                                                              <Variable x:TypeArguments="x:String" Name="repn" />
                                                                                                              <Variable x:TypeArguments="x:String" Name="pnli" />
                                                                                                              <Variable x:TypeArguments="x:String" Name="grpr" />
                                                                                                              <Variable x:TypeArguments="njl:JToken" Name="out7" />
                                                                                                              <Variable x:TypeArguments="iru:ResponseObject" Name="respObj7" />
                                                                                                              <Variable x:TypeArguments="iru:ResponseObject" Name="respObj4" />
                                                                                                              <Variable x:TypeArguments="x:String" Name="ivqa" />
                                                                                                              <Variable x:TypeArguments="x:Int32" Name="StatusCode6" />
                                                                                                              <Variable x:TypeArguments="iru:ResponseObject" Name="RespObj6" />
                                                                                                              <Variable x:TypeArguments="njl:JToken" Name="out6" />
                                                                                                              <Variable x:TypeArguments="x:Int32" Name="StatusCode7" />
                                                                                                            </Sequence.Variables>
                                                                                                            <Assign DisplayName="Assign inbn" sap2010:WorkflowViewState.IdRef="Assign_315">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:String">[inbn]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:String">[inbnValue]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_316">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="scg:List(x:String)">[vatList]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)()]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_317">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:String">[puno]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:String">[ocrValues(4)]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_318">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:String">[pnli]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:String">[rows(0)]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <Assign DisplayName="Assign itno" sap2010:WorkflowViewState.IdRef="Assign_319">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:String">[itno]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:String">[rows(5)]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_320">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:String">[repn]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:String">[rows(1)]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_321">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:String">[ivqa]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:String">[rows(3)]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_322">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:String">[grpr]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:String">[rows(2)]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for ppun, puun" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_35" Response="[respObj7]" StatusCode="[StatusCode7]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/MMS200MI/GetItmBasic&quot;]">
                                                                                                              <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                  <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                                                    <x:String>ITNO</x:String>
                                                                                                                    <x:String>dateformat</x:String>
                                                                                                                    <x:String>excludeempty</x:String>
                                                                                                                    <x:String>righttrim</x:String>
                                                                                                                    <x:String>format</x:String>
                                                                                                                    <x:String>extendedresult</x:String>
                                                                                                                  </scg:List>
                                                                                                                  <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                                                    <x:String>itno</x:String>
                                                                                                                    <x:String>YMD8</x:String>
                                                                                                                    <x:String>false</x:String>
                                                                                                                    <x:String>true</x:String>
                                                                                                                    <x:String>PRETTY</x:String>
                                                                                                                    <x:String>false</x:String>
                                                                                                                  </scg:List>
                                                                                                                </scg:List>
                                                                                                              </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                            </iai:IONAPIRequestWizard>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_323">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="njl:JToken">[out7]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj7.ReadAsText)]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <If Condition="[out7(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_138">
                                                                                                              <If.Then>
                                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_146">
                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_324">
                                                                                                                    <Assign.To>
                                                                                                                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                                    </Assign.To>
                                                                                                                    <Assign.Value>
                                                                                                                      <InArgument x:TypeArguments="x:String">["Purchase price UOM and purchase order UOM are not available."]</InArgument>
                                                                                                                    </Assign.Value>
                                                                                                                  </Assign>
                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_325">
                                                                                                                    <Assign.To>
                                                                                                                      <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                                                    </Assign.To>
                                                                                                                    <Assign.Value>
                                                                                                                      <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                                                                    </Assign.Value>
                                                                                                                  </Assign>
                                                                                                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_80" Line="[commentStatus]" Source="[logfile]" />
                                                                                                                </Sequence>
                                                                                                              </If.Then>
                                                                                                              <If.Else>
                                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_149">
                                                                                                                  <Sequence.Variables>
                                                                                                                    <Variable x:TypeArguments="njl:JToken" Name="out8" />
                                                                                                                    <Variable x:TypeArguments="x:Int32" Name="StatusCode8" />
                                                                                                                    <Variable x:TypeArguments="iru:ResponseObject" Name="respObj8" />
                                                                                                                  </Sequence.Variables>
                                                                                                                  <Assign DisplayName="Assign ppun" sap2010:WorkflowViewState.IdRef="Assign_326">
                                                                                                                    <Assign.To>
                                                                                                                      <OutArgument x:TypeArguments="x:String">[ppun]</OutArgument>
                                                                                                                    </Assign.To>
                                                                                                                    <Assign.Value>
                                                                                                                      <InArgument x:TypeArguments="x:String">[out7("results")(0)("records")(0)("PPUN").ToString]</InArgument>
                                                                                                                    </Assign.Value>
                                                                                                                  </Assign>
                                                                                                                  <Assign DisplayName="Assign puun" sap2010:WorkflowViewState.IdRef="Assign_327">
                                                                                                                    <Assign.To>
                                                                                                                      <OutArgument x:TypeArguments="x:String">[puun]</OutArgument>
                                                                                                                    </Assign.To>
                                                                                                                    <Assign.Value>
                                                                                                                      <InArgument x:TypeArguments="x:String">[out7("results")(0)("records")(0)("PUUN").ToString]</InArgument>
                                                                                                                    </Assign.Value>
                                                                                                                  </Assign>
                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_328">
                                                                                                                    <Assign.To>
                                                                                                                      <OutArgument x:TypeArguments="x:String">[puno]</OutArgument>
                                                                                                                    </Assign.To>
                                                                                                                    <Assign.Value>
                                                                                                                      <InArgument x:TypeArguments="x:String">[ocrValues(4)]</InArgument>
                                                                                                                    </Assign.Value>
                                                                                                                  </Assign>
                                                                                                                  <If Condition="[m=0 AND vatCodeConfig = &quot;LINE&quot;]" sap2010:WorkflowViewState.IdRef="If_136">
                                                                                                                    <If.Then>
                                                                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_147">
                                                                                                                        <Sequence.Variables>
                                                                                                                          <Variable x:TypeArguments="iru:ResponseObject" Name="vatResp" />
                                                                                                                          <Variable x:TypeArguments="njl:JToken" Name="vatOut" />
                                                                                                                        </Sequence.Variables>
                                                                                                                        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_36" Response="[vatResp]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/PPS200MI/GetLine?dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
                                                                                                                          <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                                <x:String>PNLI</x:String>
                                                                                                                                <x:String>PUNO</x:String>
                                                                                                                              </scg:List>
                                                                                                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                                <x:String>pnli</x:String>
                                                                                                                                <x:String>puno</x:String>
                                                                                                                              </scg:List>
                                                                                                                            </scg:List>
                                                                                                                          </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                        </iai:IONAPIRequestWizard>
                                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_329">
                                                                                                                          <Assign.To>
                                                                                                                            <OutArgument x:TypeArguments="njl:JToken">[vatOut]</OutArgument>
                                                                                                                          </Assign.To>
                                                                                                                          <Assign.Value>
                                                                                                                            <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(vatResp.ReadAsText)]</InArgument>
                                                                                                                          </Assign.Value>
                                                                                                                        </Assign>
                                                                                                                        <If Condition="[vatOut(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_135">
                                                                                                                          <If.Else>
                                                                                                                            <If Condition="[vatOut(&quot;results&quot;)(0)(&quot;records&quot;)(0)(&quot;VTCD&quot;).ToString &lt;&gt; &quot;&quot; AND vatOut(&quot;results&quot;)(0)(&quot;records&quot;)(0)(&quot;VTCD&quot;).ToString &lt;&gt; &quot;0&quot;]" sap2010:WorkflowViewState.IdRef="If_134">
                                                                                                                              <If.Then>
                                                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_330">
                                                                                                                                  <Assign.To>
                                                                                                                                    <OutArgument x:TypeArguments="x:String">[vatCode]</OutArgument>
                                                                                                                                  </Assign.To>
                                                                                                                                  <Assign.Value>
                                                                                                                                    <InArgument x:TypeArguments="x:String">[vatOut("results")(0)("records")(0)("VTCD").ToString]</InArgument>
                                                                                                                                  </Assign.Value>
                                                                                                                                </Assign>
                                                                                                                              </If.Then>
                                                                                                                            </If>
                                                                                                                          </If.Else>
                                                                                                                        </If>
                                                                                                                      </Sequence>
                                                                                                                    </If.Then>
                                                                                                                  </If>
                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_331">
                                                                                                                    <Assign.To>
                                                                                                                      <OutArgument x:TypeArguments="x:Int32">[m]</OutArgument>
                                                                                                                    </Assign.To>
                                                                                                                    <Assign.Value>
                                                                                                                      <InArgument x:TypeArguments="x:Int32">[m+1]</InArgument>
                                                                                                                    </Assign.Value>
                                                                                                                  </Assign>
                                                                                                                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_37" Response="[respObj8]" StatusCode="[StatusCode8]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                                                                                    <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                        <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                                                                          <x:String>INBN</x:String>
                                                                                                                          <x:String>RDTP</x:String>
                                                                                                                          <x:String>DIVI</x:String>
                                                                                                                          <x:String>PPUN</x:String>
                                                                                                                          <x:String>PUUN</x:String>
                                                                                                                          <x:String>GRPR</x:String>
                                                                                                                          <x:String>ITNO</x:String>
                                                                                                                          <x:String>IVQA</x:String>
                                                                                                                          <x:String>PNLI</x:String>
                                                                                                                          <x:String>PUNO</x:String>
                                                                                                                          <x:String>RELP</x:String>
                                                                                                                          <x:String>REPN</x:String>
                                                                                                                        </scg:List>
                                                                                                                        <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                                                                          <x:String>inbn</x:String>
                                                                                                                          <x:String>1</x:String>
                                                                                                                          <x:String>division</x:String>
                                                                                                                          <x:String>ppun</x:String>
                                                                                                                          <x:String>puun</x:String>
                                                                                                                          <x:String>grpr</x:String>
                                                                                                                          <x:String>itno</x:String>
                                                                                                                          <x:String>ivqa</x:String>
                                                                                                                          <x:String>pnli</x:String>
                                                                                                                          <x:String>puno</x:String>
                                                                                                                          <x:String>1</x:String>
                                                                                                                          <x:String>repn</x:String>
                                                                                                                        </scg:List>
                                                                                                                      </scg:List>
                                                                                                                    </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                  </iai:IONAPIRequestWizard>
                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_332">
                                                                                                                    <Assign.To>
                                                                                                                      <OutArgument x:TypeArguments="njl:JToken">[out8]</OutArgument>
                                                                                                                    </Assign.To>
                                                                                                                    <Assign.Value>
                                                                                                                      <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj8.ReadAsText)]</InArgument>
                                                                                                                    </Assign.Value>
                                                                                                                  </Assign>
                                                                                                                  <If Condition="[out8(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_137">
                                                                                                                    <If.Else>
                                                                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_148">
                                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_333">
                                                                                                                          <Assign.To>
                                                                                                                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                                          </Assign.To>
                                                                                                                          <Assign.Value>
                                                                                                                            <InArgument x:TypeArguments="x:String">Invoice Line has been created</InArgument>
                                                                                                                          </Assign.Value>
                                                                                                                        </Assign>
                                                                                                                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_81" Line="[commentStatus]" Source="[logfile]" />
                                                                                                                      </Sequence>
                                                                                                                    </If.Else>
                                                                                                                  </If>
                                                                                                                </Sequence>
                                                                                                              </If.Else>
                                                                                                            </If>
                                                                                                          </Sequence>
                                                                                                        </If.Then>
                                                                                                      </If>
                                                                                                    </Sequence>
                                                                                                  </ActivityAction>
                                                                                                </ForEach>
                                                                                              </Sequence>
                                                                                            </ActivityAction>
                                                                                          </ForEach>
                                                                                          <If Condition="[ocrValues(5) &lt;&gt; &quot;&quot;]" DisplayName="If TAX" sap2010:WorkflowViewState.IdRef="If_146">
                                                                                            <If.Then>
                                                                                              <If Condition="[CInt(ocrValues(5)).ToString &lt;&gt; &quot;0&quot;]" sap2010:WorkflowViewState.IdRef="If_145">
                                                                                                <If.Then>
                                                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_155">
                                                                                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_82" Line="Tax line available" Source="[logfile]" />
                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_334">
                                                                                                      <Assign.To>
                                                                                                        <OutArgument x:TypeArguments="x:String">[vat]</OutArgument>
                                                                                                      </Assign.To>
                                                                                                      <Assign.Value>
                                                                                                        <InArgument x:TypeArguments="x:String">[ocrValues(5)]</InArgument>
                                                                                                      </Assign.Value>
                                                                                                    </Assign>
                                                                                                    <If Condition="[vatCodeConfig = &quot;NA&quot;]" sap2010:WorkflowViewState.IdRef="If_142">
                                                                                                      <If.Then>
                                                                                                        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_38" Response="[respObj5]" StatusCode="[respout]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                                                                          <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                <x:String>INBN</x:String>
                                                                                                                <x:String>RDTP</x:String>
                                                                                                                <x:String>DIVI</x:String>
                                                                                                                <x:String>GLAM</x:String>
                                                                                                              </scg:List>
                                                                                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                <x:String>inbnValue</x:String>
                                                                                                                <x:String>3</x:String>
                                                                                                                <x:String>division</x:String>
                                                                                                                <x:String>vat</x:String>
                                                                                                              </scg:List>
                                                                                                            </scg:List>
                                                                                                          </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                        </iai:IONAPIRequestWizard>
                                                                                                      </If.Then>
                                                                                                      <If.Else>
                                                                                                        <If Condition="[vatCodeConfig &lt;&gt; &quot;NA&quot; AND vatCodeConfig &lt;&gt; &quot;LINE&quot;]" sap2010:WorkflowViewState.IdRef="If_141">
                                                                                                          <If.Then>
                                                                                                            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_39" Response="[respObj5]" StatusCode="[respout]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                                                                              <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                  <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                                                    <x:String>INBN</x:String>
                                                                                                                    <x:String>RDTP</x:String>
                                                                                                                    <x:String>DIVI</x:String>
                                                                                                                    <x:String>VTA1</x:String>
                                                                                                                    <x:String>VTCD</x:String>
                                                                                                                  </scg:List>
                                                                                                                  <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                                                    <x:String>inbnValue</x:String>
                                                                                                                    <x:String>3</x:String>
                                                                                                                    <x:String>division</x:String>
                                                                                                                    <x:String>vat</x:String>
                                                                                                                    <x:String>vatCodeConfig</x:String>
                                                                                                                  </scg:List>
                                                                                                                </scg:List>
                                                                                                              </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                            </iai:IONAPIRequestWizard>
                                                                                                          </If.Then>
                                                                                                          <If.Else>
                                                                                                            <If Condition="[vatCodeConfig = &quot;LINE&quot;]" sap2010:WorkflowViewState.IdRef="If_140">
                                                                                                              <If.Then>
                                                                                                                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_40" Response="[respObj5]" StatusCode="[respout]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                                                                                  <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                      <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                                                        <x:String>INBN</x:String>
                                                                                                                        <x:String>RDTP</x:String>
                                                                                                                        <x:String>DIVI</x:String>
                                                                                                                        <x:String>VTA1</x:String>
                                                                                                                        <x:String>VTCD</x:String>
                                                                                                                      </scg:List>
                                                                                                                      <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                                                        <x:String>inbnValue</x:String>
                                                                                                                        <x:String>3</x:String>
                                                                                                                        <x:String>division</x:String>
                                                                                                                        <x:String>vat</x:String>
                                                                                                                        <x:String>vatCode</x:String>
                                                                                                                      </scg:List>
                                                                                                                    </scg:List>
                                                                                                                  </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                </iai:IONAPIRequestWizard>
                                                                                                              </If.Then>
                                                                                                            </If>
                                                                                                          </If.Else>
                                                                                                        </If>
                                                                                                      </If.Else>
                                                                                                    </If>
                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_335">
                                                                                                      <Assign.To>
                                                                                                        <OutArgument x:TypeArguments="njl:JToken">[out5]</OutArgument>
                                                                                                      </Assign.To>
                                                                                                      <Assign.Value>
                                                                                                        <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj5.ReadAsText)]</InArgument>
                                                                                                      </Assign.Value>
                                                                                                    </Assign>
                                                                                                    <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_8">
                                                                                                      <TryCatch.Try>
                                                                                                        <If Condition="[respout = 200]" sap2010:WorkflowViewState.IdRef="If_144">
                                                                                                          <If.Then>
                                                                                                            <If Condition="[out5(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_143">
                                                                                                              <If.Then>
                                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_153">
                                                                                                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_83" Line="[(out5(&quot;results&quot;)(0)(&quot;errorMessage&quot;)).ToString]" Source="[logfile]" />
                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_336">
                                                                                                                    <Assign.To>
                                                                                                                      <OutArgument x:TypeArguments="x:String">[additionalChargeExcp]</OutArgument>
                                                                                                                    </Assign.To>
                                                                                                                    <Assign.Value>
                                                                                                                      <InArgument x:TypeArguments="x:String">[additionalChargeExcp + "Tax line failed."]</InArgument>
                                                                                                                    </Assign.Value>
                                                                                                                  </Assign>
                                                                                                                </Sequence>
                                                                                                              </If.Then>
                                                                                                              <If.Else>
                                                                                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_84" Line="Vat line added" Source="[logfile]" />
                                                                                                              </If.Else>
                                                                                                            </If>
                                                                                                          </If.Then>
                                                                                                          <If.Else>
                                                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_154">
                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_337">
                                                                                                                <Assign.To>
                                                                                                                  <OutArgument x:TypeArguments="x:String">[additionalChargeExcp]</OutArgument>
                                                                                                                </Assign.To>
                                                                                                                <Assign.Value>
                                                                                                                  <InArgument x:TypeArguments="x:String">[additionalChargeExcp]</InArgument>
                                                                                                                </Assign.Value>
                                                                                                              </Assign>
                                                                                                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_85" Line="Error while adding the Vat line" Source="[logfile]" />
                                                                                                            </Sequence>
                                                                                                          </If.Else>
                                                                                                        </If>
                                                                                                      </TryCatch.Try>
                                                                                                      <TryCatch.Catches>
                                                                                                        <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_8">
                                                                                                          <ActivityAction x:TypeArguments="s:Exception">
                                                                                                            <ActivityAction.Argument>
                                                                                                              <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                                            </ActivityAction.Argument>
                                                                                                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_86" Line="Exception while adding Vat line" Source="[logfile]" />
                                                                                                          </ActivityAction>
                                                                                                        </Catch>
                                                                                                      </TryCatch.Catches>
                                                                                                    </TryCatch>
                                                                                                  </Sequence>
                                                                                                </If.Then>
                                                                                              </If>
                                                                                            </If.Then>
                                                                                          </If>
                                                                                          <If Condition="[ocrValues(6) &lt;&gt; &quot;&quot;]" DisplayName="If Charges" sap2010:WorkflowViewState.IdRef="If_150">
                                                                                            <If.Then>
                                                                                              <If Condition="[CInt(ocrValues(6)).ToString &lt;&gt; &quot;0&quot;]" sap2010:WorkflowViewState.IdRef="If_149">
                                                                                                <If.Then>
                                                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_158">
                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_338">
                                                                                                      <Assign.To>
                                                                                                        <OutArgument x:TypeArguments="x:String">[charge]</OutArgument>
                                                                                                      </Assign.To>
                                                                                                      <Assign.Value>
                                                                                                        <InArgument x:TypeArguments="x:String">[ocrValues(6)]</InArgument>
                                                                                                      </Assign.Value>
                                                                                                    </Assign>
                                                                                                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding charges" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_41" Response="[respObj5]" StatusCode="[respout]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                                                                      <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                          <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                                            <x:String>INBN</x:String>
                                                                                                            <x:String>RDTP</x:String>
                                                                                                            <x:String>DIVI</x:String>
                                                                                                            <x:String>NLAM</x:String>
                                                                                                            <x:String>CEID</x:String>
                                                                                                            <x:String>PUNO</x:String>
                                                                                                          </scg:List>
                                                                                                          <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                                            <x:String>inbnValue</x:String>
                                                                                                            <x:String>2</x:String>
                                                                                                            <x:String>division</x:String>
                                                                                                            <x:String>charge</x:String>
                                                                                                            <x:String>chargeCode</x:String>
                                                                                                            <x:String>pono</x:String>
                                                                                                          </scg:List>
                                                                                                        </scg:List>
                                                                                                      </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                    </iai:IONAPIRequestWizard>
                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_339">
                                                                                                      <Assign.To>
                                                                                                        <OutArgument x:TypeArguments="njl:JToken">[out5]</OutArgument>
                                                                                                      </Assign.To>
                                                                                                      <Assign.Value>
                                                                                                        <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj5.ReadAsText)]</InArgument>
                                                                                                      </Assign.Value>
                                                                                                    </Assign>
                                                                                                    <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_9">
                                                                                                      <TryCatch.Try>
                                                                                                        <If Condition="[respout = 200]" sap2010:WorkflowViewState.IdRef="If_148">
                                                                                                          <If.Then>
                                                                                                            <If Condition="[out5(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_147">
                                                                                                              <If.Then>
                                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_156">
                                                                                                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_87" Line="[(out5(&quot;results&quot;)(0)(&quot;errorMessage&quot;)).ToString]" Source="[logfile]" />
                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_340">
                                                                                                                    <Assign.To>
                                                                                                                      <OutArgument x:TypeArguments="x:String">[additionalChargeExcp]</OutArgument>
                                                                                                                    </Assign.To>
                                                                                                                    <Assign.Value>
                                                                                                                      <InArgument x:TypeArguments="x:String">[additionalChargeExcp + "Charge line failed."]</InArgument>
                                                                                                                    </Assign.Value>
                                                                                                                  </Assign>
                                                                                                                </Sequence>
                                                                                                              </If.Then>
                                                                                                              <If.Else>
                                                                                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_88" Line="Charge line added" Source="[logfile]" />
                                                                                                              </If.Else>
                                                                                                            </If>
                                                                                                          </If.Then>
                                                                                                          <If.Else>
                                                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_157">
                                                                                                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_89" Line="Error while adding the charge line" Source="[logfile]" />
                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_341">
                                                                                                                <Assign.To>
                                                                                                                  <OutArgument x:TypeArguments="x:String">[additionalChargeExcp]</OutArgument>
                                                                                                                </Assign.To>
                                                                                                                <Assign.Value>
                                                                                                                  <InArgument x:TypeArguments="x:String">[additionalChargeExcp + "Charge line failed."]</InArgument>
                                                                                                                </Assign.Value>
                                                                                                              </Assign>
                                                                                                            </Sequence>
                                                                                                          </If.Else>
                                                                                                        </If>
                                                                                                      </TryCatch.Try>
                                                                                                      <TryCatch.Catches>
                                                                                                        <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_9">
                                                                                                          <ActivityAction x:TypeArguments="s:Exception">
                                                                                                            <ActivityAction.Argument>
                                                                                                              <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                                            </ActivityAction.Argument>
                                                                                                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_90" Line="Exception while adding charge line" Source="[logfile]" />
                                                                                                          </ActivityAction>
                                                                                                        </Catch>
                                                                                                      </TryCatch.Catches>
                                                                                                    </TryCatch>
                                                                                                  </Sequence>
                                                                                                </If.Then>
                                                                                              </If>
                                                                                            </If.Then>
                                                                                          </If>
                                                                                          <If Condition="[ocrValues(7) &lt;&gt; &quot;&quot; AND NOT (discountHandling = &quot;True&quot;)]" DisplayName="If discount" sap2010:WorkflowViewState.IdRef="If_154">
                                                                                            <If.Then>
                                                                                              <If Condition="[CInt(ocrValues(7)).ToString &lt;&gt; &quot;0&quot;]" sap2010:WorkflowViewState.IdRef="If_153">
                                                                                                <If.Then>
                                                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_161">
                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_342">
                                                                                                      <Assign.To>
                                                                                                        <OutArgument x:TypeArguments="x:String">[discount]</OutArgument>
                                                                                                      </Assign.To>
                                                                                                      <Assign.Value>
                                                                                                        <InArgument x:TypeArguments="x:String">["-"+ocrValues(7)]</InArgument>
                                                                                                      </Assign.Value>
                                                                                                    </Assign>
                                                                                                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding Discount" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_42" Response="[respObj5]" StatusCode="[respout]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                                                                      <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                          <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                                            <x:String>INBN</x:String>
                                                                                                            <x:String>RDTP</x:String>
                                                                                                            <x:String>DIVI</x:String>
                                                                                                            <x:String>NLAM</x:String>
                                                                                                            <x:String>CEID</x:String>
                                                                                                            <x:String>PUNO</x:String>
                                                                                                          </scg:List>
                                                                                                          <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                                            <x:String>inbnValue</x:String>
                                                                                                            <x:String>2</x:String>
                                                                                                            <x:String>division</x:String>
                                                                                                            <x:String>discount</x:String>
                                                                                                            <x:String>discountCode</x:String>
                                                                                                            <x:String>pono</x:String>
                                                                                                          </scg:List>
                                                                                                        </scg:List>
                                                                                                      </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                    </iai:IONAPIRequestWizard>
                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_343">
                                                                                                      <Assign.To>
                                                                                                        <OutArgument x:TypeArguments="njl:JToken">[out5]</OutArgument>
                                                                                                      </Assign.To>
                                                                                                      <Assign.Value>
                                                                                                        <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj5.ReadAsText)]</InArgument>
                                                                                                      </Assign.Value>
                                                                                                    </Assign>
                                                                                                    <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_10">
                                                                                                      <TryCatch.Try>
                                                                                                        <If Condition="[respout = 200]" sap2010:WorkflowViewState.IdRef="If_152">
                                                                                                          <If.Then>
                                                                                                            <If Condition="[out5(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_151">
                                                                                                              <If.Then>
                                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_159">
                                                                                                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_91" Line="[(out5(&quot;results&quot;)(0)(&quot;errorMessage&quot;)).ToString]" Source="[logfile]" />
                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_344">
                                                                                                                    <Assign.To>
                                                                                                                      <OutArgument x:TypeArguments="x:String">[additionalChargeExcp]</OutArgument>
                                                                                                                    </Assign.To>
                                                                                                                    <Assign.Value>
                                                                                                                      <InArgument x:TypeArguments="x:String">[additionalChargeExcp + "Discount line failed."]</InArgument>
                                                                                                                    </Assign.Value>
                                                                                                                  </Assign>
                                                                                                                </Sequence>
                                                                                                              </If.Then>
                                                                                                              <If.Else>
                                                                                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_92" Line="Discount line added" Source="[logfile]" />
                                                                                                              </If.Else>
                                                                                                            </If>
                                                                                                          </If.Then>
                                                                                                          <If.Else>
                                                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_160">
                                                                                                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_93" Line="Error while adding the discount line" Source="[logfile]" />
                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_345">
                                                                                                                <Assign.To>
                                                                                                                  <OutArgument x:TypeArguments="x:String">[additionalChargeExcp]</OutArgument>
                                                                                                                </Assign.To>
                                                                                                                <Assign.Value>
                                                                                                                  <InArgument x:TypeArguments="x:String">[additionalChargeExcp + "Discount line failed."]</InArgument>
                                                                                                                </Assign.Value>
                                                                                                              </Assign>
                                                                                                            </Sequence>
                                                                                                          </If.Else>
                                                                                                        </If>
                                                                                                      </TryCatch.Try>
                                                                                                      <TryCatch.Catches>
                                                                                                        <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_10">
                                                                                                          <ActivityAction x:TypeArguments="s:Exception">
                                                                                                            <ActivityAction.Argument>
                                                                                                              <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                                            </ActivityAction.Argument>
                                                                                                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_94" Line="Exception while adding discount line" Source="[logfile]" />
                                                                                                          </ActivityAction>
                                                                                                        </Catch>
                                                                                                      </TryCatch.Catches>
                                                                                                    </TryCatch>
                                                                                                  </Sequence>
                                                                                                </If.Then>
                                                                                              </If>
                                                                                            </If.Then>
                                                                                          </If>
                                                                                        </Sequence>
                                                                                      </If.Then>
                                                                                    </If>
                                                                                    <If Condition="[inbnValue = &quot;&quot;]" DisplayName="If validation" sap2010:WorkflowViewState.IdRef="If_159">
                                                                                      <If.Then>
                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_346">
                                                                                          <Assign.To>
                                                                                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                          </Assign.To>
                                                                                          <Assign.Value>
                                                                                            <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                                                                                          </Assign.Value>
                                                                                        </Assign>
                                                                                      </If.Then>
                                                                                      <If.Else>
                                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_164">
                                                                                          <Sequence.Variables>
                                                                                            <Variable x:TypeArguments="x:String" Name="SuccessMoved" />
                                                                                          </Sequence.Variables>
                                                                                          <If Condition="[additionalChargeExcp = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_158">
                                                                                            <If.Then>
                                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_163">
                                                                                                <Sequence.Variables>
                                                                                                  <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="checkApprovalResponseDictionary" />
                                                                                                  <Variable x:TypeArguments="x:Int32" Name="checkApprovalResponseCode" />
                                                                                                </Sequence.Variables>
                                                                                                <If Condition="[approvalRequired]" sap2010:WorkflowViewState.IdRef="If_263">
                                                                                                  <If.Then>
                                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_283">
                                                                                                      <Sequence.Variables>
                                                                                                        <Variable x:TypeArguments="x:Boolean" Name="approvalRequest" />
                                                                                                        <Variable x:TypeArguments="x:Int32" Name="sendApprovalResponseCode" />
                                                                                                      </Sequence.Variables>
                                                                                                      <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;amount&quot;,ocrValues(2)},{&quot;tenantID&quot;,tenantID},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule}}]" ContinueOnError="True" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_6" OutputArguments="[checkApprovalResponseDictionary]" ResponseCode="[checkApprovalResponseCode]" WorkflowFile="[projectPath+&quot;\CheckApproval.xaml&quot;]" />
                                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_518">
                                                                                                        <Assign.To>
                                                                                                          <OutArgument x:TypeArguments="x:Boolean">[approvalRequest]</OutArgument>
                                                                                                        </Assign.To>
                                                                                                        <Assign.Value>
                                                                                                          <InArgument x:TypeArguments="x:Boolean">[CType(checkApprovalResponseDictionary("approvalRequest"), Boolean)]</InArgument>
                                                                                                        </Assign.Value>
                                                                                                      </Assign>
                                                                                                      <If Condition="[approvalRequest]" sap2010:WorkflowViewState.IdRef="If_264">
                                                                                                        <If.Then>
                                                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_284">
                                                                                                            <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;amount&quot;,ocrValues(2)},{&quot;tenantID&quot;,tenantID},{&quot;division&quot;,DIVISION},{&quot;inbn&quot;,inbnValue},{&quot;invoiceNumber&quot;,ocrValues(0)},{&quot;poNumber&quot;,ocrValues(4)},{&quot;vendorName&quot;,vendorId},{&quot;approvalWorkflow&quot;,approvalWorkflow}}]" ContinueOnError="True" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_7" OutputArguments="[sendApprovalResponseDictionary]" ResponseCode="[sendApprovalResponseCode]" WorkflowFile="[projectPath+&quot;\SendtoApprovalWorkflow.xaml&quot;]" />
                                                                                                            <If Condition="[sendApprovalResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_265">
                                                                                                              <If.Then>
                                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_285">
                                                                                                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_102" Line="Supllier invoice is sent for approval since the invoice amount is large" Source="[logfile]" />
                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_519">
                                                                                                                    <Assign.To>
                                                                                                                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                                    </Assign.To>
                                                                                                                    <Assign.Value>
                                                                                                                      <InArgument x:TypeArguments="x:String">["Invoice created and Sent for approval"]</InArgument>
                                                                                                                    </Assign.Value>
                                                                                                                  </Assign>
                                                                                                                </Sequence>
                                                                                                              </If.Then>
                                                                                                            </If>
                                                                                                          </Sequence>
                                                                                                        </If.Then>
                                                                                                        <If.Else>
                                                                                                          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" Response="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_44" StatusCode="[respout1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS455MI/ValidByBatchNo&quot;]">
                                                                                                            <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                  <x:String>INBN</x:String>
                                                                                                                  <x:String>DIVI</x:String>
                                                                                                                </scg:List>
                                                                                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                  <x:String>inbnValue</x:String>
                                                                                                                  <x:String>division</x:String>
                                                                                                                </scg:List>
                                                                                                              </scg:List>
                                                                                                            </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                          </iai:IONAPIRequestWizard>
                                                                                                        </If.Else>
                                                                                                      </If>
                                                                                                    </Sequence>
                                                                                                  </If.Then>
                                                                                                  <If.Else>
                                                                                                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" PostData="{x:Null}" Response="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_43" StatusCode="[respout1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS455MI/ValidByBatchNo&quot;]">
                                                                                                      <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                            <x:String>INBN</x:String>
                                                                                                            <x:String>DIVI</x:String>
                                                                                                          </scg:List>
                                                                                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                            <x:String>inbnValue</x:String>
                                                                                                            <x:String>division</x:String>
                                                                                                          </scg:List>
                                                                                                        </scg:List>
                                                                                                      </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                    </iai:IONAPIRequestWizard>
                                                                                                  </If.Else>
                                                                                                </If>
                                                                                                <If Condition="[matchVendorItemCode]" sap2010:WorkflowViewState.IdRef="If_266">
                                                                                                  <If.Then>
                                                                                                    <If Condition="[transDateItem.Value.count = invoicesItemNumbers.count]" sap2010:WorkflowViewState.IdRef="If_156">
                                                                                                      <If.Then>
                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_347">
                                                                                                          <Assign.To>
                                                                                                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                          </Assign.To>
                                                                                                          <Assign.Value>
                                                                                                            <InArgument x:TypeArguments="x:String">["Invoice created and validated"]</InArgument>
                                                                                                          </Assign.Value>
                                                                                                        </Assign>
                                                                                                      </If.Then>
                                                                                                      <If.Else>
                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_348">
                                                                                                          <Assign.To>
                                                                                                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                          </Assign.To>
                                                                                                          <Assign.Value>
                                                                                                            <InArgument x:TypeArguments="x:String">["Invoice created and validated, Please verify"]</InArgument>
                                                                                                          </Assign.Value>
                                                                                                        </Assign>
                                                                                                      </If.Else>
                                                                                                    </If>
                                                                                                  </If.Then>
                                                                                                  <If.Else>
                                                                                                    <If Condition="[respOut1 = 200]" sap2010:WorkflowViewState.IdRef="If_268">
                                                                                                      <If.Then>
                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_520">
                                                                                                          <Assign.To>
                                                                                                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                          </Assign.To>
                                                                                                          <Assign.Value>
                                                                                                            <InArgument x:TypeArguments="x:String">["Invoice created and validated"]</InArgument>
                                                                                                          </Assign.Value>
                                                                                                        </Assign>
                                                                                                      </If.Then>
                                                                                                    </If>
                                                                                                  </If.Else>
                                                                                                </If>
                                                                                                <If Condition="[vat = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_157">
                                                                                                  <If.Else>
                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_349">
                                                                                                      <Assign.To>
                                                                                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                      </Assign.To>
                                                                                                      <Assign.Value>
                                                                                                        <InArgument x:TypeArguments="x:String">["Invoice created and validated. Vat line is available."]</InArgument>
                                                                                                      </Assign.Value>
                                                                                                    </Assign>
                                                                                                  </If.Else>
                                                                                                </If>
                                                                                              </Sequence>
                                                                                            </If.Then>
                                                                                            <If.Else>
                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_350">
                                                                                                <Assign.To>
                                                                                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                </Assign.To>
                                                                                                <Assign.Value>
                                                                                                  <InArgument x:TypeArguments="x:String">["Invoice created. " + additionalChargeExcp]</InArgument>
                                                                                                </Assign.Value>
                                                                                              </Assign>
                                                                                            </If.Else>
                                                                                          </If>
                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_351">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:String">["SUCCESS"]</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                        </Sequence>
                                                                                      </If.Else>
                                                                                    </If>
                                                                                  </Sequence>
                                                                                </If.Then>
                                                                                <If.Else>
                                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_166">
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_352">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:String">["Received an IONAPI error while adding Invoice header."]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_353">
                                                                                      <Assign.To>
                                                                                        <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                      </Assign.To>
                                                                                      <Assign.Value>
                                                                                        <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                                                                                      </Assign.Value>
                                                                                    </Assign>
                                                                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_95" Line="[commentStatus]" Source="[logfile]" />
                                                                                  </Sequence>
                                                                                </If.Else>
                                                                              </If>
                                                                            </Sequence>
                                                                          </Sequence>
                                                                        </If.Then>
                                                                        <If.Else>
                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_169">
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_354">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching Banking details for the Supplier Number " + SupplierNo +"."]</InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_355">
                                                                              <Assign.To>
                                                                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                              </Assign.To>
                                                                              <Assign.Value>
                                                                                <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                                                                              </Assign.Value>
                                                                            </Assign>
                                                                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_96" Line="[commentStatus]" Source="[logfile]" />
                                                                          </Sequence>
                                                                        </If.Else>
                                                                      </If>
                                                                    </Sequence>
                                                                  </Sequence>
                                                                </If.Then>
                                                                <If.Else>
                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_172">
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_356">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">The quantity and unit price of the items on the invoice do not match with receipts.</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_357">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_97" Line="[commentStatus]" Source="[logfile]" />
                                                                  </Sequence>
                                                                </If.Else>
                                                              </If>
                                                            </Sequence>
                                                          </If.Then>
                                                        </If>
                                                      </Sequence>
                                                    </If.Then>
                                                    <If.Else>
                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_45">
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_118">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:String">An error occurred with the AI Optimizer.</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_119">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_12" Line="[commentStatus]" Source="[logfile]" />
                                                      </Sequence>
                                                    </If.Else>
                                                  </If>
                                                </Sequence>
                                              </If.Then>
                                            </If>
                                          </Sequence>
                                        </ActivityAction>
                                      </ForEach>
                                    </Sequence>
                                  </If.Then>
                                  <If.Else>
                                    <If Condition="[matchVendorItemCode  and invoicesItemNumbers.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_127">
                                      <If.Then>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_140">
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_293">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">One or more items listed on the invoice have not been received.</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_294">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">PONOTRECEIVED</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_78" Line="[commentStatus]" Source="[logfile]" />
                                        </Sequence>
                                      </If.Then>
                                      <If.Else>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_139">
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_291">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">The item(s) listed on the invoice has not been received.</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_292">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">PONOTRECEIVED</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_77" Line="[commentStatus]" Source="[logfile]" />
                                        </Sequence>
                                      </If.Else>
                                    </If>
                                  </If.Else>
                                </If>
                              </If.Then>
                              <If.Else>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_114">
                                  <If Condition="[matchVendorItemCode  and invoicesItemNumbers.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_109">
                                    <If.Then>
                                      <If Condition="[allLinesReceived]" sap2010:WorkflowViewState.IdRef="If_112">
                                        <If.Then>
                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_118">
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_259">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">All items listed on the invoice have been invoiced.</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_260">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">ALLPOLINESINVOICED</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_69" Line="[commentStatus]" Source="[logFile]" />
                                          </Sequence>
                                        </If.Then>
                                        <If.Else>
                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_119">
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_261">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">["The item(s) listed on the invoice has not been received."]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_262">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String">PONOTRECEIVED</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_70" Line="[commentStatus]" Source="[logfile]" />
                                          </Sequence>
                                        </If.Else>
                                      </If>
                                    </If.Then>
                                    <If.Else>
                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_121">
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_263">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">Items from the purchase order have not been received or have already been invoiced.</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_264">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">PONOTRECEIVED</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_71" Line="[commentStatus]" Source="[logfile]" />
                                      </Sequence>
                                    </If.Else>
                                  </If>
                                </Sequence>
                              </If.Else>
                            </If>
                          </Sequence>
                        </If.Else>
                      </If>
                    </Sequence>
                  </If.Then>
                  <If.Else>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_44">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_116">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching lines for the PO " + pono +"."]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_117">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_11" Line="[commentStatus]" Source="[logfile]" />
                    </Sequence>
                  </If.Else>
                </If>
              </Sequence>
            </If.Else>
          </If>
        </Sequence>
      </If.Then>
      <If.Else>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_43">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_114">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching company number and division for the PO " + pono +"."]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_115">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
            </Assign.Value>
          </Assign>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_10" Line="[commentStatus]" Source="[logfile]" />
        </Sequence>
      </If.Else>
    </If>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="5962.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_136" sap:VirtualizedContainerService.HintSize="5962.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_137" sap:VirtualizedContainerService.HintSize="5962.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_158" sap:VirtualizedContainerService.HintSize="5962.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_157" sap:VirtualizedContainerService.HintSize="5962.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="5962.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_1" sap:VirtualizedContainerService.HintSize="5962.66666666667,22" />
      <sap2010:ViewStateData Id="Assign_240" sap:VirtualizedContainerService.HintSize="5962.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_271" sap:VirtualizedContainerService.HintSize="5962.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="5650.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_7" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_111" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="264,348.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_19" sap:VirtualizedContainerService.HintSize="5338.66666666667,22" />
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="5338.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_141" sap:VirtualizedContainerService.HintSize="5338.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_142" sap:VirtualizedContainerService.HintSize="5338.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_235" sap:VirtualizedContainerService.HintSize="5338.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_239" sap:VirtualizedContainerService.HintSize="5338.66666666667,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_2" sap:VirtualizedContainerService.HintSize="738,22" />
      <sap2010:ViewStateData Id="Assign_238" sap:VirtualizedContainerService.HintSize="738,62" />
      <sap2010:ViewStateData Id="Assign_234" sap:VirtualizedContainerService.HintSize="590,62" />
      <sap2010:ViewStateData Id="Assign_236" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="If_91" sap:VirtualizedContainerService.HintSize="464,215.333333333333" />
      <sap2010:ViewStateData Id="If_90" sap:VirtualizedContainerService.HintSize="590,369.333333333333" />
      <sap2010:ViewStateData Id="Assign_237" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="If_92" sap:VirtualizedContainerService.HintSize="464,213.333333333333" />
      <sap2010:ViewStateData Id="If_93" sap:VirtualizedContainerService.HintSize="590,365.333333333333" />
      <sap2010:ViewStateData Id="Sequence_102" sap:VirtualizedContainerService.HintSize="612,1000.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_94" sap:VirtualizedContainerService.HintSize="738,1154.66666666667" />
      <sap2010:ViewStateData Id="Sequence_101" sap:VirtualizedContainerService.HintSize="760,1442.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_88" sap:VirtualizedContainerService.HintSize="5338.66666666667,52">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_56" sap:VirtualizedContainerService.HintSize="5338.66666666667,22" />
      <sap2010:ViewStateData Id="Assign_166" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_167" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_51" sap:VirtualizedContainerService.HintSize="510,216" />
      <sap2010:ViewStateData Id="If_52" sap:VirtualizedContainerService.HintSize="5338.66666666667,52">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_168" sap:VirtualizedContainerService.HintSize="5338.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_164" sap:VirtualizedContainerService.HintSize="5338.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_163" sap:VirtualizedContainerService.HintSize="5338.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="5338.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_17" sap:VirtualizedContainerService.HintSize="5338.66666666667,22" />
      <sap2010:ViewStateData Id="Assign_170" sap:VirtualizedContainerService.HintSize="776,62" />
      <sap2010:ViewStateData Id="Assign_171" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_172" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_69" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_57" sap:VirtualizedContainerService.HintSize="464,340" />
      <sap2010:ViewStateData Id="Sequence_68" sap:VirtualizedContainerService.HintSize="486,566">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_227" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_228" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_55" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_99" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_56" sap:VirtualizedContainerService.HintSize="776,720" />
      <sap2010:ViewStateData Id="Sequence_67" sap:VirtualizedContainerService.HintSize="798,946">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_225" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_226" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_54" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_98" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_55" sap:VirtualizedContainerService.HintSize="5338.66666666667,1098" />
      <sap2010:ViewStateData Id="Assign_256" sap:VirtualizedContainerService.HintSize="5338.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_283" sap:VirtualizedContainerService.HintSize="5338.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_31" sap:VirtualizedContainerService.HintSize="2100,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_5" sap:VirtualizedContainerService.HintSize="812.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_517" sap:VirtualizedContainerService.HintSize="664.666666666667,62" />
      <sap2010:ViewStateData Id="InvokeMethod_13" sap:VirtualizedContainerService.HintSize="464,134" />
      <sap2010:ViewStateData Id="Assign_370" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="InvokeMethod_14" sap:VirtualizedContainerService.HintSize="242,132" />
      <sap2010:ViewStateData Id="Sequence_181" sap:VirtualizedContainerService.HintSize="264,357.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_171" sap:VirtualizedContainerService.HintSize="464,511.333333333333" />
      <sap2010:ViewStateData Id="Sequence_182" sap:VirtualizedContainerService.HintSize="486,809.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_174" sap:VirtualizedContainerService.HintSize="612,963.333333333333" />
      <sap2010:ViewStateData Id="Sequence_186" sap:VirtualizedContainerService.HintSize="634,1087.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_33" sap:VirtualizedContainerService.HintSize="664.666666666667,1240" />
      <sap2010:ViewStateData Id="Sequence_282" sap:VirtualizedContainerService.HintSize="686.666666666667,1466">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_261" sap:VirtualizedContainerService.HintSize="812.666666666667,1620" />
      <sap2010:ViewStateData Id="Sequence_188" sap:VirtualizedContainerService.HintSize="834.666666666667,1806">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeMethod_2" sap:VirtualizedContainerService.HintSize="464,134" />
      <sap2010:ViewStateData Id="Assign_257" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_8" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Sequence_134" sap:VirtualizedContainerService.HintSize="264,360">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_110" sap:VirtualizedContainerService.HintSize="464,514" />
      <sap2010:ViewStateData Id="Sequence_117" sap:VirtualizedContainerService.HintSize="486,812">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_243" sap:VirtualizedContainerService.HintSize="612,62" />
      <sap2010:ViewStateData Id="Assign_369" sap:VirtualizedContainerService.HintSize="612,62" />
      <sap2010:ViewStateData Id="InvokeMethod_3" sap:VirtualizedContainerService.HintSize="464,134" />
      <sap2010:ViewStateData Id="Assign_258" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_9" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Sequence_135" sap:VirtualizedContainerService.HintSize="264,360">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_111" sap:VirtualizedContainerService.HintSize="464,514" />
      <sap2010:ViewStateData Id="Sequence_120" sap:VirtualizedContainerService.HintSize="486,812">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_102" sap:VirtualizedContainerService.HintSize="612,966" />
      <sap2010:ViewStateData Id="Sequence_108" sap:VirtualizedContainerService.HintSize="634,1294">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_96" sap:VirtualizedContainerService.HintSize="1146,1448" />
      <sap2010:ViewStateData Id="Sequence_103" sap:VirtualizedContainerService.HintSize="1168,1572">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_9" sap:VirtualizedContainerService.HintSize="1198.66666666667,1724.66666666667" />
      <sap2010:ViewStateData Id="If_170" sap:VirtualizedContainerService.HintSize="2059.33333333333,1960" />
      <sap2010:ViewStateData Id="Sequence_180" sap:VirtualizedContainerService.HintSize="2081.33333333333,2084">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_65" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_7" sap:VirtualizedContainerService.HintSize="2086,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_7" sap:VirtualizedContainerService.HintSize="2100,2319.33333333333" />
      <sap2010:ViewStateData Id="Append_Line_63" sap:VirtualizedContainerService.HintSize="2100,22" />
      <sap2010:ViewStateData Id="Sequence_124" sap:VirtualizedContainerService.HintSize="2122,2567.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_117" sap:VirtualizedContainerService.HintSize="5338.66666666667,2719.33333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_2" sap:VirtualizedContainerService.HintSize="5338.66666666667,22" />
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="5026.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_13" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_14" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="264,348.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_20" sap:VirtualizedContainerService.HintSize="4714.66666666667,22" />
      <sap2010:ViewStateData Id="Assign_133" sap:VirtualizedContainerService.HintSize="4714.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_16" sap:VirtualizedContainerService.HintSize="4714.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_18" sap:VirtualizedContainerService.HintSize="878,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_19" sap:VirtualizedContainerService.HintSize="878,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_20" sap:VirtualizedContainerService.HintSize="730,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_21" sap:VirtualizedContainerService.HintSize="730,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_22" sap:VirtualizedContainerService.HintSize="730,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_23" sap:VirtualizedContainerService.HintSize="730,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_24" sap:VirtualizedContainerService.HintSize="730,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_25" sap:VirtualizedContainerService.HintSize="730,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_27" sap:VirtualizedContainerService.HintSize="730,61.3333333333333" />
      <sap2010:ViewStateData Id="InvokeMethod_1" sap:VirtualizedContainerService.HintSize="218,132" />
      <sap2010:ViewStateData Id="Sequence_106" sap:VirtualizedContainerService.HintSize="240,256">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_107" sap:VirtualizedContainerService.HintSize="262,380">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_99" sap:VirtualizedContainerService.HintSize="464,532" />
      <sap2010:ViewStateData Id="InvokeMethod_4" sap:VirtualizedContainerService.HintSize="218,132" />
      <sap2010:ViewStateData Id="Sequence_111" sap:VirtualizedContainerService.HintSize="240,256">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_107" sap:VirtualizedContainerService.HintSize="730,684" />
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="752,1517.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="878,1669.33333333333" />
      <sap2010:ViewStateData Id="Sequence_6" sap:VirtualizedContainerService.HintSize="900,1996">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="922,2120">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_3" sap:VirtualizedContainerService.HintSize="4714.66666666667,2271.33333333333" />
      <sap2010:ViewStateData Id="Assign_269" sap:VirtualizedContainerService.HintSize="4714.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="If_269" sap:VirtualizedContainerService.HintSize="4714.66666666667,212" />
      <sap2010:ViewStateData Id="InvokeMethod_6" sap:VirtualizedContainerService.HintSize="218,132" />
      <sap2010:ViewStateData Id="If_118" sap:VirtualizedContainerService.HintSize="464,284" />
      <sap2010:ViewStateData Id="InvokeMethod_7" sap:VirtualizedContainerService.HintSize="218,132" />
      <sap2010:ViewStateData Id="If_119" sap:VirtualizedContainerService.HintSize="708,436" />
      <sap2010:ViewStateData Id="Sequence_125" sap:VirtualizedContainerService.HintSize="730,560">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_15" sap:VirtualizedContainerService.HintSize="4714.66666666667,711.333333333333" />
      <sap2010:ViewStateData Id="Assign_363" sap:VirtualizedContainerService.HintSize="464,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_364" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="ForEach`1_31" sap:VirtualizedContainerService.HintSize="464,212.666666666667" />
      <sap2010:ViewStateData Id="Assign_365" sap:VirtualizedContainerService.HintSize="464,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_366" sap:VirtualizedContainerService.HintSize="464,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_367" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="If_166" sap:VirtualizedContainerService.HintSize="464,213.333333333333" />
      <sap2010:ViewStateData Id="Append_Line_100" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Append_Line_101" sap:VirtualizedContainerService.HintSize="218,22" />
      <sap2010:ViewStateData Id="InvokeMethod_12" sap:VirtualizedContainerService.HintSize="218,132" />
      <sap2010:ViewStateData Id="Sequence_176" sap:VirtualizedContainerService.HintSize="240,318">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_167" sap:VirtualizedContainerService.HintSize="464,470" />
      <sap2010:ViewStateData Id="Sequence_177" sap:VirtualizedContainerService.HintSize="486,1466">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_178" sap:VirtualizedContainerService.HintSize="508,1590">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_168" sap:VirtualizedContainerService.HintSize="634,1742" />
      <sap2010:ViewStateData Id="If_169" sap:VirtualizedContainerService.HintSize="760,1894" />
      <sap2010:ViewStateData Id="Sequence_179" sap:VirtualizedContainerService.HintSize="782,2018">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_32" sap:VirtualizedContainerService.HintSize="812.666666666667,2170.66666666667" />
      <sap2010:ViewStateData Id="If_165" sap:VirtualizedContainerService.HintSize="938.666666666667,2324.66666666667" />
      <sap2010:ViewStateData Id="Catch`1_13" sap:VirtualizedContainerService.HintSize="943.333333333333,21.3333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_13" sap:VirtualizedContainerService.HintSize="957.333333333333,2562" />
      <sap2010:ViewStateData Id="If_262" sap:VirtualizedContainerService.HintSize="4714.66666666667,52">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_64" sap:VirtualizedContainerService.HintSize="664.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_284" sap:VirtualizedContainerService.HintSize="664.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_241" sap:VirtualizedContainerService.HintSize="612,62" />
      <sap2010:ViewStateData Id="Assign_280" sap:VirtualizedContainerService.HintSize="612,62" />
      <sap2010:ViewStateData Id="Assign_242" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_281" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_72" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_285" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_131" sap:VirtualizedContainerService.HintSize="264,452">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_98" sap:VirtualizedContainerService.HintSize="464,606" />
      <sap2010:ViewStateData Id="ForEach`1_26" sap:VirtualizedContainerService.HintSize="612,758.666666666667" />
      <sap2010:ViewStateData Id="Append_Line_73" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Append_Line_74" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_125" sap:VirtualizedContainerService.HintSize="464,214" />
      <sap2010:ViewStateData Id="Assign_286" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Sequence_136" sap:VirtualizedContainerService.HintSize="486,440">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_101" sap:VirtualizedContainerService.HintSize="612,594" />
      <sap2010:ViewStateData Id="Sequence_105" sap:VirtualizedContainerService.HintSize="634,1720.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_10" sap:VirtualizedContainerService.HintSize="664.666666666667,1873.33333333333" />
      <sap2010:ViewStateData Id="Sequence_130" sap:VirtualizedContainerService.HintSize="686.666666666667,2161.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_106" sap:VirtualizedContainerService.HintSize="812.666666666667,2315.33333333333" />
      <sap2010:ViewStateData Id="If_128" sap:VirtualizedContainerService.HintSize="4714.66666666667,52">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_272" sap:VirtualizedContainerService.HintSize="3220.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_278" sap:VirtualizedContainerService.HintSize="3020,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_273" sap:VirtualizedContainerService.HintSize="3020,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_274" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="If_121" sap:VirtualizedContainerService.HintSize="554,213.333333333333" />
      <sap2010:ViewStateData Id="Assign_275" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_128" sap:VirtualizedContainerService.HintSize="264,185.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_277" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_129" sap:VirtualizedContainerService.HintSize="264,185.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_123" sap:VirtualizedContainerService.HintSize="554,337.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_282" sap:VirtualizedContainerService.HintSize="554,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_132" sap:VirtualizedContainerService.HintSize="576,816">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_133" sap:VirtualizedContainerService.HintSize="598,940">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_19" sap:VirtualizedContainerService.HintSize="3020,1091.33333333333" />
      <sap2010:ViewStateData Id="InvokeWorkflow_1" sap:VirtualizedContainerService.HintSize="3020,22" />
      <sap2010:ViewStateData Id="Assign_53" sap:VirtualizedContainerService.HintSize="2708,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_54" sap:VirtualizedContainerService.HintSize="2708,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_52" sap:VirtualizedContainerService.HintSize="2560,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_296" sap:VirtualizedContainerService.HintSize="2248,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_297" sap:VirtualizedContainerService.HintSize="2248,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_298" sap:VirtualizedContainerService.HintSize="2248,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_299" sap:VirtualizedContainerService.HintSize="2226,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_32" sap:VirtualizedContainerService.HintSize="2226,22" />
      <sap2010:ViewStateData Id="Assign_300" sap:VirtualizedContainerService.HintSize="1914,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_301" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_142" sap:VirtualizedContainerService.HintSize="264,185.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_302" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_143" sap:VirtualizedContainerService.HintSize="264,185.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_131" sap:VirtualizedContainerService.HintSize="1914,337.333333333333" />
      <sap2010:ViewStateData Id="Assign_303" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Catch`1_14" sap:VirtualizedContainerService.HintSize="404.666666666667,21.3333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_14" sap:VirtualizedContainerService.HintSize="1892,296" />
      <sap2010:ViewStateData Id="Assign_304" sap:VirtualizedContainerService.HintSize="1892,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_305" sap:VirtualizedContainerService.HintSize="1892,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_306" sap:VirtualizedContainerService.HintSize="1892,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_33" sap:VirtualizedContainerService.HintSize="1892,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_34" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_132" sap:VirtualizedContainerService.HintSize="1892,212" />
      <sap2010:ViewStateData Id="Assign_307" sap:VirtualizedContainerService.HintSize="1580,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_308" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_309" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_310" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_144" sap:VirtualizedContainerService.HintSize="264,388">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_311" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_312" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_313" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_145" sap:VirtualizedContainerService.HintSize="264,388">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_133" sap:VirtualizedContainerService.HintSize="1580,540" />
      <sap2010:ViewStateData Id="Append_Line_79" sap:VirtualizedContainerService.HintSize="1580,22" />
      <sap2010:ViewStateData Id="Assign_314" sap:VirtualizedContainerService.HintSize="1580,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_315" sap:VirtualizedContainerService.HintSize="1050,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_316" sap:VirtualizedContainerService.HintSize="1050,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_317" sap:VirtualizedContainerService.HintSize="1050,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_318" sap:VirtualizedContainerService.HintSize="1050,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_319" sap:VirtualizedContainerService.HintSize="1050,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_320" sap:VirtualizedContainerService.HintSize="1050,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_321" sap:VirtualizedContainerService.HintSize="1050,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_322" sap:VirtualizedContainerService.HintSize="1050,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_35" sap:VirtualizedContainerService.HintSize="1050,22" />
      <sap2010:ViewStateData Id="Assign_323" sap:VirtualizedContainerService.HintSize="1050,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_324" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_325" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_80" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_146" sap:VirtualizedContainerService.HintSize="264,348.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_326" sap:VirtualizedContainerService.HintSize="738,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_327" sap:VirtualizedContainerService.HintSize="738,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_328" sap:VirtualizedContainerService.HintSize="738,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_36" sap:VirtualizedContainerService.HintSize="590,22" />
      <sap2010:ViewStateData Id="Assign_329" sap:VirtualizedContainerService.HintSize="590,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_330" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="If_134" sap:VirtualizedContainerService.HintSize="464,213.333333333333" />
      <sap2010:ViewStateData Id="If_135" sap:VirtualizedContainerService.HintSize="590,365.333333333333" />
      <sap2010:ViewStateData Id="Sequence_147" sap:VirtualizedContainerService.HintSize="612,652.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_136" sap:VirtualizedContainerService.HintSize="738,804.666666666667" />
      <sap2010:ViewStateData Id="Assign_331" sap:VirtualizedContainerService.HintSize="738,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_37" sap:VirtualizedContainerService.HintSize="738,22" />
      <sap2010:ViewStateData Id="Assign_332" sap:VirtualizedContainerService.HintSize="738,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_333" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_81" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_148" sap:VirtualizedContainerService.HintSize="264,247.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_137" sap:VirtualizedContainerService.HintSize="738,399.333333333333" />
      <sap2010:ViewStateData Id="Sequence_149" sap:VirtualizedContainerService.HintSize="760,1936.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_138" sap:VirtualizedContainerService.HintSize="1050,2088.66666666667" />
      <sap2010:ViewStateData Id="Sequence_150" sap:VirtualizedContainerService.HintSize="1072,3186.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_139" sap:VirtualizedContainerService.HintSize="1198,3338.66666666667" />
      <sap2010:ViewStateData Id="Sequence_151" sap:VirtualizedContainerService.HintSize="1220,3462.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_27" sap:VirtualizedContainerService.HintSize="1250.66666666667,3614" />
      <sap2010:ViewStateData Id="Sequence_152" sap:VirtualizedContainerService.HintSize="1272.66666666667,3738">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_28" sap:VirtualizedContainerService.HintSize="1303.33333333333,3889.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_82" sap:VirtualizedContainerService.HintSize="916,22" />
      <sap2010:ViewStateData Id="Assign_334" sap:VirtualizedContainerService.HintSize="916,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_38" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_39" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_40" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_140" sap:VirtualizedContainerService.HintSize="464,212" />
      <sap2010:ViewStateData Id="If_141" sap:VirtualizedContainerService.HintSize="690,364" />
      <sap2010:ViewStateData Id="If_142" sap:VirtualizedContainerService.HintSize="916,516" />
      <sap2010:ViewStateData Id="Assign_335" sap:VirtualizedContainerService.HintSize="916,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_83" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_336" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_153" sap:VirtualizedContainerService.HintSize="264,247.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_84" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_143" sap:VirtualizedContainerService.HintSize="490,399.333333333333" />
      <sap2010:ViewStateData Id="Assign_337" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_85" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_154" sap:VirtualizedContainerService.HintSize="264,247.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_144" sap:VirtualizedContainerService.HintSize="780,551.333333333333" />
      <sap2010:ViewStateData Id="Append_Line_86" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_8" sap:VirtualizedContainerService.HintSize="784.666666666667,21.3333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_8" sap:VirtualizedContainerService.HintSize="916,786">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_155" sap:VirtualizedContainerService.HintSize="938,1730.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_145" sap:VirtualizedContainerService.HintSize="1064,1882.66666666667" />
      <sap2010:ViewStateData Id="If_146" sap:VirtualizedContainerService.HintSize="1303.33333333333,2034.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_338" sap:VirtualizedContainerService.HintSize="798.666666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_41" sap:VirtualizedContainerService.HintSize="798.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_339" sap:VirtualizedContainerService.HintSize="798.666666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_87" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_340" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_156" sap:VirtualizedContainerService.HintSize="264,247.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_88" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_147" sap:VirtualizedContainerService.HintSize="490,399.333333333333" />
      <sap2010:ViewStateData Id="Append_Line_89" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_341" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_157" sap:VirtualizedContainerService.HintSize="264,247.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_148" sap:VirtualizedContainerService.HintSize="780,551.333333333333" />
      <sap2010:ViewStateData Id="Append_Line_90" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_9" sap:VirtualizedContainerService.HintSize="784.666666666667,21.3333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_9" sap:VirtualizedContainerService.HintSize="798.666666666667,786">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_158" sap:VirtualizedContainerService.HintSize="820.666666666667,1174.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_149" sap:VirtualizedContainerService.HintSize="946.666666666667,1326.66666666667" />
      <sap2010:ViewStateData Id="If_150" sap:VirtualizedContainerService.HintSize="1303.33333333333,1478.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_342" sap:VirtualizedContainerService.HintSize="798.666666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_42" sap:VirtualizedContainerService.HintSize="798.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_343" sap:VirtualizedContainerService.HintSize="798.666666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_91" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_344" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_159" sap:VirtualizedContainerService.HintSize="264,247.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_92" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_151" sap:VirtualizedContainerService.HintSize="490,399.333333333333" />
      <sap2010:ViewStateData Id="Append_Line_93" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_345" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_160" sap:VirtualizedContainerService.HintSize="264,247.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_152" sap:VirtualizedContainerService.HintSize="780,551.333333333333" />
      <sap2010:ViewStateData Id="Append_Line_94" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_10" sap:VirtualizedContainerService.HintSize="784.666666666667,21.3333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_10" sap:VirtualizedContainerService.HintSize="798.666666666667,786">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_161" sap:VirtualizedContainerService.HintSize="820.666666666667,1174.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_153" sap:VirtualizedContainerService.HintSize="946.666666666667,1326.66666666667" />
      <sap2010:ViewStateData Id="If_154" sap:VirtualizedContainerService.HintSize="1303.33333333333,1478.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_162" sap:VirtualizedContainerService.HintSize="1325.33333333333,9125.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_155" sap:VirtualizedContainerService.HintSize="1580,9277.33333333333" />
      <sap2010:ViewStateData Id="Assign_346" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="InvokeWorkflow_6" sap:VirtualizedContainerService.HintSize="712,22" />
      <sap2010:ViewStateData Id="Assign_518" sap:VirtualizedContainerService.HintSize="712,61.3333333333333" />
      <sap2010:ViewStateData Id="InvokeWorkflow_7" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Append_Line_102" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_519" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_285" sap:VirtualizedContainerService.HintSize="264,247.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_265" sap:VirtualizedContainerService.HintSize="464,399.333333333333" />
      <sap2010:ViewStateData Id="Sequence_284" sap:VirtualizedContainerService.HintSize="486,585.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_44" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_264" sap:VirtualizedContainerService.HintSize="712,737.333333333333" />
      <sap2010:ViewStateData Id="Sequence_283" sap:VirtualizedContainerService.HintSize="734,1024.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_43" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_263" sap:VirtualizedContainerService.HintSize="1000,1176.66666666667" />
      <sap2010:ViewStateData Id="Assign_347" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_348" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="If_156" sap:VirtualizedContainerService.HintSize="510,213.333333333333" />
      <sap2010:ViewStateData Id="Assign_520" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="If_268" sap:VirtualizedContainerService.HintSize="464,213.333333333333" />
      <sap2010:ViewStateData Id="If_266" sap:VirtualizedContainerService.HintSize="1000,365.333333333333" />
      <sap2010:ViewStateData Id="Assign_349" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="If_157" sap:VirtualizedContainerService.HintSize="1000,213.333333333333" />
      <sap2010:ViewStateData Id="Sequence_163" sap:VirtualizedContainerService.HintSize="1022,1959.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_350" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="If_158" sap:VirtualizedContainerService.HintSize="1290,2111.33333333333" />
      <sap2010:ViewStateData Id="Assign_351" sap:VirtualizedContainerService.HintSize="1290,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_164" sap:VirtualizedContainerService.HintSize="1312,2336.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_159" sap:VirtualizedContainerService.HintSize="1580,2488.66666666667" />
      <sap2010:ViewStateData Id="Sequence_165" sap:VirtualizedContainerService.HintSize="1602,12774.6666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_352" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_353" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_95" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_166" sap:VirtualizedContainerService.HintSize="264,348.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_160" sap:VirtualizedContainerService.HintSize="1892,12926.6666666667" />
      <sap2010:ViewStateData Id="Sequence_167" sap:VirtualizedContainerService.HintSize="1914,14004.6666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_168" sap:VirtualizedContainerService.HintSize="1936,14607.3333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_354" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_355" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_96" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_169" sap:VirtualizedContainerService.HintSize="264,348.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_161" sap:VirtualizedContainerService.HintSize="2226,14759.3333333333" />
      <sap2010:ViewStateData Id="Sequence_170" sap:VirtualizedContainerService.HintSize="2248,15046.6666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_171" sap:VirtualizedContainerService.HintSize="2270,15474.6666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_356" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_357" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_97" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_172" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_162" sap:VirtualizedContainerService.HintSize="2560,15626.6666666667" />
      <sap2010:ViewStateData Id="Sequence_141" sap:VirtualizedContainerService.HintSize="2582,15852">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_130" sap:VirtualizedContainerService.HintSize="2708,16004" />
      <sap2010:ViewStateData Id="Sequence_19" sap:VirtualizedContainerService.HintSize="2730,16330.6666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_118" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_119" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_12" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_45" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_12" sap:VirtualizedContainerService.HintSize="3020,16482.6666666667" />
      <sap2010:ViewStateData Id="Sequence_18" sap:VirtualizedContainerService.HintSize="3042,18002.6666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_120" sap:VirtualizedContainerService.HintSize="3168,18154.6666666667" />
      <sap2010:ViewStateData Id="Sequence_127" sap:VirtualizedContainerService.HintSize="3190,18278.6666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_17" sap:VirtualizedContainerService.HintSize="3220.66666666667,18430" />
      <sap2010:ViewStateData Id="Sequence_126" sap:VirtualizedContainerService.HintSize="3242.66666666667,18655.3333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_293" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_294" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_78" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_140" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_291" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_292" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_77" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_139" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_127" sap:VirtualizedContainerService.HintSize="554,502" />
      <sap2010:ViewStateData Id="If_10" sap:VirtualizedContainerService.HintSize="3822.66666666667,18807.3333333333" />
      <sap2010:ViewStateData Id="Assign_259" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_260" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_69" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_118" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_261" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_262" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_70" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_119" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_112" sap:VirtualizedContainerService.HintSize="554,504" />
      <sap2010:ViewStateData Id="Assign_263" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_264" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_71" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_121" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_109" sap:VirtualizedContainerService.HintSize="844,658" />
      <sap2010:ViewStateData Id="Sequence_114" sap:VirtualizedContainerService.HintSize="866,782">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_108" sap:VirtualizedContainerService.HintSize="4714.66666666667,18959.3333333333" />
      <sap2010:ViewStateData Id="Sequence_8" sap:VirtualizedContainerService.HintSize="4736.66666666667,22948">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_6" sap:VirtualizedContainerService.HintSize="5026.66666666667,23100" />
      <sap2010:ViewStateData Id="Sequence_23" sap:VirtualizedContainerService.HintSize="5048.66666666667,23325.3333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_116" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_117" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_11" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_44" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_15" sap:VirtualizedContainerService.HintSize="5338.66666666667,23477.3333333333" />
      <sap2010:ViewStateData Id="Sequence_16" sap:VirtualizedContainerService.HintSize="5360.66666666667,29048.6666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_11" sap:VirtualizedContainerService.HintSize="5650.66666666667,29200.6666666667" />
      <sap2010:ViewStateData Id="Sequence_22" sap:VirtualizedContainerService.HintSize="5672.66666666667,29426">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_114" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_115" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_10" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_43" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_14" sap:VirtualizedContainerService.HintSize="5962.66666666667,29578" />
      <sap2010:ViewStateData Id="Sequence_17" sap:VirtualizedContainerService.HintSize="5984.66666666667,30575.3333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="6024.66666666667,30775.3333333333" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>