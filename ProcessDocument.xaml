﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:si="clr-namespace:System.IO;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="attachment" Type="InArgument(x:String)" />
    <x:Property Name="successFolder" Type="InArgument(x:String)" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
    <x:Property Name="invoiceDictionary" Type="InArgument(scg:Dictionary(x:String, x:String))" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.OCR</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.IO</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>System.Text.RegularExpressions</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.OCR</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="ProcessDocument" sap2010:WorkflowViewState.IdRef="Sequence_10">
    <Sequence.Variables>
      <Variable x:TypeArguments="njl:JToken" Name="values" />
      <Variable x:TypeArguments="x:Int32" Name="ocrResponseCode" />
      <Variable x:TypeArguments="njl:JToken" Name="extractedDataJToken" />
      <Variable x:TypeArguments="s:String[]" Name="pageInforArray2" />
      <Variable x:TypeArguments="s:Byte[]" Name="fileBytes" />
      <Variable x:TypeArguments="x:String" Name="documentName" />
      <Variable x:TypeArguments="x:String" Name="filename" />
      <Variable x:TypeArguments="x:String" Name="comments" />
      <Variable x:TypeArguments="x:String" Name="ocrProcessedPages" />
      <Variable x:TypeArguments="x:String" Name="message" />
      <Variable x:TypeArguments="x:String" Name="startTime" />
      <Variable x:TypeArguments="x:Int32" Name="totalPages" />
      <Variable x:TypeArguments="x:String" Name="endtime" />
      <Variable x:TypeArguments="x:String" Name="totalPages_str" />
      <Variable x:TypeArguments="x:String" Name="split_invocies" />
    </Sequence.Variables>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_39">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[startTime]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[System.DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_29">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[comments]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">
          <Literal x:TypeArguments="x:String" Value="" />
        </InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_31">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[ocrProcessedPages]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">
          <Literal x:TypeArguments="x:String" Value="" />
        </InArgument>
      </Assign.Value>
    </Assign>
    <If Condition="True" sap2010:WorkflowViewState.IdRef="If_8">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_9">
          <Sequence.Variables>
            <Variable x:TypeArguments="x:Int32" Name="index" />
          </Sequence.Variables>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_4">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Int32">[index]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Int32">0</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_42">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[split_invocies]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[invoiceDictionary.Count.tostring]</InArgument>
            </Assign.Value>
          </Assign>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="[&quot;Invoice Count :&quot;+invoiceDictionary.Count.tostring]" Source="[logFile]" />
          <If Condition="[invoiceDictionary.Count &gt;1]" sap2010:WorkflowViewState.IdRef="If_7">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_8">
                <Sequence.Variables>
                  <Variable x:TypeArguments="x:String" Name="pageInfoString" />
                  <Variable x:TypeArguments="x:String" Name="templateOutput" />
                  <Variable x:TypeArguments="iru:ResponseObject" Name="splitAPIResonse" />
                  <Variable x:TypeArguments="x:Int32" Name="splitAPIResponseCode" />
                  <Variable x:TypeArguments="x:Int32" Name="splitAPIStatusCode" />
                  <Variable x:TypeArguments="x:Int32" Name="ocrPages" />
                  <Variable x:TypeArguments="x:String" Name="fileBase64Str" />
                </Sequence.Variables>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_19">
                  <Assign.To>
                    <OutArgument x:TypeArguments="s:String[]">[pageInforArray2]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="s:String[]">[New String(invoiceDictionary.Count-1){}]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_5">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:Int32">[ocrPages]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:Int32">0</InArgument>
                  </Assign.Value>
                </Assign>
                <ForEach x:TypeArguments="scg:KeyValuePair(x:String, x:String)" DisplayName="ForEach&lt;KeyValuePair&lt;String,String&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_2" Values="[invoiceDictionary]">
                  <ActivityAction x:TypeArguments="scg:KeyValuePair(x:String, x:String)">
                    <ActivityAction.Argument>
                      <DelegateInArgument x:TypeArguments="scg:KeyValuePair(x:String, x:String)" Name="invoice" />
                    </ActivityAction.Argument>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_11">
                      <If Condition="[invoice.value.Split(&quot;-&quot;C).length &gt;0]" sap2010:WorkflowViewState.IdRef="If_4">
                        <If.Then>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_4">
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_6">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[pageInforArray2(index)]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String" xml:space="preserve">["{ \""invoiceNumber\"":  \""" + invoice.Key+"\"", \""pageNumber\"" : \"""+invoice.Value+"\"" }"]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_7">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Int32">[ocrPages]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Int32">[ocrPages + invoice.value.Split("-"C).length]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_30">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[ocrProcessedPages]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String" xml:space="preserve">[ocrProcessedPages +" Invoice :"+invoice.key +" Pages : " +invoice.value+",  "]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_8">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Int32">[index]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Int32">[index + 1]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </Sequence>
                        </If.Then>
                      </If>
                    </Sequence>
                  </ActivityAction>
                </ForEach>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_9">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[pageInfoString]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[String.Join(",", pageInforArray2)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_20">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[fileName]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[attachment.Substring(attachment.LastIndexOf("\"c)+1,(attachment.Length()-attachment.LastIndexOf("\"c))-1)]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:FileToBase64 ErrorCode="{x:Null}" Base64="[fileBase64Str]" ContinueOnError="True" DisplayName="File to Base64 String" FilePath="[attachment]" sap2010:WorkflowViewState.IdRef="FileToBase64_1" />
                <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_1" Template="[&quot;{  &quot;&quot;json_input&quot;&quot;: &quot;&quot;{\&quot;&quot;filename\&quot;&quot;: \&quot;&quot;{{%filename%}}\&quot;&quot;,\&quot;&quot;base64\&quot;&quot;: \&quot;&quot;{{%filebase64%}}\&quot;&quot;,\&quot;&quot;pageInfo\&quot;&quot;: [{{%pageInfo%}} ] }&quot;&quot; }&quot;]" Text="[templateOutput]">
                  <ias:Template_Apply.Values>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>filename</x:String>
                        <x:String>filebase64</x:String>
                        <x:String>pageInfo</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>fileName</x:String>
                        <x:String>fileBase64Str</x:String>
                        <x:String>pageInfoString</x:String>
                      </scg:List>
                    </scg:List>
                  </ias:Template_Apply.Values>
                </ias:Template_Apply>
                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" QueryParameters="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_1" PostData="[templateOutput]" Response="[splitAPIResonse]" ResponseCode="[splitAPIResponseCode]" StatusCode="[splitAPIStatusCode]" Url="[tenantID+&quot;IONSERVICES/scriptingservice/engine/v1/scripts/InvoiceSplit/execute&quot;]">
                  <iai:IONAPIRequestWizard.Headers>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>Accept</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>application/json</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.Headers>
                </iai:IONAPIRequestWizard>
                <If Condition="[splitAPIStatusCode= 200]" sap2010:WorkflowViewState.IdRef="If_6">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_7">
                      <Sequence.Variables>
                        <Variable x:TypeArguments="njl:JToken" Name="responseObject" />
                        <Variable x:TypeArguments="njl:JToken" Name="filesArray" />
                        <Variable x:TypeArguments="njl:JToken" Name="splitAPIResponseToken" />
                        <Variable x:TypeArguments="njl:JToken" Name="filesResponse" />
                      </Sequence.Variables>
                      <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_2" JTokenObject="[splitAPIResponseToken]" JTokenString="[splitAPIResonse.readasjson.tostring]" />
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_24">
                        <Assign.To>
                          <OutArgument x:TypeArguments="njl:JToken">[responseObject]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="njl:JToken">[splitAPIResponseToken("result_json")]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_3" JTokenObject="[filesResponse]" JTokenString="[responseObject.tostring()]" />
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_25">
                        <Assign.To>
                          <OutArgument x:TypeArguments="njl:JToken">[filesArray]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="njl:JToken">[filesResponse("files")]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_26">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Int32">[totalPages]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:Int32">[Integer.parse(filesResponse("NoOfPages").tostring)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <If Condition="True" sap2010:WorkflowViewState.IdRef="If_5">
                        <If.Then>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_6">
                            <Sequence.Variables>
                              <Variable x:TypeArguments="x:String" Name="invoiceNumber" />
                            </Sequence.Variables>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_36">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[comments]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">Success</InArgument>
                              </Assign.Value>
                            </Assign>
                            <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_3" Values="[filesArray]">
                              <ActivityAction x:TypeArguments="njl:JToken">
                                <ActivityAction.Argument>
                                  <DelegateInArgument x:TypeArguments="njl:JToken" Name="fileVar" />
                                </ActivityAction.Argument>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_5">
                                  <Sequence.Variables>
                                    <Variable x:TypeArguments="x:String" Name="pageNumberStr" />
                                    <Variable x:TypeArguments="x:String" Name="documentPath" />
                                    <Variable x:TypeArguments="x:String" Name="newFilePath" />
                                    <Variable x:TypeArguments="x:String" Name="fileName1" />
                                  </Sequence.Variables>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_13">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[invoiceNumber]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[fileVar("invoiceNumber").tostring()]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_15">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[pageNumberStr]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[fileVar("pageNumber").tostring()]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_14">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[fileName1]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[fileName.Replace(".pdf","-"+invoiceNumber+"-"+pageNumberStr+".pdf")]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <If Condition="[fileName1.length &gt; 260]" sap2010:WorkflowViewState.IdRef="If_10">
                                    <If.Then>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_44">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[documentName]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[fileName1.Replace(".pdf","BigFile.pdf")]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                    </If.Then>
                                    <If.Else>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_43">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[documentName]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[fileName1]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                    </If.Else>
                                  </If>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_16">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[fileBase64Str]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[fileVar("base64").tostring()]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_17">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="s:Byte[]">[fileBytes]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="s:Byte[]">[System.Convert.FromBase64String(fileBase64Str)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_18">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[newFilePath]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[successFolder+"\"+System.Text.RegularExpressions.Regex.Replace(documentName, "[:#&amp;@`'*/\?&lt;&gt;|""]", "_")]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_4" MethodName="WriteAllBytes" TargetType="si:File">
                                    <InArgument x:TypeArguments="x:String">[newFilePath]</InArgument>
                                    <InArgument x:TypeArguments="s:Byte[]">[fileBytes]</InArgument>
                                  </InvokeMethod>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_37">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[comments]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[comments+Environment.NewLine +newFilePath]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </Sequence>
                              </ActivityAction>
                            </ForEach>
                          </Sequence>
                        </If.Then>
                        <If.Else>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_13">
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_32">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[comments]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String" xml:space="preserve">["Validation : " +Environment.NewLine +fileName +  "OCR page count does not match total page count." +Environment.NewLine + "Total Pages Count : "+totalPages.tostring +" OCR Pages Count: "+ocrPages.tostring+" OCR Procossed pages :" +ocrProcessedPages]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </Sequence>
                        </If.Else>
                      </If>
                    </Sequence>
                  </If.Then>
                </If>
              </Sequence>
            </If.Then>
            <If.Else>
              <If Condition="[invoiceDictionary.Count =1]" sap2010:WorkflowViewState.IdRef="If_9">
                <If.Then>
                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_12">
                    <Sequence.Variables>
                      <Variable x:TypeArguments="x:String" Name="moveFileOuput" />
                    </Sequence.Variables>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_33">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[comments]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">["Only one invoice available"]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <ias:File_Move ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Move File" sap2010:WorkflowViewState.IdRef="File_Move_1" OutputFile="[moveFileOuput]" OverwriteFile="False" Source="[attachment]" Target="[successFolder]" targetName="[fileName]" />
                  </Sequence>
                </If.Then>
              </If>
            </If.Else>
          </If>
        </Sequence>
      </If.Then>
    </If>
    <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_1" Source="[attachment]" />
    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_2" Line="[comments]" Source="[logFile]" />
    <sads:DebugSymbol.Symbol>d1tDOlxVc2Vyc1xkbWl0dGFwYWxsaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYyXFByb2Nlc3NEb2N1bWVudC54YW1sjwFNA8sDDgIBAV8FZg4DAeABZwVwDgMB3AFxBXoOAwHYAXsFxwMKAgEKyAMFyAOkAQIBB8kDBckDswECAQJkMGRlAwHjAWExYTwDAeEBbQttOgMB3wFpMWk7AwHdAXcLdzoDAdsBczFzRAMB2QF7E3sZAgELfQnFAxQCAQzIA5MByAOhAQIBCMkDkQHJA50BAgEFyQOlAckDsAECAQOBAQuIARQDAdQBiQELkAEUAwHPAZEBC5EB7QEDAcoBkgELxAMQAgENhgE1hgE2AwHXAYMBNoMBPQMB1QGOATaOAVgDAdIBiwE3iwFHAwHQAZEBlwGRAdcBAwHNAZEB3wGRAeoBAwHLAZIBGZIBOgIBDpQBD64DGgIBIbEDD8IDFAIBEJ4BEaUBGgMBxQGmARGtARoDAcEBrgER3AEbAwGgAd0BEeQBGgMBmwHlARHsARoDAZMB7QER7QHXAQMBjgHuARH9ASYDAYoB/gERiQIrAgF/igIRrQMWAgEisQMdsQM7AgERswMTwAMeAgETowE+owFnAwHIAaABP6ABUAMBxgGrATurATwDAcQBqAE8qAFGAwHCAa4BxQGuAdoBAwG/AbMBFdoBIAMBoQHiATziAV8DAZ4B3wE93wFNAwGcAeoBPOoBpwEDAZYB5wE95wFHAwGUAe0BP+0BUAMBkQHtAZUB7QGjAQMBjwHuAdgD7gHqAwMBjAHuAa8B7gHSAwMBiwH+AYYC/gGYAgMBiAH+AcMC/gHbAgMBhgH+AaIC/gG1AgMBhAH+AYID/gHeAwMBggH+AecC/gH9AgMBgAGKAh+KAjoCASOMAhWrAyACASW3AxW+Ax4CAR2/AxW/A5ICAgEUtAEX2QEcAwGiAZMCF5MCiQICAXuUAhebAiACAXacAhecAvcBAgFynQIXpAIgAgFtpQIXrAIgAgFprQIXqgMcAgEmvANAvANeAgEguQNBuQNLAgEevwOhAb8DsgECARu/A+YBvwP3AQIBGb8DgwK/A48CAgEXvwPQAb8D3gECARW0ASW0AVkDAaMBtgEb1wEmAwGlAZMC3wGTAoYCAgF+kwK4AZMC0QECAXyZAkSZAmoCAXmWAkWWAlUCAXecAtcBnAL0AQIBdZwCuAGcAskBAgFzogJEogJcAgFwnwJFnwJRAgFuqgJBqgJ1AgFspwJCpwJOAgFqrQIlrQIrAgEnrwIbnAMmAgEtnwMbqAMmAgEotwEdvgEmAwG4Ab8BHcYBJgMBsgHHAR3OASYDAasBzwEd1gEmAwGmAbMCHboCJgIBZbsCHZsDJwIBLqADHacDJgIBKbkBSbkBYQMBuQHEAUfEAXQDAbUBwQFIwQFSAwGzAckBSckBXAMBrAHUAUfUAVIDAakB0QFI0QFPAwGnAbgCSLgCTwIBaLUCSbUCUwIBZrsCnAG7AqoBAgFkwAIhmQMsAgEvogNJogNTAgEqxwIjzgIsAgFgzwIj1gIsAgFc1wIj3gIsAgFV3wIj9AIoAgFI9QIj/AIsAgFE/QIjhAMsAgE/hQMjjAMsAgE5jQMjkAMyAgE0kQMjmAMsAgEwzAJOzAJzAgFjyQJPyQJeAgFh1AJO1AJwAgFf0QJP0QJeAgFd3AJO3AKTAQIBWNkCT9kCWgIBVt8CMd8CTgIBSeECJ+gCMAIBUOsCJ/ICMAIBS/oCTvoCbAIBR/cCT/cCXgIBRYIDToIDfgIBQv8CT/8CWgIBQIoDTooDwAECATyHA0+HA1wCATqPA0yPA1cCATeOA0yOA1kCATWWA06WA3kCATOTA0+TA1kCATHmAlLmAnsCAVPjAlPjAmECAVHwAlLwAl0CAU7tAlPtAmECAUw=</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_39" sap:VirtualizedContainerService.HintSize="1677,60" />
      <sap2010:ViewStateData Id="Assign_29" sap:VirtualizedContainerService.HintSize="1677,60" />
      <sap2010:ViewStateData Id="Assign_31" sap:VirtualizedContainerService.HintSize="1677,60" />
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="1530,60" />
      <sap2010:ViewStateData Id="Assign_42" sap:VirtualizedContainerService.HintSize="1530,60" />
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="1530,22" />
      <sap2010:ViewStateData Id="Assign_19" sap:VirtualizedContainerService.HintSize="1019,60" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="1019,60" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_7" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_30" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_4" sap:VirtualizedContainerService.HintSize="264,484">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="464,632" />
      <sap2010:ViewStateData Id="Sequence_11" sap:VirtualizedContainerService.HintSize="486,756">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_2" sap:VirtualizedContainerService.HintSize="1019,904" />
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="1019,60" />
      <sap2010:ViewStateData Id="Assign_20" sap:VirtualizedContainerService.HintSize="1019,60" />
      <sap2010:ViewStateData Id="FileToBase64_1" sap:VirtualizedContainerService.HintSize="1019,22" />
      <sap2010:ViewStateData Id="Template_Apply_1" sap:VirtualizedContainerService.HintSize="1019,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_1" sap:VirtualizedContainerService.HintSize="1019,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_2" sap:VirtualizedContainerService.HintSize="872,22" />
      <sap2010:ViewStateData Id="Assign_24" sap:VirtualizedContainerService.HintSize="872,60" />
      <sap2010:ViewStateData Id="DeserializeJSON_3" sap:VirtualizedContainerService.HintSize="872,22" />
      <sap2010:ViewStateData Id="Assign_25" sap:VirtualizedContainerService.HintSize="872,60" />
      <sap2010:ViewStateData Id="Assign_26" sap:VirtualizedContainerService.HintSize="872,60" />
      <sap2010:ViewStateData Id="Assign_36" sap:VirtualizedContainerService.HintSize="561,60" />
      <sap2010:ViewStateData Id="Assign_13" sap:VirtualizedContainerService.HintSize="509,60" />
      <sap2010:ViewStateData Id="Assign_15" sap:VirtualizedContainerService.HintSize="509,60" />
      <sap2010:ViewStateData Id="Assign_14" sap:VirtualizedContainerService.HintSize="509,60" />
      <sap2010:ViewStateData Id="Assign_44" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_43" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_10" sap:VirtualizedContainerService.HintSize="509,208" />
      <sap2010:ViewStateData Id="Assign_16" sap:VirtualizedContainerService.HintSize="509,60" />
      <sap2010:ViewStateData Id="Assign_17" sap:VirtualizedContainerService.HintSize="509,60" />
      <sap2010:ViewStateData Id="Assign_18" sap:VirtualizedContainerService.HintSize="509,60" />
      <sap2010:ViewStateData Id="InvokeMethod_4" sap:VirtualizedContainerService.HintSize="509,128" />
      <sap2010:ViewStateData Id="Assign_37" sap:VirtualizedContainerService.HintSize="509,60" />
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="531,1200">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_3" sap:VirtualizedContainerService.HintSize="561,1348" />
      <sap2010:ViewStateData Id="Sequence_6" sap:VirtualizedContainerService.HintSize="583,1572">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_32" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_13" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_5" sap:VirtualizedContainerService.HintSize="872,1720" />
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="894,2268">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_6" sap:VirtualizedContainerService.HintSize="1019,2416" />
      <sap2010:ViewStateData Id="Sequence_8" sap:VirtualizedContainerService.HintSize="1041,4070">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_33" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="File_Move_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_12" sap:VirtualizedContainerService.HintSize="264,246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_9" sap:VirtualizedContainerService.HintSize="464,394" />
      <sap2010:ViewStateData Id="If_7" sap:VirtualizedContainerService.HintSize="1530,4218" />
      <sap2010:ViewStateData Id="Sequence_9" sap:VirtualizedContainerService.HintSize="1552,4604">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_8" sap:VirtualizedContainerService.HintSize="1677,4752" />
      <sap2010:ViewStateData Id="File_Delete_1" sap:VirtualizedContainerService.HintSize="1677,22" />
      <sap2010:ViewStateData Id="Append_Line_2" sap:VirtualizedContainerService.HintSize="1677,22" />
      <sap2010:ViewStateData Id="Sequence_10" sap:VirtualizedContainerService.HintSize="1699,5300">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="1739,5740" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>