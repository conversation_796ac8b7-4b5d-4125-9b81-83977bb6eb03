﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:sl="clr-namespace:System.Linq;assembly=System.Core"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="ocrValues" Type="InArgument(s:String[])" />
    <x:Property Name="colemanAPI" Type="InArgument(x:String)" />
    <x:Property Name="commentStatus" Type="OutArgument(x:String)" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="status" Type="OutArgument(x:String)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="vendorId" Type="InOutArgument(x:String)" />
    <x:Property Name="vendorName" Type="OutArgument(x:String)" />
    <x:Property Name="company" Type="InOutArgument(x:String)" />
    <x:Property Name="chargeCode" Type="InArgument(x:String)" />
    <x:Property Name="discountCode" Type="InArgument(x:String)" />
    <x:Property Name="authUser" Type="InArgument(x:String)" />
    <x:Property Name="division" Type="InOutArgument(x:String)" />
    <x:Property Name="vatCodeConfig" Type="InArgument(x:String)" />
    <x:Property Name="poDiscountsHandlingConfig" Type="InArgument(x:Boolean)" />
    <x:Property Name="ocrText" Type="InArgument(njl:JToken)" />
    <x:Property Name="matchVendorItemCode" Type="InArgument(x:Boolean)" />
    <x:Property Name="documentPath" Type="InArgument(x:String)" />
    <x:Property Name="approvalRequired" Type="InArgument(x:Boolean)" />
    <x:Property Name="approvalWorkflow" Type="InArgument(x:String)" />
    <x:Property Name="checkAmountBussinessRule" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="correlationID" Type="OutArgument(x:String)" />
    <x:Property Name="ocrLineValues" Type="InArgument(scg:List(scg:List(x:String)))" />
    <x:Property Name="InYear" Type="OutArgument(x:String)" />
    <x:Property Name="AYear" Type="OutArgument(x:String)" />
    <x:Property Name="approvalList" Type="OutArgument(scg:List(x:String))" />
    <x:Property Name="APResp" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.Text.RegularExpressions</x:String>
      <x:String>System.Globalization</x:String>
      <x:String>Microsoft.VisualBasic</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="ProcessPOInvoiceAPISequence" sap2010:WorkflowViewState.IdRef="Sequence_17">
    <Sequence.Variables>
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj1" />
      <Variable x:TypeArguments="njl:JToken" Name="out1" />
      <Variable x:TypeArguments="x:String" Name="req1" />
      <Variable x:TypeArguments="x:String" Name="pono" />
      <Variable x:TypeArguments="x:Int32" Name="count1" />
      <Variable x:TypeArguments="x:String" Name="req2" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj2" />
      <Variable x:TypeArguments="njl:JToken" Name="out2" />
      <Variable x:TypeArguments="scg:List(s:String[])" Name="M3TotalTableRows" />
      <Variable x:TypeArguments="x:Int32" Name="i" />
      <Variable x:TypeArguments="s:String[]" Name="M3Values" />
      <Variable x:TypeArguments="x:String" Name="cono" />
      <Variable x:TypeArguments="x:String" Name="cucd" />
      <Variable x:TypeArguments="x:String" Name="SupplierNo" />
      <Variable x:TypeArguments="x:String" Name="APIString" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode1" />
      <Variable x:TypeArguments="x:String" Name="pyme" />
      <Variable x:TypeArguments="scg:List(x:String)" Name="invoicesItemNumbers" />
      <Variable x:TypeArguments="x:Boolean" Name="allLinesReceived" />
      <Variable x:TypeArguments="scg:IEnumerable(sl:IGrouping(x:String, s:String[]))" Name="transDateGroups" />
      <Variable x:TypeArguments="scg:Dictionary(x:String, scg:List(s:String[]))" Name="transDateDictionary" />
      <Variable x:TypeArguments="x:Int32" Name="POocrWorkflowStatus" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="POocrWorkflowOutput" />
      <Variable x:TypeArguments="scg:List(x:String)" Name="exportResult" />
      <Variable x:TypeArguments="x:String" Name="tepy" />
      <Variable x:TypeArguments="x:String" Name="bkid" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="ColemanOutPut" />
      <Variable x:TypeArguments="x:Int32" Name="ColemanRespCode" />
      <Variable x:TypeArguments="njl:JToken" Name="httpOut" />
      <Variable x:TypeArguments="x:String" Name="ivdate" />
      <Variable x:TypeArguments="x:String" Name="inbnValue" />
      <Variable x:TypeArguments="x:Boolean" Name="InvoiceAlreadyExists" />
      <Variable x:TypeArguments="x:Boolean" Name="chargeExists" />
      <Variable x:TypeArguments="scg:List(x:String)" Name="PONumbers" />
      <Variable x:TypeArguments="scg:List(s:String[])" Name="M3TotalTableRows1" />
    </Sequence.Variables>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_893">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(x:String)">[approvalList]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(x:String)">[new list(Of string)]</InArgument>
      </Assign.Value>
    </Assign>
    <Sequence DisplayName="Fiscal year Sequence" sap2010:WorkflowViewState.IdRef="Sequence_497">
      <Sequence.Variables>
        <Variable x:TypeArguments="iru:ResponseObject" Name="respForFiscalYear" />
        <Variable x:TypeArguments="x:Int32" Name="respForFiscalYearCode" />
      </Sequence.Variables>
      <Assign DisplayName="Assign ivdate" sap2010:WorkflowViewState.IdRef="Assign_855">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[ivdate]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">[DateTime.ParseExact(ocrValues(1),"dd/MM/yyyy",Nothing).ToString("yyyyMMdd")]</InArgument>
        </Assign.Value>
      </Assign>
      <Assign sap2010:WorkflowViewState.IdRef="Assign_856">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[InYear]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">[ivdate.SubString(0,4)]</InArgument>
        </Assign.Value>
      </Assign>
      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Get Fiscal year IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_23" Response="[respForFiscalYear]" StatusCode="[respForFiscalYearCode]" Url="[tenantID+&quot;M3/m3api-rest/v2/execute/CRS900MI/GetSysCalDate&quot;]">
        <iai:IONAPIRequestWizard.Headers>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>Accept</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>application/json</x:String>
            </scg:List>
          </scg:List>
        </iai:IONAPIRequestWizard.Headers>
        <iai:IONAPIRequestWizard.QueryParameters>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>YMD8</x:String>
              <x:String>DIVI</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>ivdate</x:String>
              <x:String>division</x:String>
            </scg:List>
          </scg:List>
        </iai:IONAPIRequestWizard.QueryParameters>
      </iai:IONAPIRequestWizard>
      <If Condition="[respForFiscalYearCode = 200]" sap2010:WorkflowViewState.IdRef="If_434">
        <If.Then>
          <If Condition="[respForFiscalYear.ReadAsJson(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_433">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_495">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_857">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[AYear]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[ivdate.SubString(0,4)]</InArgument>
                  </Assign.Value>
                </Assign>
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_496">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_858">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[AYear]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[respForFiscalYear.ReadAsJson("results")(0)("records")(0)("CYP1").ToString.Substring(0,4)]</InArgument>
                  </Assign.Value>
                </Assign>
              </Sequence>
            </If.Else>
          </If>
        </If.Then>
      </If>
    </Sequence>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_1">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[pono]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[ocrValues(4)]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_630">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(x:String)">[PONumbers]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_631">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(x:String)">[PONumbers]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(x:String)">[ocrValues(4).split(","c).ToList]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_685">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[pono]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[PONUmbers(0)]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_136">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[vendorId]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_686">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows1]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(s:String[])">[new list(Of String())]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_687">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(s:String[])">[new list(Of String())]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_137">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[vendorName]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[ocrValues(8)]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_602">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[division]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[division]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_603">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[company]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[miscValues("company").ToString]</InArgument>
      </Assign.Value>
    </Assign>
    <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;pono&quot;,pono}}]" ContinueOnError="True" DisplayName="PO details Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_10" OutputArguments="[POocrWorkflowOutput]" ResponseCode="[POocrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\ExportMI.xaml&quot;]" />
    <If Condition="[POocrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_270">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_287">
          <Sequence.Variables>
            <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="vendorOcrWorkflowOutput" />
            <Variable x:TypeArguments="x:Int32" Name="vendorOcrWorkflowStatus" />
            <Variable x:TypeArguments="scg:List(x:String)" Name="vendorResult" />
          </Sequence.Variables>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_528">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(x:String)">[exportResult]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(x:String)">[CType(POocrWorkflowOutput("result"), List(Of String))]</InArgument>
            </Assign.Value>
          </Assign>
          <If Condition="[exportResult.count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_273">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_310">
                <Sequence.Variables>
                  <Variable x:TypeArguments="x:Int32" Name="invoiceExistsOcrWorkflowStatus" />
                  <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="invoiceExistsOcrWorkflowOutput" />
                  <Variable x:TypeArguments="x:String" Name="colAmount" />
                </Sequence.Variables>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_859">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[exportResult(2)]</InArgument>
                  </Assign.Value>
                </Assign>
                <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;SupInvoiceNo&quot;,ocrValues(0)},{&quot;division&quot;,division},{&quot;AYear&quot;,AYear},{&quot;vendorID&quot;,vendorId}}]" ContinueOnError="True" DisplayName="VerifyInvoiceExists Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_17" OutputArguments="[invoiceExistsOcrWorkflowOutput]" ResponseCode="[invoiceExistsOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\VerifyInvoiceExists.xaml&quot;]" />
                <Assign sap2010:WorkflowViewState.IdRef="Assign_579">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:Boolean">[InvoiceAlreadyExists]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:Boolean">[CType(invoiceExistsOcrWorkflowOutput("invoiceExist"),boolean)]</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[NOT InvoiceAlreadyExists]" sap2010:WorkflowViewState.IdRef="If_286">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_291">
                      <Sequence.Variables>
                        <Variable x:TypeArguments="x:Boolean" Name="DNinM3exists" />
                        <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="AddHeadOcrWorkflowOutput" />
                      </Sequence.Variables>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_529">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[company]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[exportResult(0)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_530">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[division]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[exportResult(1)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_531">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[exportResult(2)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_532">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[cucd]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[exportResult(3)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_533">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[tepy]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[exportResult(4)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_534">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[pyme]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[exportResult(5)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantId&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;vendorId&quot;,vendorId}}]" ContinueOnError="True" DisplayName="Vendor Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_11" OutputArguments="[vendorOcrWorkflowOutput]" ResponseCode="[vendorOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\getvendordetails.xaml&quot;]" />
                      <If Condition="[vendorOcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_271">
                        <If.Then>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_290">
                            <Sequence.Variables>
                              <Variable x:TypeArguments="x:String" Name="discountTerms" />
                            </Sequence.Variables>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_294">
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_546">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[CType(vendorOcrWorkflowOutput("Status"), String)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_547">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[CType(vendorOcrWorkflowOutput("commentStatus"), String)]</InArgument>
                                </Assign.Value>
                              </Assign>
                            </Sequence>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_540">
                              <Assign.To>
                                <OutArgument x:TypeArguments="scg:List(x:String)">[vendorResult]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="scg:List(x:String)">[CType(vendorOcrWorkflowOutput("result"), List(Of String))]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_538">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[vendorName]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[vendorResult(0)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_545">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[bkid]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[vendorResult(2)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_931">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[discountTerms]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[vendorResult(3)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_295">
                              <Sequence.Variables>
                                <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="BROcrWorkflowOutput" />
                                <Variable x:TypeArguments="x:Int32" Name="BROcrWorkflowStatus" />
                                <Variable x:TypeArguments="x:String" Name="tolAmt" />
                                <Variable x:TypeArguments="x:String" Name="tolPercentage" />
                                <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="AddLineOcrWorkflowOutput" />
                                <Variable x:TypeArguments="x:Int32" Name="AddLineOcrWorkflowStatus" />
                                <Variable x:TypeArguments="x:Int32" Name="validateStatus" />
                              </Sequence.Variables>
                              <If Condition="[miscValues(&quot;useBusinessRuleForTelerance&quot;).ToString.ToLower =&quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_303">
                                <If.Then>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_328">
                                    <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;VendorID&quot;,vendorId},{&quot;division&quot;,division},{&quot;businessRule&quot;,miscValues(&quot;businessRuleForTolerance&quot;).ToString}}]" ContinueOnError="True" DisplayName="Percentage &amp; amt Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_13" OutputArguments="[BROcrWorkflowOutput]" ResponseCode="[BROcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\DivisionVendorTolerance.xaml&quot;]" />
                                    <If Condition="[BROcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_274">
                                      <If.Then>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_296">
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_548">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[tolPercentage]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[CType(BROcrWorkflowOutput("percentage"), String)]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_549">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[tolAmt]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[CType(BROcrWorkflowOutput("amount"), String)]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                        </Sequence>
                                      </If.Then>
                                      <If.Else>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_297">
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_550">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">Percentage and tolerance amount is not obtained</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_551">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">VERIFICATION</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_106" Line="[commentStatus]" Source="[logfile]" />
                                        </Sequence>
                                      </If.Else>
                                    </If>
                                  </Sequence>
                                </If.Then>
                                <If.Else>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_329">
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_616">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[tolPercentage]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">100</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_617">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[tolAmt]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">100</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                  </Sequence>
                                </If.Else>
                              </If>
                              <If Condition="[tolPercentage = &quot;&quot; AND tolAmt = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_275">
                                <If.Then>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_298">
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_552">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">Percentage and tolerance amount is not obtained</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_553">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">NEEDSVERIFICATION</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_107" Line="[commentStatus]" Source="[logfile]" />
                                  </Sequence>
                                </If.Then>
                                <If.Else>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_346">
                                    <Sequence.Variables>
                                      <Variable x:TypeArguments="x:String" Name="poList" />
                                      <Variable x:TypeArguments="x:Int32" Name="AddHeadOcrWorkflowStatus" />
                                    </Sequence.Variables>
                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_299">
                                      <Sequence.Variables>
                                        <Variable x:TypeArguments="x:Int32" Name="linesOcrWorkflowStatus" />
                                        <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="linesOcrWorkflowOutput" />
                                        <Variable x:TypeArguments="x:Boolean" Name="poExists" />
                                      </Sequence.Variables>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_878">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:Boolean">[poExists]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_51" Values="[PONumbers]">
                                        <ActivityAction x:TypeArguments="x:String">
                                          <ActivityAction.Argument>
                                            <DelegateInArgument x:TypeArguments="x:String" Name="pono" />
                                          </ActivityAction.Argument>
                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_347">
                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_556">
                                              <Assign.To>
                                                <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
                                              </Assign.To>
                                              <Assign.Value>
                                                <InArgument x:TypeArguments="x:String" xml:space="preserve">["F2PNLI,F2REPN,F2SCOC,F2RPQA,F2TRDT,F2ITNO,F2CONO,F2CUCD,F2DIVI,F2SUNO,F2IVQA,F2PUNO,F2SUDO,F2RCAC,F2RPQT,F2SCOP,F2SERA,F2IVQT,F2ICAC,F2PNLS  from FGRECL where F2DIVI = " + DIVISION+" AND F2PUNO = " + pono + " and F2IMST != 9 "]</InArgument>
                                              </Assign.Value>
                                            </Assign>
                                            <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;miscValues&quot;,miscValues},{&quot;req2&quot;,req2}}]" ContinueOnError="True" DisplayName="Lines extraction Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_12" OutputArguments="[linesOcrWorkflowOutput]" ResponseCode="[linesOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\LinesExtractionWithPO.xaml&quot;]" />
                                            <If Condition="[linesOcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_314">
                                              <If.Then>
                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_348">
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_632">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows1]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="scg:List(s:String[])">[CType(linesOcrWorkflowOutput("M3TotalTableRows"), List(Of String()))]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_633">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:Boolean">[DNinM3exists]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:Boolean">[CType(linesOcrWorkflowOutput("DNinM3exists"), Boolean)]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <If Condition="[M3TotalTableRows1.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_315">
                                                    <If.Then>
                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_354">
                                                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_118" Line="[&quot;Receipt lines for the purchase order &quot; +pono.trim + &quot; is extracted.&quot;]" Source="[logfile]" />
                                                        <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_53" Values="[M3TotalTableRows1]">
                                                          <ActivityAction x:TypeArguments="s:String[]">
                                                            <ActivityAction.Argument>
                                                              <DelegateInArgument x:TypeArguments="s:String[]" Name="item" />
                                                            </ActivityAction.Argument>
                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_353">
                                                              <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_27" MethodName="Add">
                                                                <InvokeMethod.TargetObject>
                                                                  <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</InArgument>
                                                                </InvokeMethod.TargetObject>
                                                                <InArgument x:TypeArguments="s:String[]">[item]</InArgument>
                                                              </InvokeMethod>
                                                            </Sequence>
                                                          </ActivityAction>
                                                        </ForEach>
                                                      </Sequence>
                                                    </If.Then>
                                                    <If.Else>
                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_503">
                                                        <Sequence.Variables>
                                                          <Variable x:TypeArguments="x:String" Name="po" />
                                                        </Sequence.Variables>
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_863">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:String">[po]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:String">[po + pono.trim + ", "]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <If Condition="[DNinM3exists]" sap2010:WorkflowViewState.IdRef="If_436">
                                                          <If.Then>
                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_499">
                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_864">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="x:String" xml:space="preserve">["Lines already invoiced for po " +  po.Substring(0,po.Length-2)]</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_865">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_150" Line="[&quot;Lines already invoiced for po: &quot; + pono.trim]" Source="[logfile]" />
                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_866">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="x:Boolean">[poExists]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                            </Sequence>
                                                          </If.Then>
                                                          <If.Else>
                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_502">
                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_504">
                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_885">
                                                                  <Assign.To>
                                                                    <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
                                                                  </Assign.To>
                                                                  <Assign.Value>
                                                                    <InArgument x:TypeArguments="x:String" xml:space="preserve">["F2PNLI,F2REPN,F2SCOC,F2RPQA,F2TRDT,F2ITNO,F2CONO,F2CUCD,F2DIVI,F2SUNO,F2IVQA,F2PUNO,F2SUDO,F2RCAC,F2RPQT,F2SCOP,F2SERA,F2IVQT,F2ICAC,F2PNLS  from FGRECL where F2DIVI = " + DIVISION+" AND F2PUNO = " + pono]</InArgument>
                                                                  </Assign.Value>
                                                                </Assign>
                                                                <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;miscValues&quot;,miscValues},{&quot;req2&quot;,req2}}]" ContinueOnError="True" DisplayName="Lines extraction Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_48" OutputArguments="[linesOcrWorkflowOutput]" ResponseCode="[linesOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\LinesExtractionWithPO.xaml&quot;]" />
                                                                <If Condition="[linesOcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_440">
                                                                  <If.Then>
                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_508">
                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_886">
                                                                        <Assign.To>
                                                                          <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows1]</OutArgument>
                                                                        </Assign.To>
                                                                        <Assign.Value>
                                                                          <InArgument x:TypeArguments="scg:List(s:String[])">[CType(linesOcrWorkflowOutput("M3TotalTableRows"), List(Of String()))]</InArgument>
                                                                        </Assign.Value>
                                                                      </Assign>
                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_887">
                                                                        <Assign.To>
                                                                          <OutArgument x:TypeArguments="x:Boolean">[DNinM3exists]</OutArgument>
                                                                        </Assign.To>
                                                                        <Assign.Value>
                                                                          <InArgument x:TypeArguments="x:Boolean">[CType(linesOcrWorkflowOutput("DNinM3exists"), Boolean)]</InArgument>
                                                                        </Assign.Value>
                                                                      </Assign>
                                                                      <If Condition="[M3TotalTableRows1.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_441">
                                                                        <If.Else>
                                                                          <If Condition="[DNinM3exists]" sap2010:WorkflowViewState.IdRef="If_442">
                                                                            <If.Then>
                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_509">
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_888">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String" xml:space="preserve">["Lines already invoiced for po " +  po.Substring(0,po.Length-2)]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_889">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_154" Line="[&quot;Lines already invoiced for po: &quot; + pono.trim]" Source="[logfile]" />
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_890">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:Boolean">[poExists]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                              </Sequence>
                                                                            </If.Then>
                                                                            <If.Else>
                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_510">
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_875">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">["No receipts available for the given po: " + po.Substring(0,po.Length-2)]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_876">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:String">PONOTRECEIVED</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_152" Line="[&quot;No receipts available for the given po: &quot; + pono.trim]" Source="[logfile]" />
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_877">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:Boolean">[poExists]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                              </Sequence>
                                                                            </If.Else>
                                                                          </If>
                                                                        </If.Else>
                                                                      </If>
                                                                    </Sequence>
                                                                  </If.Then>
                                                                </If>
                                                              </Sequence>
                                                            </Sequence>
                                                          </If.Else>
                                                        </If>
                                                      </Sequence>
                                                    </If.Else>
                                                  </If>
                                                </Sequence>
                                              </If.Then>
                                              <If.Else>
                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_127" Line="Lines of the PO not extracted." Source="[logfile]" />
                                              </If.Else>
                                            </If>
                                          </Sequence>
                                        </ActivityAction>
                                      </ForEach>
                                      <If Condition="[M3TotalTableRows.Count &gt; 0 and poExists]" sap2010:WorkflowViewState.IdRef="If_380">
                                        <If.Then>
                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_427">
                                            <Sequence.Variables>
                                              <Variable x:TypeArguments="x:Int32" Name="x" />
                                              <Variable x:TypeArguments="iru:ResponseObject" Name="poLinesResponseObject" />
                                              <Variable x:TypeArguments="scg:List(x:String)" Name="notReceivedItems" />
                                              <Variable x:TypeArguments="x:Boolean" Name="optimizerSuccess" />
                                            </Sequence.Variables>
                                            <If Condition="[miscValues(&quot;MandateDeliveryNoteWhenPo&quot;).ToString.ToLower = &quot;true&quot; AND miscValues(&quot;processDeliveryNote&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_454">
                                              <If.Then>
                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_524">
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_932">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">["Verify the Delivery notes."]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_933">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">NEEDSVERIFICATION</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_155" Line="[commentStatus]" Source="[logfile]" />
                                                </Sequence>
                                              </If.Then>
                                              <If.Else>
                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_525">
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_688">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="scg:List(x:String)">[notReceivedItems]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)()]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_879">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="scg:List(x:String)">[invoicesItemNumbers]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)()]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <If Condition="[matchVendorItemCode]" DisplayName="match Vendor item code If" sap2010:WorkflowViewState.IdRef="If_355">
                                                    <If.Then>
                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_403">
                                                        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_11" Response="[poLinesResponseObject]" Url="[TenantID +&quot;M3/m3api-rest/v2/execute/PPS200MI/LstLine?maxrecs=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;returncols=PUNO%2CITNO%2CSITE%2CORQA%2CRVQA&amp;format=PRETTY&amp;extendedresult=false&quot;]">
                                                          <iai:IONAPIRequestWizard.Headers>
                                                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                <x:String>Accept</x:String>
                                                              </scg:List>
                                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                <x:String>application/json</x:String>
                                                              </scg:List>
                                                            </scg:List>
                                                          </iai:IONAPIRequestWizard.Headers>
                                                          <iai:IONAPIRequestWizard.QueryParameters>
                                                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                <x:String>PUNO</x:String>
                                                              </scg:List>
                                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                <x:String>pono</x:String>
                                                              </scg:List>
                                                            </scg:List>
                                                          </iai:IONAPIRequestWizard.QueryParameters>
                                                        </iai:IONAPIRequestWizard>
                                                        <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_62" Values="[poLinesResponseObject.readasjson(&quot;results&quot;)(0)(&quot;records&quot;)]">
                                                          <ActivityAction x:TypeArguments="njl:JToken">
                                                            <ActivityAction.Argument>
                                                              <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
                                                            </ActivityAction.Argument>
                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_399">
                                                              <ForEach x:TypeArguments="scg:List(x:String)" DisplayName="ForEach&lt;List&lt;String&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_61" Values="[ocrLineValues]">
                                                                <ActivityAction x:TypeArguments="scg:List(x:String)">
                                                                  <ActivityAction.Argument>
                                                                    <DelegateInArgument x:TypeArguments="scg:List(x:String)" Name="lines" />
                                                                  </ActivityAction.Argument>
                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_398">
                                                                    <If Condition="[lines(0).Split(&quot;(&quot;c)(0).ToLower.contains(&quot; &quot;+item(&quot;ITNO&quot;).tostring.tolower+&quot; &quot;) OR lines(0).Split(&quot;(&quot;c)(0).ToLower.contains(&quot; &quot;+item(&quot;SITE&quot;).tostring.tolower+&quot; &quot;)]" sap2010:WorkflowViewState.IdRef="If_352">
                                                                      <If.Then>
                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_394">
                                                                          <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_35" MethodName="Add">
                                                                            <InvokeMethod.TargetObject>
                                                                              <InArgument x:TypeArguments="scg:List(x:String)">[invoicesItemNumbers]</InArgument>
                                                                            </InvokeMethod.TargetObject>
                                                                            <InArgument x:TypeArguments="x:String">[item("ITNO").tostring]</InArgument>
                                                                          </InvokeMethod>
                                                                          <If Condition="[Integer.Parse(item(&quot;ORQA&quot;).tostring) &gt; Integer.Parse(item(&quot;RVQA&quot;).tostring)]" sap2010:WorkflowViewState.IdRef="If_349">
                                                                            <If.Then>
                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_393">
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_689">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="x:Boolean">[allLinesReceived]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_36" MethodName="Add">
                                                                                  <InvokeMethod.TargetObject>
                                                                                    <InArgument x:TypeArguments="scg:List(x:String)">[notReceivedItems]</InArgument>
                                                                                  </InvokeMethod.TargetObject>
                                                                                  <InArgument x:TypeArguments="x:String">[item("ITNO").tostring]</InArgument>
                                                                                </InvokeMethod>
                                                                              </Sequence>
                                                                            </If.Then>
                                                                          </If>
                                                                        </Sequence>
                                                                      </If.Then>
                                                                      <If.Else>
                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_397">
                                                                          <Sequence.Variables>
                                                                            <Variable x:TypeArguments="x:String" Name="itemNumberWithPrefix" />
                                                                            <Variable x:TypeArguments="x:String" Name="itemSupNumberWithPrefix" />
                                                                          </Sequence.Variables>
                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_690">
                                                                            <Assign.To>
                                                                              <OutArgument x:TypeArguments="x:String">[itemSupNumberWithPrefix]</OutArgument>
                                                                            </Assign.To>
                                                                            <Assign.Value>
                                                                              <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(item("SITE").tostring, "(\d+)", " $1 ")]</InArgument>
                                                                            </Assign.Value>
                                                                          </Assign>
                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_691">
                                                                            <Assign.To>
                                                                              <OutArgument x:TypeArguments="x:String">[itemNumberWithPrefix]</OutArgument>
                                                                            </Assign.To>
                                                                            <Assign.Value>
                                                                              <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(item("ITNO").tostring, "(\d+)", " $1 ")]</InArgument>
                                                                            </Assign.Value>
                                                                          </Assign>
                                                                          <If Condition="[lines(0).Split(&quot;(&quot;c)(0).tolower.contains(&quot; &quot;+itemNumberWithPrefix.trim.tolower+&quot; &quot;) OR lines(0).Split(&quot;(&quot;c)(0).tolower.contains(&quot; &quot;+itemSupNumberWithPrefix.trim.tolower+&quot; &quot;)]" sap2010:WorkflowViewState.IdRef="If_351">
                                                                            <If.Then>
                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_396">
                                                                                <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_37" MethodName="Add">
                                                                                  <InvokeMethod.TargetObject>
                                                                                    <InArgument x:TypeArguments="scg:List(x:String)">[invoicesItemNumbers]</InArgument>
                                                                                  </InvokeMethod.TargetObject>
                                                                                  <InArgument x:TypeArguments="x:String">[item("ITNO").tostring]</InArgument>
                                                                                </InvokeMethod>
                                                                                <If Condition="[Integer.Parse(item(&quot;ORQA&quot;).tostring) &gt; Integer.Parse(item(&quot;RVQA&quot;).tostring)]" sap2010:WorkflowViewState.IdRef="If_350">
                                                                                  <If.Then>
                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_395">
                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_692">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:Boolean">[allLinesReceived]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                      <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_38" MethodName="Add">
                                                                                        <InvokeMethod.TargetObject>
                                                                                          <InArgument x:TypeArguments="scg:List(x:String)">[notReceivedItems]</InArgument>
                                                                                        </InvokeMethod.TargetObject>
                                                                                        <InArgument x:TypeArguments="x:String">[item("ITNO").tostring]</InArgument>
                                                                                      </InvokeMethod>
                                                                                    </Sequence>
                                                                                  </If.Then>
                                                                                </If>
                                                                              </Sequence>
                                                                            </If.Then>
                                                                          </If>
                                                                        </Sequence>
                                                                      </If.Else>
                                                                    </If>
                                                                  </Sequence>
                                                                </ActivityAction>
                                                              </ForEach>
                                                            </Sequence>
                                                          </ActivityAction>
                                                        </ForEach>
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_693">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows1]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="scg:List(s:String[])">[new list(OF string()) ()]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_63" Values="[M3TotalTableRows]">
                                                          <ActivityAction x:TypeArguments="s:String[]">
                                                            <ActivityAction.Argument>
                                                              <DelegateInArgument x:TypeArguments="s:String[]" Name="m3Values" />
                                                            </ActivityAction.Argument>
                                                            <If Condition="[matchVendorItemCode and invoicesItemNumbers.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_354">
                                                              <If.Then>
                                                                <If Condition="[invoicesItemNumbers.contains(m3Values(5))]" sap2010:WorkflowViewState.IdRef="If_353">
                                                                  <If.Then>
                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_401">
                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_400">
                                                                        <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_39" MethodName="Add">
                                                                          <InvokeMethod.TargetObject>
                                                                            <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows1]</InArgument>
                                                                          </InvokeMethod.TargetObject>
                                                                          <InArgument x:TypeArguments="s:String[]">[m3Values]</InArgument>
                                                                        </InvokeMethod>
                                                                      </Sequence>
                                                                    </Sequence>
                                                                  </If.Then>
                                                                </If>
                                                              </If.Then>
                                                              <If.Else>
                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_402">
                                                                  <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_40" MethodName="Add">
                                                                    <InvokeMethod.TargetObject>
                                                                      <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows1]</InArgument>
                                                                    </InvokeMethod.TargetObject>
                                                                    <InArgument x:TypeArguments="s:String[]">[m3Values]</InArgument>
                                                                  </InvokeMethod>
                                                                </Sequence>
                                                              </If.Else>
                                                            </If>
                                                          </ActivityAction>
                                                        </ForEach>
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_694">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows1]</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                      </Sequence>
                                                    </If.Then>
                                                  </If>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_749">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="scg:IEnumerable(sl:IGrouping(x:String, s:String[]))">[transDateGroups]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="scg:IEnumerable(sl:IGrouping(x:String, s:String[]))">[M3TotalTableRows.GroupBy(Function(item) item(4))]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_750">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(s:String[]))">[transDateDictionary]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(s:String[]))">[new Dictionary(Of String,list(OF string()))]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <If Condition="[miscValues(&quot;groupByTransDate&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_386">
                                                    <If.Then>
                                                      <ForEach x:TypeArguments="sl:IGrouping(x:String, s:String[])" DisplayName="ForEach&lt;IGrouping&lt;String,String[]&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_68" Values="[transDateGroups]">
                                                        <ActivityAction x:TypeArguments="sl:IGrouping(x:String, s:String[])">
                                                          <ActivityAction.Argument>
                                                            <DelegateInArgument x:TypeArguments="sl:IGrouping(x:String, s:String[])" Name="group" />
                                                          </ActivityAction.Argument>
                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_435">
                                                            <If Condition="[M3TotalTableRows.Count &gt; 0 and matchVendorItemCode]" sap2010:WorkflowViewState.IdRef="If_385">
                                                              <If.Then>
                                                                <If Condition="[group.ToList().Count &gt;=  ocrLineValues.Count]" sap2010:WorkflowViewState.IdRef="If_384">
                                                                  <If.Then>
                                                                    <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_42" MethodName="Add">
                                                                      <InvokeMethod.TargetObject>
                                                                        <InArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(s:String[]))">[transDateDictionary]</InArgument>
                                                                      </InvokeMethod.TargetObject>
                                                                      <InArgument x:TypeArguments="x:String">[group.Key]</InArgument>
                                                                      <InArgument x:TypeArguments="scg:List(s:String[])">[group.ToList]</InArgument>
                                                                    </InvokeMethod>
                                                                  </If.Then>
                                                                </If>
                                                              </If.Then>
                                                              <If.Else>
                                                                <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_43" MethodName="Add">
                                                                  <InvokeMethod.TargetObject>
                                                                    <InArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(s:String[]))">[transDateDictionary]</InArgument>
                                                                  </InvokeMethod.TargetObject>
                                                                  <InArgument x:TypeArguments="x:String">[group.Key]</InArgument>
                                                                  <InArgument x:TypeArguments="scg:List(s:String[])">[group.ToList]</InArgument>
                                                                </InvokeMethod>
                                                              </If.Else>
                                                            </If>
                                                          </Sequence>
                                                        </ActivityAction>
                                                      </ForEach>
                                                    </If.Then>
                                                    <If.Else>
                                                      <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_44" MethodName="Add">
                                                        <InvokeMethod.TargetObject>
                                                          <InArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(s:String[]))">[transDateDictionary]</InArgument>
                                                        </InvokeMethod.TargetObject>
                                                        <InArgument x:TypeArguments="x:String">[transDateGroups(0).key]</InArgument>
                                                        <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</InArgument>
                                                      </InvokeMethod>
                                                    </If.Else>
                                                  </If>
                                                  <If Condition="[transDateDictionary.count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_387">
                                                    <If.Then>
                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_436">
                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_751">
                                                          <Assign.To>
                                                            <OutArgument x:TypeArguments="x:Boolean">[optimizerSuccess]</OutArgument>
                                                          </Assign.To>
                                                          <Assign.Value>
                                                            <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                                          </Assign.Value>
                                                        </Assign>
                                                        <ForEach x:TypeArguments="scg:KeyValuePair(x:String, scg:List(s:String[]))" DisplayName="ForEach&lt;KeyValuePair&lt;String,List&lt;String[]&gt;&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_70" Values="[transDateDictionary]">
                                                          <ActivityAction x:TypeArguments="scg:KeyValuePair(x:String, scg:List(s:String[]))">
                                                            <ActivityAction.Argument>
                                                              <DelegateInArgument x:TypeArguments="scg:KeyValuePair(x:String, scg:List(s:String[]))" Name="group" />
                                                            </ActivityAction.Argument>
                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_437">
                                                              <Sequence.Variables>
                                                                <Variable x:TypeArguments="x:Decimal" Name="qty" />
                                                              </Sequence.Variables>
                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_752">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="scg:List(s:String[])">[group.value]</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                              <If Condition="[not optimizerSuccess]" sap2010:WorkflowViewState.IdRef="If_388">
                                                                <If.Then>
                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_438">
                                                                    <Sequence.Variables>
                                                                      <Variable x:TypeArguments="x:Decimal" Name="calcTotal" />
                                                                      <Variable x:TypeArguments="x:Decimal" Name="tolPercent" />
                                                                      <Variable x:TypeArguments="x:String" Name="LessCharge" />
                                                                    </Sequence.Variables>
                                                                    <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_3">
                                                                      <TryCatch.Try>
                                                                        <If Condition="[miscValues(&quot;Comments&quot;).ToString.ToLower.Contains(&quot;approved&quot;)]" sap2010:WorkflowViewState.IdRef="If_451">
                                                                          <If.Then>
                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_520">
                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_926">
                                                                                <Assign.To>
                                                                                  <OutArgument x:TypeArguments="x:String">[tolPercentage]</OutArgument>
                                                                                </Assign.To>
                                                                                <Assign.Value>
                                                                                  <InArgument x:TypeArguments="x:String">100</InArgument>
                                                                                </Assign.Value>
                                                                              </Assign>
                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_927">
                                                                                <Assign.To>
                                                                                  <OutArgument x:TypeArguments="x:String">[tolAmt]</OutArgument>
                                                                                </Assign.To>
                                                                                <Assign.Value>
                                                                                  <InArgument x:TypeArguments="x:String">[ocrValues(2)]</InArgument>
                                                                                </Assign.Value>
                                                                              </Assign>
                                                                            </Sequence>
                                                                          </If.Then>
                                                                        </If>
                                                                      </TryCatch.Try>
                                                                      <TryCatch.Catches>
                                                                        <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_3">
                                                                          <ActivityAction x:TypeArguments="s:Exception">
                                                                            <ActivityAction.Argument>
                                                                              <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                            </ActivityAction.Argument>
                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_521">
                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_928">
                                                                                <Assign.To>
                                                                                  <OutArgument x:TypeArguments="x:String">[tolPercentage]</OutArgument>
                                                                                </Assign.To>
                                                                                <Assign.Value>
                                                                                  <InArgument x:TypeArguments="x:String">100</InArgument>
                                                                                </Assign.Value>
                                                                              </Assign>
                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_929">
                                                                                <Assign.To>
                                                                                  <OutArgument x:TypeArguments="x:String">[tolAmt]</OutArgument>
                                                                                </Assign.To>
                                                                                <Assign.Value>
                                                                                  <InArgument x:TypeArguments="x:String">100</InArgument>
                                                                                </Assign.Value>
                                                                              </Assign>
                                                                            </Sequence>
                                                                          </ActivityAction>
                                                                        </Catch>
                                                                      </TryCatch.Catches>
                                                                    </TryCatch>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_925">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:Boolean">[chargeExists]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_916">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:Decimal">[calcTotal]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:Decimal">[0]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_80" Values="[M3TotalTableRows]">
                                                                      <ActivityAction x:TypeArguments="s:String[]">
                                                                        <ActivityAction.Argument>
                                                                          <DelegateInArgument x:TypeArguments="s:String[]" Name="item" />
                                                                        </ActivityAction.Argument>
                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_518">
                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_917">
                                                                            <Assign.To>
                                                                              <OutArgument x:TypeArguments="s:String[]">[m3Values]</OutArgument>
                                                                            </Assign.To>
                                                                            <Assign.Value>
                                                                              <InArgument x:TypeArguments="s:String[]">[item]</InArgument>
                                                                            </Assign.Value>
                                                                          </Assign>
                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_918">
                                                                            <Assign.To>
                                                                              <OutArgument x:TypeArguments="x:Decimal">[calcTotal]</OutArgument>
                                                                            </Assign.To>
                                                                            <Assign.Value>
                                                                              <InArgument x:TypeArguments="x:Decimal">[calcTotal+ (Convert.toDecimal(m3Values(2))*Convert.toDecimal(m3Values(3)))]</InArgument>
                                                                            </Assign.Value>
                                                                          </Assign>
                                                                        </Sequence>
                                                                      </ActivityAction>
                                                                    </ForEach>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_919">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:Decimal">[tolPercent]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:Decimal">[Convert.toDecimal(calcTotal) * Convert.toDecimal(tolPercentage) *0.01D]</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <If Condition="[tolPercent &gt; Convert.toDecimal(tolAmt)]" sap2010:WorkflowViewState.IdRef="If_448">
                                                                      <If.Then>
                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_920">
                                                                          <Assign.To>
                                                                            <OutArgument x:TypeArguments="x:Decimal">[tolPercent]</OutArgument>
                                                                          </Assign.To>
                                                                          <Assign.Value>
                                                                            <InArgument x:TypeArguments="x:Decimal">[Convert.toDecimal(tolAmt)]</InArgument>
                                                                          </Assign.Value>
                                                                        </Assign>
                                                                      </If.Then>
                                                                    </If>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_921">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:String">[LessCharge]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:String">0</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <If Condition="[ocrValues(6) &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_450">
                                                                      <If.Then>
                                                                        <If Condition="[CInt(ocrValues(6)) &lt;&gt; 0 AND Convert.ToDecimal(ocrValues(6)) &lt; tolPercent]" sap2010:WorkflowViewState.IdRef="If_449">
                                                                          <If.Then>
                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_519">
                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_922">
                                                                                <Assign.To>
                                                                                  <OutArgument x:TypeArguments="x:String">[LessCharge]</OutArgument>
                                                                                </Assign.To>
                                                                                <Assign.Value>
                                                                                  <InArgument x:TypeArguments="x:String">[ocrValues(6)]</InArgument>
                                                                                </Assign.Value>
                                                                              </Assign>
                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_923">
                                                                                <Assign.To>
                                                                                  <OutArgument x:TypeArguments="x:Boolean">[chargeExists]</OutArgument>
                                                                                </Assign.To>
                                                                                <Assign.Value>
                                                                                  <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                                                                </Assign.Value>
                                                                              </Assign>
                                                                            </Sequence>
                                                                          </If.Then>
                                                                        </If>
                                                                      </If.Then>
                                                                    </If>
                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_695">
                                                                      <Assign.To>
                                                                        <OutArgument x:TypeArguments="x:Int32">[x]</OutArgument>
                                                                      </Assign.To>
                                                                      <Assign.Value>
                                                                        <InArgument x:TypeArguments="x:Int32">1</InArgument>
                                                                      </Assign.Value>
                                                                    </Assign>
                                                                    <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_64" Values="[M3TotalTableRows]">
                                                                      <ActivityAction x:TypeArguments="s:String[]">
                                                                        <ActivityAction.Argument>
                                                                          <DelegateInArgument x:TypeArguments="s:String[]" Name="m3Values" />
                                                                        </ActivityAction.Argument>
                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_404">
                                                                          <Sequence.Variables>
                                                                            <Variable x:TypeArguments="x:String" Name="FinAmt" />
                                                                          </Sequence.Variables>
                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_924">
                                                                            <Assign.To>
                                                                              <OutArgument x:TypeArguments="x:String">[FinAmt]</OutArgument>
                                                                            </Assign.To>
                                                                            <Assign.Value>
                                                                              <InArgument x:TypeArguments="x:String">[(Convert.toDecimal(ocrValues(2)) - Convert.toDecimal(LessCharge)).ToString]</InArgument>
                                                                            </Assign.Value>
                                                                          </Assign>
                                                                          <If Condition="[x=1]" sap2010:WorkflowViewState.IdRef="If_356">
                                                                            <If.Then>
                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_696">
                                                                                <Assign.To>
                                                                                  <OutArgument x:TypeArguments="x:String">[APIString]</OutArgument>
                                                                                </Assign.To>
                                                                                <Assign.Value>
                                                                                  <InArgument x:TypeArguments="x:String">["[["+m3Values(0)+","+m3Values(1)+","+m3Values(2)+","+m3Values(3)+","+FinAmt+","+ tolPercentage +"," + tolAmt +"]"]</InArgument>
                                                                                </Assign.Value>
                                                                              </Assign>
                                                                            </If.Then>
                                                                            <If.Else>
                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_697">
                                                                                <Assign.To>
                                                                                  <OutArgument x:TypeArguments="x:String">[APIString]</OutArgument>
                                                                                </Assign.To>
                                                                                <Assign.Value>
                                                                                  <InArgument x:TypeArguments="x:String">[APIString+",["+m3Values(0)+","+m3Values(1)+","+m3Values(2)+","+m3Values(3)+","+FinAmt+","+ tolPercentage +"," + tolAmt +"]"]</InArgument>
                                                                                </Assign.Value>
                                                                              </Assign>
                                                                            </If.Else>
                                                                          </If>
                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_698">
                                                                            <Assign.To>
                                                                              <OutArgument x:TypeArguments="x:Int32">[x]</OutArgument>
                                                                            </Assign.To>
                                                                            <Assign.Value>
                                                                              <InArgument x:TypeArguments="x:Int32">[x+1]</InArgument>
                                                                            </Assign.Value>
                                                                          </Assign>
                                                                        </Sequence>
                                                                      </ActivityAction>
                                                                    </ForEach>
                                                                    <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;ocrValues&quot;,ocrValues},{&quot;colemanAPI&quot;,colemanAPI},{&quot;APIString&quot;,APIString},{&quot;logfile&quot;,logfile}}]" ContinueOnError="True" DisplayName="Coleman Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_38" OutputArguments="[ColemanOutPut]" ResponseCode="[ColemanRespCode]" WorkflowFile="[projectPath+&quot;\CallColeman.xaml&quot;]" />
                                                                    <If Condition="[ColemanRespCode = 200]" sap2010:WorkflowViewState.IdRef="If_413">
                                                                      <If.Then>
                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_463">
                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_754">
                                                                            <Assign.To>
                                                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                            </Assign.To>
                                                                            <Assign.Value>
                                                                              <InArgument x:TypeArguments="x:String">[ColemanOutPut("comments").ToString()]</InArgument>
                                                                            </Assign.Value>
                                                                          </Assign>
                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_755">
                                                                            <Assign.To>
                                                                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                            </Assign.To>
                                                                            <Assign.Value>
                                                                              <InArgument x:TypeArguments="x:String">[ColemanOutPut("Status").ToString()]</InArgument>
                                                                            </Assign.Value>
                                                                          </Assign>
                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_756">
                                                                            <Assign.To>
                                                                              <OutArgument x:TypeArguments="njl:JToken">[httpOut]</OutArgument>
                                                                            </Assign.To>
                                                                            <Assign.Value>
                                                                              <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(ColemanOutPut("httpOut").ToString)]</InArgument>
                                                                            </Assign.Value>
                                                                          </Assign>
                                                                          <If Condition="[Status = &quot;Success&quot;]" sap2010:WorkflowViewState.IdRef="If_412">
                                                                            <If.Then>
                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_462">
                                                                                <If Condition="[httpOut.ToString.Contains(&quot;Not Allocated&quot;)]" sap2010:WorkflowViewState.IdRef="If_394">
                                                                                  <If.Then>
                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_444">
                                                                                      <If Condition="[CInt(ocrValues(6)) &lt;&gt; 0 OR CInt(ocrValues(5)) &lt;&gt; 0]" sap2010:WorkflowViewState.IdRef="If_393">
                                                                                        <If.Then>
                                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_443">
                                                                                            <Sequence.Variables>
                                                                                              <Variable x:TypeArguments="x:String" Name="colamt" />
                                                                                            </Sequence.Variables>
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_757">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="x:String">[colamt]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="x:String">[(Convert.toDecimal(ocrValues(2)) - Convert.toDecimal(ocrValues(6)) - Convert.toDecimal(ocrValues(5))).ToString()]</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_758">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="x:Int32">[x]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="x:Int32">1</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                            <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_71" Values="[M3TotalTableRows]">
                                                                                              <ActivityAction x:TypeArguments="s:String[]">
                                                                                                <ActivityAction.Argument>
                                                                                                  <DelegateInArgument x:TypeArguments="s:String[]" Name="item" />
                                                                                                </ActivityAction.Argument>
                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_441">
                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_759">
                                                                                                    <Assign.To>
                                                                                                      <OutArgument x:TypeArguments="s:String[]">[m3Values]</OutArgument>
                                                                                                    </Assign.To>
                                                                                                    <Assign.Value>
                                                                                                      <InArgument x:TypeArguments="s:String[]">[item]</InArgument>
                                                                                                    </Assign.Value>
                                                                                                  </Assign>
                                                                                                  <If Condition="[x=1]" sap2010:WorkflowViewState.IdRef="If_389">
                                                                                                    <If.Then>
                                                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_439">
                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_760">
                                                                                                          <Assign.To>
                                                                                                            <OutArgument x:TypeArguments="x:String">[APIString]</OutArgument>
                                                                                                          </Assign.To>
                                                                                                          <Assign.Value>
                                                                                                            <InArgument x:TypeArguments="x:String">["[["+m3Values(0)+","+m3Values(1)+","+m3Values(2)+","+m3Values(3)+","+colamt+","+ tolPercentage +"," + tolAmt +"]"]</InArgument>
                                                                                                          </Assign.Value>
                                                                                                        </Assign>
                                                                                                      </Sequence>
                                                                                                    </If.Then>
                                                                                                    <If.Else>
                                                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_440">
                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_761">
                                                                                                          <Assign.To>
                                                                                                            <OutArgument x:TypeArguments="x:String">[APIString]</OutArgument>
                                                                                                          </Assign.To>
                                                                                                          <Assign.Value>
                                                                                                            <InArgument x:TypeArguments="x:String">[APIString+",["+m3Values(0)+","+m3Values(1)+","+m3Values(2)+","+m3Values(3)+","+colamt+","+ tolPercentage +"," + tolAmt +"]"]</InArgument>
                                                                                                          </Assign.Value>
                                                                                                        </Assign>
                                                                                                      </Sequence>
                                                                                                    </If.Else>
                                                                                                  </If>
                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_762">
                                                                                                    <Assign.To>
                                                                                                      <OutArgument x:TypeArguments="x:Int32">[x]</OutArgument>
                                                                                                    </Assign.To>
                                                                                                    <Assign.Value>
                                                                                                      <InArgument x:TypeArguments="x:Int32">[x+1]</InArgument>
                                                                                                    </Assign.Value>
                                                                                                  </Assign>
                                                                                                </Sequence>
                                                                                              </ActivityAction>
                                                                                            </ForEach>
                                                                                            <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;ocrValues&quot;,ocrValues},{&quot;colemanAPI&quot;,colemanAPI},{&quot;APIString&quot;,APIString},{&quot;logfile&quot;,logfile}}]" ContinueOnError="True" DisplayName="Coleman Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_39" OutputArguments="[ColemanOutPut]" ResponseCode="[ColemanRespCode]" WorkflowFile="[projectPath+&quot;\CallColeman.xaml&quot;]" />
                                                                                            <If Condition="[ColemanRespCode = 200]" sap2010:WorkflowViewState.IdRef="If_392">
                                                                                              <If.Then>
                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_442">
                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_763">
                                                                                                    <Assign.To>
                                                                                                      <OutArgument x:TypeArguments="njl:JToken">[httpOut]</OutArgument>
                                                                                                    </Assign.To>
                                                                                                    <Assign.Value>
                                                                                                      <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(ColemanOutPut("httpOut").ToString)]</InArgument>
                                                                                                    </Assign.Value>
                                                                                                  </Assign>
                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_764">
                                                                                                    <Assign.To>
                                                                                                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                    </Assign.To>
                                                                                                    <Assign.Value>
                                                                                                      <InArgument x:TypeArguments="x:String">[ColemanOutPut("comments").ToString()]</InArgument>
                                                                                                    </Assign.Value>
                                                                                                  </Assign>
                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_765">
                                                                                                    <Assign.To>
                                                                                                      <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                                    </Assign.To>
                                                                                                    <Assign.Value>
                                                                                                      <InArgument x:TypeArguments="x:String">[ColemanOutPut("Status").ToString()]</InArgument>
                                                                                                    </Assign.Value>
                                                                                                  </Assign>
                                                                                                  <If Condition="[Status = &quot;Success&quot;]" sap2010:WorkflowViewState.IdRef="If_391">
                                                                                                    <If.Then>
                                                                                                      <If Condition="[NOT httpOut.ToString.Contains(&quot;Not Allocated&quot;)]" sap2010:WorkflowViewState.IdRef="If_390">
                                                                                                        <If.Then>
                                                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_473">
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_766">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:Boolean">[chargeExists]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_812">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:String">[colAmount]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:String">[colAmt]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                          </Sequence>
                                                                                                        </If.Then>
                                                                                                      </If>
                                                                                                    </If.Then>
                                                                                                  </If>
                                                                                                </Sequence>
                                                                                              </If.Then>
                                                                                            </If>
                                                                                          </Sequence>
                                                                                        </If.Then>
                                                                                      </If>
                                                                                    </Sequence>
                                                                                  </If.Then>
                                                                                  <If.Else>
                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_445">
                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_813">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:String">[colAmount]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:String">[ocrValues(2)]</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                    </Sequence>
                                                                                  </If.Else>
                                                                                </If>
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_767">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="njl:JToken">[httpOut]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(ColemanOutPut("httpOut").ToString)]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <If Condition="[(NOT (httpOut.ToString.Contains(&quot;Not Allocated&quot;) AND miscValues(&quot;createInvoiceIrrespectiveTolerance&quot;).ToString.ToLower = &quot;false&quot;)) OR miscValues(&quot;Comments&quot;).ToString.ToLower.Contains(&quot;approved&quot;)]" sap2010:WorkflowViewState.IdRef="If_411">
                                                                                  <If.Then>
                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_456">
                                                                                      <Sequence.Variables>
                                                                                        <Variable x:TypeArguments="x:String" Name="sino" />
                                                                                      </Sequence.Variables>
                                                                                      <Assign DisplayName="Assign ivdate" sap2010:WorkflowViewState.IdRef="Assign_768">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:String">[ivdate]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:String">[DateTime.ParseExact(ocrValues(1),"dd/MM/yyyy",Nothing).ToString("yyyyMMdd")]</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                      <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_769">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:String">[System.DateTime.Now.ToString("yyyyMMddHHmmssfff").tostring + " -"+ ocrValues(0) + "-" + division + "-" + vendorID]</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                      <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_1">
                                                                                        <TryCatch.Try>
                                                                                          <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_770">
                                                                                            <Assign.To>
                                                                                              <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                                                                                            </Assign.To>
                                                                                            <Assign.Value>
                                                                                              <InArgument x:TypeArguments="x:String">[correlationID.Substring(0,35)]</InArgument>
                                                                                            </Assign.Value>
                                                                                          </Assign>
                                                                                        </TryCatch.Try>
                                                                                        <TryCatch.Catches>
                                                                                          <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_1">
                                                                                            <ActivityAction x:TypeArguments="s:Exception">
                                                                                              <ActivityAction.Argument>
                                                                                                <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                              </ActivityAction.Argument>
                                                                                              <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_814">
                                                                                                <Assign.To>
                                                                                                  <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                                                                                                </Assign.To>
                                                                                                <Assign.Value>
                                                                                                  <InArgument x:TypeArguments="x:String">[correlationID]</InArgument>
                                                                                                </Assign.Value>
                                                                                              </Assign>
                                                                                            </ActivityAction>
                                                                                          </Catch>
                                                                                        </TryCatch.Catches>
                                                                                      </TryCatch>
                                                                                      <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_797">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:Boolean">[optimizerSuccess]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                      <If Condition="[miscValues(&quot;BusinessRuleAPResp&quot;).ToString.ToUpper() &lt;&gt; &quot;NA&quot;]" sap2010:WorkflowViewState.IdRef="If_445">
                                                                                        <If.Then>
                                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_516">
                                                                                            <Sequence.Variables>
                                                                                              <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="APRespRespDict" />
                                                                                              <Variable x:TypeArguments="x:Int32" Name="APRespRespCode" />
                                                                                            </Sequence.Variables>
                                                                                            <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;division&quot;,division},{&quot;company&quot;,company},{&quot;VendorID&quot;,vendorID}}]" ContinueOnError="True" DisplayName="AP resp Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_49" OutputArguments="[APRespRespDict]" ResponseCode="[APRespRespCode]" WorkflowFile="[projectPath+&quot;\APResp.xaml&quot;]" />
                                                                                            <If Condition="[APRespRespCode = 200]" sap2010:WorkflowViewState.IdRef="If_446">
                                                                                              <If.Then>
                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_914">
                                                                                                  <Assign.To>
                                                                                                    <OutArgument x:TypeArguments="x:String">[APResp]</OutArgument>
                                                                                                  </Assign.To>
                                                                                                  <Assign.Value>
                                                                                                    <InArgument x:TypeArguments="x:String">[CType(APRespRespDict("APResp"), String)]</InArgument>
                                                                                                  </Assign.Value>
                                                                                                </Assign>
                                                                                              </If.Then>
                                                                                            </If>
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_913">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="x:String">[authUser]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="x:String">[APResp]</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                          </Sequence>
                                                                                        </If.Then>
                                                                                        <If.Else>
                                                                                          <If Condition="[authUser.ToUpper() = &quot;SUPPLIERAUTHUSER&quot;]" sap2010:WorkflowViewState.IdRef="If_444">
                                                                                            <If.Then>
                                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_515">
                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_912">
                                                                                                  <Assign.To>
                                                                                                    <OutArgument x:TypeArguments="x:String">[authUser]</OutArgument>
                                                                                                  </Assign.To>
                                                                                                  <Assign.Value>
                                                                                                    <InArgument x:TypeArguments="x:String">[vendorResult(1)]</InArgument>
                                                                                                  </Assign.Value>
                                                                                                </Assign>
                                                                                              </Sequence>
                                                                                            </If.Then>
                                                                                          </If>
                                                                                        </If.Else>
                                                                                      </If>
                                                                                      <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;bkid&quot;,bkid},{&quot;authUser&quot;,authUser},{&quot;cuam&quot;,ocrValues(2).Replace(&quot;,&quot;,&quot;&quot;)},{&quot;pyme&quot;,pyme},{&quot;tepy&quot;,tepy},{&quot;cucd&quot;,cucd},{&quot;sino&quot;,ocrValues(0)},{&quot;division&quot;,division},{&quot;ivdate&quot;,ivdate},{&quot;SupplierNo&quot;,VendorID},{&quot;correlationID&quot;,correlationID},{&quot;discountTerms&quot;,discountTerms},{&quot;miscValues&quot;,miscValues}}]" ContinueOnError="True" DisplayName="Add Head Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_40" OutputArguments="[AddHeadOcrWorkflowOutput]" ResponseCode="[AddHeadOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\AddHead.xaml&quot;]" />
                                                                                      <If Condition="[AddHeadOcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_407">
                                                                                        <If.Then>
                                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_455">
                                                                                            <Sequence.Variables>
                                                                                              <Variable x:TypeArguments="scg:List(s:String[])" Name="M3TotalTableRowsAndColeman" />
                                                                                            </Sequence.Variables>
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_771">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="x:String">[inbnValue]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="x:String">[CType(AddHeadOcrWorkflowOutput("inbnValue"), String)]</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_772">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="x:String">[CType(AddHeadOcrWorkflowOutput("Status"), String)]</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_773">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="x:String">[CType(AddHeadOcrWorkflowOutput("commentStatus"), String)]</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                            <If Condition="[inbnValue &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_406">
                                                                                              <If.Then>
                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_454">
                                                                                                  <Sequence.Variables>
                                                                                                    <Variable x:TypeArguments="iru:ResponseObject" Name="resp" />
                                                                                                    <Variable x:TypeArguments="x:String" Name="diffamt" />
                                                                                                    <Variable x:TypeArguments="x:String" Name="includeDistribution" />
                                                                                                    <Variable x:TypeArguments="x:Boolean" Name="chFound" />
                                                                                                    <Variable x:TypeArguments="x:Decimal" Name="lineAmt" />
                                                                                                    <Variable x:TypeArguments="x:String" Name="chargeDiff" />
                                                                                                  </Sequence.Variables>
                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_774">
                                                                                                    <Assign.To>
                                                                                                      <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRowsAndColeman]</OutArgument>
                                                                                                    </Assign.To>
                                                                                                    <Assign.Value>
                                                                                                      <InArgument x:TypeArguments="scg:List(s:String[])">[new list(OF string())]</InArgument>
                                                                                                    </Assign.Value>
                                                                                                  </Assign>
                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_829">
                                                                                                    <Assign.To>
                                                                                                      <OutArgument x:TypeArguments="x:Decimal">[lineAmt]</OutArgument>
                                                                                                    </Assign.To>
                                                                                                    <Assign.Value>
                                                                                                      <InArgument x:TypeArguments="x:Decimal">[0]</InArgument>
                                                                                                    </Assign.Value>
                                                                                                  </Assign>
                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_830">
                                                                                                    <Assign.To>
                                                                                                      <OutArgument x:TypeArguments="x:Decimal">[qty]</OutArgument>
                                                                                                    </Assign.To>
                                                                                                    <Assign.Value>
                                                                                                      <InArgument x:TypeArguments="x:Decimal">[0]</InArgument>
                                                                                                    </Assign.Value>
                                                                                                  </Assign>
                                                                                                  <If Condition="[httpOut.ToString.Contains(&quot;Not Allocated&quot;)]" sap2010:WorkflowViewState.IdRef="If_424">
                                                                                                    <If.Then>
                                                                                                      <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_75" Values="[M3TotalTableRows]">
                                                                                                        <ActivityAction x:TypeArguments="s:String[]">
                                                                                                          <ActivityAction.Argument>
                                                                                                            <DelegateInArgument x:TypeArguments="s:String[]" Name="rows" />
                                                                                                          </ActivityAction.Argument>
                                                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_480">
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_823">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:Decimal">[lineAmt]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:Decimal">[lineAmt +convert.ToDecimal(rows(2))*convert.ToDecimal(rows(3))]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_824">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:Decimal">[qty]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:Decimal">[qty + convert.ToDecimal(rows(3))]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_46" MethodName="Add">
                                                                                                              <InvokeMethod.TargetObject>
                                                                                                                <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRowsAndColeman]</InArgument>
                                                                                                              </InvokeMethod.TargetObject>
                                                                                                              <InArgument x:TypeArguments="s:String[]">[rows]</InArgument>
                                                                                                            </InvokeMethod>
                                                                                                          </Sequence>
                                                                                                        </ActivityAction>
                                                                                                      </ForEach>
                                                                                                    </If.Then>
                                                                                                    <If.Else>
                                                                                                      <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_79" Values="[httpOut]">
                                                                                                        <ActivityAction x:TypeArguments="njl:JToken">
                                                                                                          <ActivityAction.Argument>
                                                                                                            <DelegateInArgument x:TypeArguments="njl:JToken" Name="out" />
                                                                                                          </ActivityAction.Argument>
                                                                                                          <ForEach x:TypeArguments="s:String[]" DisplayName="ForEach&lt;String[]&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_78" Values="[M3TotalTableRows]">
                                                                                                            <ActivityAction x:TypeArguments="s:String[]">
                                                                                                              <ActivityAction.Argument>
                                                                                                                <DelegateInArgument x:TypeArguments="s:String[]" Name="rows" />
                                                                                                              </ActivityAction.Argument>
                                                                                                              <If Condition="[out(&quot;line_number&quot;).ToString=rows(0) And out(&quot;rcd_number&quot;).ToString=rows(1)]" sap2010:WorkflowViewState.IdRef="If_425">
                                                                                                                <If.Then>
                                                                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_482">
                                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_827">
                                                                                                                      <Assign.To>
                                                                                                                        <OutArgument x:TypeArguments="x:Decimal">[lineAmt]</OutArgument>
                                                                                                                      </Assign.To>
                                                                                                                      <Assign.Value>
                                                                                                                        <InArgument x:TypeArguments="x:Decimal">[lineAmt +convert.ToDecimal(rows(2))*convert.ToDecimal(rows(3))]</InArgument>
                                                                                                                      </Assign.Value>
                                                                                                                    </Assign>
                                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_828">
                                                                                                                      <Assign.To>
                                                                                                                        <OutArgument x:TypeArguments="x:Decimal">[qty]</OutArgument>
                                                                                                                      </Assign.To>
                                                                                                                      <Assign.Value>
                                                                                                                        <InArgument x:TypeArguments="x:Decimal">[qty + convert.ToDecimal(rows(3))]</InArgument>
                                                                                                                      </Assign.Value>
                                                                                                                    </Assign>
                                                                                                                    <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_48" MethodName="Add">
                                                                                                                      <InvokeMethod.TargetObject>
                                                                                                                        <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRowsAndColeman]</InArgument>
                                                                                                                      </InvokeMethod.TargetObject>
                                                                                                                      <InArgument x:TypeArguments="s:String[]">[rows]</InArgument>
                                                                                                                    </InvokeMethod>
                                                                                                                  </Sequence>
                                                                                                                </If.Then>
                                                                                                              </If>
                                                                                                            </ActivityAction>
                                                                                                          </ForEach>
                                                                                                        </ActivityAction>
                                                                                                      </ForEach>
                                                                                                    </If.Else>
                                                                                                  </If>
                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_805">
                                                                                                    <Assign.To>
                                                                                                      <OutArgument x:TypeArguments="x:String">[diffamt]</OutArgument>
                                                                                                    </Assign.To>
                                                                                                    <Assign.Value>
                                                                                                      <InArgument x:TypeArguments="x:String">[((convert.ToDecimal(colAmount) - lineAmt)/qty).ToString]</InArgument>
                                                                                                    </Assign.Value>
                                                                                                  </Assign>
                                                                                                  <If Condition="[chargeExists]" sap2010:WorkflowViewState.IdRef="If_422">
                                                                                                    <If.Then>
                                                                                                      <Sequence DisplayName="Amount is matched with subtotal Sequence" sap2010:WorkflowViewState.IdRef="Sequence_477">
                                                                                                        <If Condition="[ocrValues(6) &lt;&gt; &quot;0&quot;]" sap2010:WorkflowViewState.IdRef="If_419">
                                                                                                          <If.Then>
                                                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_476">
                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_819">
                                                                                                                <Assign.To>
                                                                                                                  <OutArgument x:TypeArguments="x:String">[diffamt]</OutArgument>
                                                                                                                </Assign.To>
                                                                                                                <Assign.Value>
                                                                                                                  <InArgument x:TypeArguments="x:String">[((convert.ToDecimal(ocrValues(2)) - convert.ToDecimal(Ocrvalues(6)) - lineAmt)/qty).ToString]</InArgument>
                                                                                                                </Assign.Value>
                                                                                                              </Assign>
                                                                                                            </Sequence>
                                                                                                          </If.Then>
                                                                                                        </If>
                                                                                                      </Sequence>
                                                                                                    </If.Then>
                                                                                                    <If.Else>
                                                                                                      <Sequence DisplayName="Total Amount is within Tol" sap2010:WorkflowViewState.IdRef="Sequence_479">
                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_820">
                                                                                                          <Assign.To>
                                                                                                            <OutArgument x:TypeArguments="x:String">[diffamt]</OutArgument>
                                                                                                          </Assign.To>
                                                                                                          <Assign.Value>
                                                                                                            <InArgument x:TypeArguments="x:String">[((convert.ToDecimal(colAmount) - lineAmt)/qty).ToString]</InArgument>
                                                                                                          </Assign.Value>
                                                                                                        </Assign>
                                                                                                        <If Condition="[CInt(ocrValues(6)) &lt;&gt; 0]" DisplayName="Matched with total but there is charge" sap2010:WorkflowViewState.IdRef="If_421">
                                                                                                          <If.Then>
                                                                                                            <If Condition="[Convert.ToDecimal(ocrValues(6)) - convert.ToDecimal(diffAmt) * Convert.ToDecimal(qty) &lt; 1]" DisplayName="If diff amount = charge in invoice" sap2010:WorkflowViewState.IdRef="If_420">
                                                                                                              <If.Then>
                                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_478">
                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_821">
                                                                                                                    <Assign.To>
                                                                                                                      <OutArgument x:TypeArguments="x:String">[diffamt]</OutArgument>
                                                                                                                    </Assign.To>
                                                                                                                    <Assign.Value>
                                                                                                                      <InArgument x:TypeArguments="x:String">0</InArgument>
                                                                                                                    </Assign.Value>
                                                                                                                  </Assign>
                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_822">
                                                                                                                    <Assign.To>
                                                                                                                      <OutArgument x:TypeArguments="x:Boolean">[ChargeExists]</OutArgument>
                                                                                                                    </Assign.To>
                                                                                                                    <Assign.Value>
                                                                                                                      <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                                                                                                    </Assign.Value>
                                                                                                                  </Assign>
                                                                                                                </Sequence>
                                                                                                              </If.Then>
                                                                                                            </If>
                                                                                                          </If.Then>
                                                                                                        </If>
                                                                                                      </Sequence>
                                                                                                    </If.Else>
                                                                                                  </If>
                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_806">
                                                                                                    <Assign.To>
                                                                                                      <OutArgument x:TypeArguments="x:String">[includeDistribution]</OutArgument>
                                                                                                    </Assign.To>
                                                                                                    <Assign.Value>
                                                                                                      <InArgument x:TypeArguments="x:String">[miscValues("includeDistribution").ToString]</InArgument>
                                                                                                    </Assign.Value>
                                                                                                  </Assign>
                                                                                                  <If Condition="[httpOut.ToString.Contains(&quot;Not Allocated&quot;)]" sap2010:WorkflowViewState.IdRef="If_427">
                                                                                                    <If.Then>
                                                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_483">
                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_831">
                                                                                                          <Assign.To>
                                                                                                            <OutArgument x:TypeArguments="x:String">[diffamt]</OutArgument>
                                                                                                          </Assign.To>
                                                                                                          <Assign.Value>
                                                                                                            <InArgument x:TypeArguments="x:String">[((convert.ToDecimal(ocrValues(2)) - convert.ToDecimal(Ocrvalues(6)) - convert.ToDecimal(Ocrvalues(5)) - lineAmt)/qty).ToString]</InArgument>
                                                                                                          </Assign.Value>
                                                                                                        </Assign>
                                                                                                        <If Condition="[CInt(ocrValues(6)) &lt;&gt; 0]" sap2010:WorkflowViewState.IdRef="If_426">
                                                                                                          <If.Then>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_832">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:Boolean">[ChargeExists]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                          </If.Then>
                                                                                                        </If>
                                                                                                      </Sequence>
                                                                                                    </If.Then>
                                                                                                  </If>
                                                                                                  <If Condition="[diffamt &lt;&gt; &quot;0&quot; AND includeDistribution.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_418">
                                                                                                    <If.Then>
                                                                                                      <ForEach x:TypeArguments="x:Int32" DisplayName="ForEach&lt;Int32&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_74" Values="[Enumerable.Range(0,M3TotalTableRowsAndColeman.count)]">
                                                                                                        <ActivityAction x:TypeArguments="x:Int32">
                                                                                                          <ActivityAction.Argument>
                                                                                                            <DelegateInArgument x:TypeArguments="x:Int32" Name="i" />
                                                                                                          </ActivityAction.Argument>
                                                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_471">
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_809">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:String">[M3TotalTableRowsAndColeman(i)(2)]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:String">[(convert.ToDecimal(M3TotalTableRowsAndColeman(i)(2)) + convert.Todecimal(diffamt)).ToString]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                          </Sequence>
                                                                                                        </ActivityAction>
                                                                                                      </ForEach>
                                                                                                    </If.Then>
                                                                                                  </If>
                                                                                                  <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;M3TotalTableRows&quot;,M3TotalTableRowsAndColeman},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;division&quot;,division},{&quot;inbnValue&quot;,inbnValue}}]" ContinueOnError="True" DisplayName="Add Lines Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_41" OutputArguments="[AddLineOcrWorkflowOutput]" ResponseCode="[AddLineOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\AddLine.xaml&quot;]" />
                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_883">
                                                                                                    <Assign.To>
                                                                                                      <OutArgument x:TypeArguments="x:String">[chargeDiff]</OutArgument>
                                                                                                    </Assign.To>
                                                                                                    <Assign.Value>
                                                                                                      <InArgument x:TypeArguments="x:String">0</InArgument>
                                                                                                    </Assign.Value>
                                                                                                  </Assign>
                                                                                                  <If Condition="[ocrValues(6) &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_397">
                                                                                                    <If.Then>
                                                                                                      <If Condition="[chargeExists AND CInt(ocrValues(6)) &lt;&gt; 0]" sap2010:WorkflowViewState.IdRef="If_396">
                                                                                                        <If.Then>
                                                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_507">
                                                                                                            <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;M3TotalTableRows&quot;,M3TotalTableRowsAndColeman},{&quot;division&quot;,division},{&quot;inbnValue&quot;,inbnValue},{&quot;charge&quot;,ocrvalues(6)},{&quot;chargeCode&quot;,chargeCode},{&quot;emptyCharge&quot;,False}}]" ContinueOnError="True" DisplayName="Add Charges Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_42" OutputArguments="[AddLineOcrWorkflowOutput]" ResponseCode="[AddLineOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\AddCharge.xaml&quot;]" />
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_884">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:String">[chargeDiff]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:String">[Ctype(AddLineOcrWorkflowOutput("chargeDiff"),String)]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                          </Sequence>
                                                                                                        </If.Then>
                                                                                                      </If>
                                                                                                    </If.Then>
                                                                                                  </If>
                                                                                                  <If Condition="[ocrValues(5) &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_399">
                                                                                                    <If.Then>
                                                                                                      <If Condition="[chargeExists AND CInt(ocrValues(5)) &lt;&gt; 0]" sap2010:WorkflowViewState.IdRef="If_398">
                                                                                                        <If.Then>
                                                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_446">
                                                                                                            <Sequence.Variables>
                                                                                                              <Variable x:TypeArguments="x:String" Name="vat" />
                                                                                                              <Variable x:TypeArguments="x:String" Name="vatCode" />
                                                                                                            </Sequence.Variables>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_775">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:String">[vat]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:String">[ocrValues(5)]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_882">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:String">[vatCode]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:String">[miscValues("vatCodeConfig").ToString]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <If Condition="[miscValues(&quot;vatCodeConfig&quot;).ToString = &quot;NA&quot;]" sap2010:WorkflowViewState.IdRef="If_438">
                                                                                                              <If.Then>
                                                                                                                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_25" Response="[resp]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                                                                                  <iai:IONAPIRequestWizard.Headers>
                                                                                                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                        <x:String>Accept</x:String>
                                                                                                                      </scg:List>
                                                                                                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                        <x:String>application/json</x:String>
                                                                                                                      </scg:List>
                                                                                                                    </scg:List>
                                                                                                                  </iai:IONAPIRequestWizard.Headers>
                                                                                                                  <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                        <x:String>INBN</x:String>
                                                                                                                        <x:String>RDTP</x:String>
                                                                                                                        <x:String>DIVI</x:String>
                                                                                                                        <x:String>GLAM</x:String>
                                                                                                                      </scg:List>
                                                                                                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                        <x:String>inbnValue</x:String>
                                                                                                                        <x:String>3</x:String>
                                                                                                                        <x:String>division</x:String>
                                                                                                                        <x:String>vat</x:String>
                                                                                                                      </scg:List>
                                                                                                                    </scg:List>
                                                                                                                  </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                </iai:IONAPIRequestWizard>
                                                                                                              </If.Then>
                                                                                                              <If.Else>
                                                                                                                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_26" Response="[resp]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                                                                                  <iai:IONAPIRequestWizard.Headers>
                                                                                                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                        <x:String>Accept</x:String>
                                                                                                                      </scg:List>
                                                                                                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                        <x:String>application/json</x:String>
                                                                                                                      </scg:List>
                                                                                                                    </scg:List>
                                                                                                                  </iai:IONAPIRequestWizard.Headers>
                                                                                                                  <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                      <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                                                        <x:String>INBN</x:String>
                                                                                                                        <x:String>RDTP</x:String>
                                                                                                                        <x:String>DIVI</x:String>
                                                                                                                        <x:String>VTA1</x:String>
                                                                                                                        <x:String>VTCD</x:String>
                                                                                                                      </scg:List>
                                                                                                                      <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                                                        <x:String>inbnValue</x:String>
                                                                                                                        <x:String>3</x:String>
                                                                                                                        <x:String>division</x:String>
                                                                                                                        <x:String>vat</x:String>
                                                                                                                        <x:String>vatCode</x:String>
                                                                                                                      </scg:List>
                                                                                                                    </scg:List>
                                                                                                                  </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                </iai:IONAPIRequestWizard>
                                                                                                              </If.Else>
                                                                                                            </If>
                                                                                                          </Sequence>
                                                                                                        </If.Then>
                                                                                                      </If>
                                                                                                    </If.Then>
                                                                                                  </If>
                                                                                                  <If Condition="[AddLineOcrWorkflowStatus= 200]" sap2010:WorkflowViewState.IdRef="If_402">
                                                                                                    <If.Then>
                                                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_505">
                                                                                                        <Sequence.Variables>
                                                                                                          <Variable x:TypeArguments="iru:ResponseObject" Name="resp1" />
                                                                                                        </Sequence.Variables>
                                                                                                        <If Condition="[CInt(ocrValues(6)) = 0 AND NOT chargeExists]" sap2010:WorkflowViewState.IdRef="If_452">
                                                                                                          <If.Then>
                                                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_522">
                                                                                                              <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;M3TotalTableRows&quot;,M3TotalTableRowsAndColeman},{&quot;division&quot;,division},{&quot;inbnValue&quot;,inbnValue},{&quot;charge&quot;,ocrvalues(6)},{&quot;chargeCode&quot;,chargeCode},{&quot;emptyCharge&quot;,True}}]" ContinueOnError="True" DisplayName="Add Charges Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_50" OutputArguments="[AddLineOcrWorkflowOutput]" ResponseCode="[AddLineOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\AddCharge.xaml&quot;]" />
                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_930">
                                                                                                                <Assign.To>
                                                                                                                  <OutArgument x:TypeArguments="x:String">[chargeDiff]</OutArgument>
                                                                                                                </Assign.To>
                                                                                                                <Assign.Value>
                                                                                                                  <InArgument x:TypeArguments="x:String">[Ctype(AddLineOcrWorkflowOutput("chargeDiff"),String)]</InArgument>
                                                                                                                </Assign.Value>
                                                                                                              </Assign>
                                                                                                            </Sequence>
                                                                                                          </If.Then>
                                                                                                        </If>
                                                                                                        <If Condition="[Math.abs(Convert.ToDecimal(diffamt)*Convert.ToDecimal(qty) + Convert.ToDecimal(chargeDiff)) &gt; 0]" sap2010:WorkflowViewState.IdRef="If_439">
                                                                                                          <If.Then>
                                                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_511">
                                                                                                              <Sequence.Variables>
                                                                                                                <Variable x:TypeArguments="x:String" Name="diffAmt1" />
                                                                                                              </Sequence.Variables>
                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_892">
                                                                                                                <Assign.To>
                                                                                                                  <OutArgument x:TypeArguments="x:String">[diffAmt1]</OutArgument>
                                                                                                                </Assign.To>
                                                                                                                <Assign.Value>
                                                                                                                  <InArgument x:TypeArguments="x:String">[(Convert.ToDecimal(diffamt)*Convert.ToDecimal(qty) + Convert.ToDecimal(chargeDiff)).ToString]</InArgument>
                                                                                                                </Assign.Value>
                                                                                                              </Assign>
                                                                                                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for adding AddInfo" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_28" Response="[resp1]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddAddInfo&quot;]">
                                                                                                                <iai:IONAPIRequestWizard.Headers>
                                                                                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                      <x:String>Accept</x:String>
                                                                                                                    </scg:List>
                                                                                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                      <x:String>application/json</x:String>
                                                                                                                    </scg:List>
                                                                                                                  </scg:List>
                                                                                                                </iai:IONAPIRequestWizard.Headers>
                                                                                                                <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                      <x:String>INBN</x:String>
                                                                                                                      <x:String>PEXN</x:String>
                                                                                                                      <x:String>PEXI</x:String>
                                                                                                                      <x:String>DIVI</x:String>
                                                                                                                    </scg:List>
                                                                                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                      <x:String>inbnValue</x:String>
                                                                                                                      <x:String>414</x:String>
                                                                                                                      <x:String>diffAmt1</x:String>
                                                                                                                      <x:String>division</x:String>
                                                                                                                    </scg:List>
                                                                                                                  </scg:List>
                                                                                                                </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                              </iai:IONAPIRequestWizard>
                                                                                                            </Sequence>
                                                                                                          </If.Then>
                                                                                                        </If>
                                                                                                        <If Condition="[miscValues(&quot;AutomateValidation&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_437">
                                                                                                          <If.Then>
                                                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_512">
                                                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_449">
                                                                                                                <Sequence.Variables>
                                                                                                                  <Variable x:TypeArguments="x:Int32" Name="loopBreak" />
                                                                                                                  <Variable x:TypeArguments="x:String" Name="supa" />
                                                                                                                  <Variable x:TypeArguments="x:String" Name="inyr" />
                                                                                                                </Sequence.Variables>
                                                                                                                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" Response="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_16" StatusCode="[validateStatus]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS455MI/ValidByBatchNo&quot;]">
                                                                                                                  <iai:IONAPIRequestWizard.Headers>
                                                                                                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                        <x:String>Accept</x:String>
                                                                                                                      </scg:List>
                                                                                                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                        <x:String>application/json</x:String>
                                                                                                                      </scg:List>
                                                                                                                    </scg:List>
                                                                                                                  </iai:IONAPIRequestWizard.Headers>
                                                                                                                  <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                        <x:String>INBN</x:String>
                                                                                                                        <x:String>DIVI</x:String>
                                                                                                                      </scg:List>
                                                                                                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                        <x:String>inbnValue</x:String>
                                                                                                                        <x:String>division</x:String>
                                                                                                                      </scg:List>
                                                                                                                    </scg:List>
                                                                                                                  </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                </iai:IONAPIRequestWizard>
                                                                                                                <Delay Duration="00:00:02" sap2010:WorkflowViewState.IdRef="Delay_4" />
                                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_776">
                                                                                                                  <Assign.To>
                                                                                                                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                                  </Assign.To>
                                                                                                                  <Assign.Value>
                                                                                                                    <InArgument x:TypeArguments="x:String">Invoice is successfully created and validated</InArgument>
                                                                                                                  </Assign.Value>
                                                                                                                </Assign>
                                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_778">
                                                                                                                  <Assign.To>
                                                                                                                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                                                  </Assign.To>
                                                                                                                  <Assign.Value>
                                                                                                                    <InArgument x:TypeArguments="x:String">SUCCESS</InArgument>
                                                                                                                  </Assign.Value>
                                                                                                                </Assign>
                                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_910">
                                                                                                                  <Assign.To>
                                                                                                                    <OutArgument x:TypeArguments="x:String">[sino]</OutArgument>
                                                                                                                  </Assign.To>
                                                                                                                  <Assign.Value>
                                                                                                                    <InArgument x:TypeArguments="x:String">[ocrValues(0)]</InArgument>
                                                                                                                  </Assign.Value>
                                                                                                                </Assign>
                                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_911">
                                                                                                                  <Assign.To>
                                                                                                                    <OutArgument x:TypeArguments="x:String">[inyr]</OutArgument>
                                                                                                                  </Assign.To>
                                                                                                                  <Assign.Value>
                                                                                                                    <InArgument x:TypeArguments="x:String">[ivdate.Substring(0,4)]</InArgument>
                                                                                                                  </Assign.Value>
                                                                                                                </Assign>
                                                                                                                <If Condition="[validateStatus = 200 AND NOT httpOut.ToString.Contains(&quot;Not Allocated&quot;)]" sap2010:WorkflowViewState.IdRef="If_401">
                                                                                                                  <If.Then>
                                                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_513">
                                                                                                                      <If Condition="[miscValues(&quot;AutomateApproval&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_443">
                                                                                                                        <If.Then>
                                                                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_514">
                                                                                                                            <InvokeMethod DisplayName="inbn value InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_49" MethodName="Add">
                                                                                                                              <InvokeMethod.TargetObject>
                                                                                                                                <InArgument x:TypeArguments="scg:List(x:String)">[approvalList]</InArgument>
                                                                                                                              </InvokeMethod.TargetObject>
                                                                                                                              <InArgument x:TypeArguments="x:String">[InbnValue]</InArgument>
                                                                                                                            </InvokeMethod>
                                                                                                                            <InvokeMethod DisplayName="sino InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_50" MethodName="Add">
                                                                                                                              <InvokeMethod.TargetObject>
                                                                                                                                <InArgument x:TypeArguments="scg:List(x:String)">[approvalList]</InArgument>
                                                                                                                              </InvokeMethod.TargetObject>
                                                                                                                              <InArgument x:TypeArguments="x:String">[sino]</InArgument>
                                                                                                                            </InvokeMethod>
                                                                                                                            <InvokeMethod DisplayName="vendorID InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_51" MethodName="Add">
                                                                                                                              <InvokeMethod.TargetObject>
                                                                                                                                <InArgument x:TypeArguments="scg:List(x:String)">[approvalList]</InArgument>
                                                                                                                              </InvokeMethod.TargetObject>
                                                                                                                              <InArgument x:TypeArguments="x:String">[vendorID]</InArgument>
                                                                                                                            </InvokeMethod>
                                                                                                                            <InvokeMethod DisplayName="year InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_52" MethodName="Add">
                                                                                                                              <InvokeMethod.TargetObject>
                                                                                                                                <InArgument x:TypeArguments="scg:List(x:String)">[approvalList]</InArgument>
                                                                                                                              </InvokeMethod.TargetObject>
                                                                                                                              <InArgument x:TypeArguments="x:String">[inyr]</InArgument>
                                                                                                                            </InvokeMethod>
                                                                                                                            <InvokeMethod DisplayName="divi InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_53" MethodName="Add">
                                                                                                                              <InvokeMethod.TargetObject>
                                                                                                                                <InArgument x:TypeArguments="scg:List(x:String)">[approvalList]</InArgument>
                                                                                                                              </InvokeMethod.TargetObject>
                                                                                                                              <InArgument x:TypeArguments="x:String">[division]</InArgument>
                                                                                                                            </InvokeMethod>
                                                                                                                          </Sequence>
                                                                                                                        </If.Then>
                                                                                                                      </If>
                                                                                                                    </Sequence>
                                                                                                                  </If.Then>
                                                                                                                  <If.Else>
                                                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_448">
                                                                                                                      <If Condition="[httpOut.ToString.Contains(&quot;Not Allocated&quot;)]" sap2010:WorkflowViewState.IdRef="If_429">
                                                                                                                        <If.Then>
                                                                                                                          <If Condition="[validateStatus = 200]" sap2010:WorkflowViewState.IdRef="If_428">
                                                                                                                            <If.Then>
                                                                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_485">
                                                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_837">
                                                                                                                                  <Assign.To>
                                                                                                                                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                                                  </Assign.To>
                                                                                                                                  <Assign.Value>
                                                                                                                                    <InArgument x:TypeArguments="x:String">["Invoice created and validated in the M3 but amount in invoice is not matched with M3. please verify the amounts."]</InArgument>
                                                                                                                                  </Assign.Value>
                                                                                                                                </Assign>
                                                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_838">
                                                                                                                                  <Assign.To>
                                                                                                                                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                                                                  </Assign.To>
                                                                                                                                  <Assign.Value>
                                                                                                                                    <InArgument x:TypeArguments="x:String">SUCCESS</InArgument>
                                                                                                                                  </Assign.Value>
                                                                                                                                </Assign>
                                                                                                                              </Sequence>
                                                                                                                            </If.Then>
                                                                                                                            <If.Else>
                                                                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_491">
                                                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_849">
                                                                                                                                  <Assign.To>
                                                                                                                                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                                                  </Assign.To>
                                                                                                                                  <Assign.Value>
                                                                                                                                    <InArgument x:TypeArguments="x:String">Invoice created in the M3 but amount in invoice is not matched with M3. please verify the amounts. Error occured while validating the invoice.</InArgument>
                                                                                                                                  </Assign.Value>
                                                                                                                                </Assign>
                                                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_850">
                                                                                                                                  <Assign.To>
                                                                                                                                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                                                                  </Assign.To>
                                                                                                                                  <Assign.Value>
                                                                                                                                    <InArgument x:TypeArguments="x:String">SUCCESS</InArgument>
                                                                                                                                  </Assign.Value>
                                                                                                                                </Assign>
                                                                                                                              </Sequence>
                                                                                                                            </If.Else>
                                                                                                                          </If>
                                                                                                                        </If.Then>
                                                                                                                        <If.Else>
                                                                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_492">
                                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_851">
                                                                                                                              <Assign.To>
                                                                                                                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                                              </Assign.To>
                                                                                                                              <Assign.Value>
                                                                                                                                <InArgument x:TypeArguments="x:String">Invoice Header and lines are created. Error occured while validating the invoice.</InArgument>
                                                                                                                              </Assign.Value>
                                                                                                                            </Assign>
                                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_852">
                                                                                                                              <Assign.To>
                                                                                                                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                                                              </Assign.To>
                                                                                                                              <Assign.Value>
                                                                                                                                <InArgument x:TypeArguments="x:String">SUCCESS</InArgument>
                                                                                                                              </Assign.Value>
                                                                                                                            </Assign>
                                                                                                                          </Sequence>
                                                                                                                        </If.Else>
                                                                                                                      </If>
                                                                                                                    </Sequence>
                                                                                                                  </If.Else>
                                                                                                                </If>
                                                                                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_138" Line="[commentStatus]" Source="[logfile]" />
                                                                                                              </Sequence>
                                                                                                            </Sequence>
                                                                                                          </If.Then>
                                                                                                          <If.Else>
                                                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_506">
                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_880">
                                                                                                                <Assign.To>
                                                                                                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                                </Assign.To>
                                                                                                                <Assign.Value>
                                                                                                                  <InArgument x:TypeArguments="x:String">Invoice and Lines created</InArgument>
                                                                                                                </Assign.Value>
                                                                                                              </Assign>
                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_881">
                                                                                                                <Assign.To>
                                                                                                                  <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                                                </Assign.To>
                                                                                                                <Assign.Value>
                                                                                                                  <InArgument x:TypeArguments="x:String">SUCCESS</InArgument>
                                                                                                                </Assign.Value>
                                                                                                              </Assign>
                                                                                                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_153" Line="[commentStatus]" Source="[logfile]" />
                                                                                                            </Sequence>
                                                                                                          </If.Else>
                                                                                                        </If>
                                                                                                      </Sequence>
                                                                                                    </If.Then>
                                                                                                    <If.Else>
                                                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_450">
                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_781">
                                                                                                          <Assign.To>
                                                                                                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                          </Assign.To>
                                                                                                          <Assign.Value>
                                                                                                            <InArgument x:TypeArguments="x:String">Error while adding the lines</InArgument>
                                                                                                          </Assign.Value>
                                                                                                        </Assign>
                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_782">
                                                                                                          <Assign.To>
                                                                                                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                                          </Assign.To>
                                                                                                          <Assign.Value>
                                                                                                            <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                                                          </Assign.Value>
                                                                                                        </Assign>
                                                                                                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_139" Line="[commentStatus]" Source="[logfile]" />
                                                                                                      </Sequence>
                                                                                                    </If.Else>
                                                                                                  </If>
                                                                                                  <If Condition="[approvalRequired]" sap2010:WorkflowViewState.IdRef="If_405">
                                                                                                    <If.Then>
                                                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_453">
                                                                                                        <Sequence.Variables>
                                                                                                          <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="ApprovalOcrWorkflowOutput" />
                                                                                                          <Variable x:TypeArguments="x:Int32" Name="ApprovalOcrWorkflowStatus" />
                                                                                                          <Variable x:TypeArguments="x:String" Name="GUID" />
                                                                                                        </Sequence.Variables>
                                                                                                        <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;VendorID&quot;,vendorId},{&quot;division&quot;,division},{&quot;businessRule&quot;,miscValues(&quot;businessRuleForGUID&quot;).ToString}}]" ContinueOnError="True" DisplayName="Approval Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_43" OutputArguments="[ApprovalOcrWorkflowOutput]" ResponseCode="[ApprovalOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\ApprovalGUID.xaml&quot;]" />
                                                                                                        <If Condition="[ApprovalOcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_404">
                                                                                                          <If.Then>
                                                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_452">
                                                                                                              <Sequence.Variables>
                                                                                                                <Variable x:TypeArguments="x:Int32" Name="sendApprovalResponseCode" />
                                                                                                                <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="sendApprovalResponseDictionary" />
                                                                                                              </Sequence.Variables>
                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_783">
                                                                                                                <Assign.To>
                                                                                                                  <OutArgument x:TypeArguments="x:String">[GUID]</OutArgument>
                                                                                                                </Assign.To>
                                                                                                                <Assign.Value>
                                                                                                                  <InArgument x:TypeArguments="x:String">[CType(ApprovalOcrWorkflowOutput("GUID"), String)]</InArgument>
                                                                                                                </Assign.Value>
                                                                                                              </Assign>
                                                                                                              <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;amount&quot;,ocrValues(2)},{&quot;tenantID&quot;,tenantID},{&quot;division&quot;,DIVISION},{&quot;inbn&quot;,inbnValue},{&quot;invoiceNumber&quot;,ocrValues(0)},{&quot;poNumber&quot;,ocrValues(4)},{&quot;vendorName&quot;,vendorId},{&quot;approvalWorkflow&quot;,approvalWorkflow}}]" ContinueOnError="True" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_44" OutputArguments="[sendApprovalResponseDictionary]" ResponseCode="[sendApprovalResponseCode]" WorkflowFile="[projectPath+&quot;\SendtoApprovalWorkflow.xaml&quot;]" />
                                                                                                              <If Condition="[sendApprovalResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_403">
                                                                                                                <If.Then>
                                                                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_451">
                                                                                                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_140" Line="Supllier invoice is sent for approval since the invoice amount is large" Source="[logfile]" />
                                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_784">
                                                                                                                      <Assign.To>
                                                                                                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                                      </Assign.To>
                                                                                                                      <Assign.Value>
                                                                                                                        <InArgument x:TypeArguments="x:String">["Invoice created and Sent for approval"]</InArgument>
                                                                                                                      </Assign.Value>
                                                                                                                    </Assign>
                                                                                                                  </Sequence>
                                                                                                                </If.Then>
                                                                                                              </If>
                                                                                                            </Sequence>
                                                                                                          </If.Then>
                                                                                                        </If>
                                                                                                      </Sequence>
                                                                                                    </If.Then>
                                                                                                  </If>
                                                                                                </Sequence>
                                                                                              </If.Then>
                                                                                            </If>
                                                                                          </Sequence>
                                                                                        </If.Then>
                                                                                      </If>
                                                                                    </Sequence>
                                                                                  </If.Then>
                                                                                  <If.Else>
                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_461">
                                                                                      <If Condition="[miscValues(&quot;createInvoiceIrrespectiveTolerance&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_410">
                                                                                        <If.Then>
                                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_459">
                                                                                            <Assign DisplayName="Assign ivdate" sap2010:WorkflowViewState.IdRef="Assign_785">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="x:String">[ivdate]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="x:String">[DateTime.ParseExact(ocrValues(1),"dd/MM/yyyy",Nothing).ToString("yyyyMMdd")]</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                            <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_786">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="x:String">[System.DateTime.Now.ToString("yyyyMMddHHmmssfff").tostring + " -"+ ocrValues(0) + "-" + division + "-" + vendorID]</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                            <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_2">
                                                                                              <TryCatch.Try>
                                                                                                <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_816">
                                                                                                  <Assign.To>
                                                                                                    <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                                                                                                  </Assign.To>
                                                                                                  <Assign.Value>
                                                                                                    <InArgument x:TypeArguments="x:String">[correlationID.Substring(0,35)]</InArgument>
                                                                                                  </Assign.Value>
                                                                                                </Assign>
                                                                                              </TryCatch.Try>
                                                                                              <TryCatch.Catches>
                                                                                                <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_2">
                                                                                                  <ActivityAction x:TypeArguments="s:Exception">
                                                                                                    <ActivityAction.Argument>
                                                                                                      <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                                    </ActivityAction.Argument>
                                                                                                    <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_817">
                                                                                                      <Assign.To>
                                                                                                        <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                                                                                                      </Assign.To>
                                                                                                      <Assign.Value>
                                                                                                        <InArgument x:TypeArguments="x:String">[correlationID]</InArgument>
                                                                                                      </Assign.Value>
                                                                                                    </Assign>
                                                                                                  </ActivityAction>
                                                                                                </Catch>
                                                                                              </TryCatch.Catches>
                                                                                            </TryCatch>
                                                                                            <If Condition="[authUser.ToUpper() = &quot;SUPPLIERAUTHUSER&quot;]" sap2010:WorkflowViewState.IdRef="If_447">
                                                                                              <If.Then>
                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_517">
                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_915">
                                                                                                    <Assign.To>
                                                                                                      <OutArgument x:TypeArguments="x:String">[authUser]</OutArgument>
                                                                                                    </Assign.To>
                                                                                                    <Assign.Value>
                                                                                                      <InArgument x:TypeArguments="x:String">[vendorResult(1)]</InArgument>
                                                                                                    </Assign.Value>
                                                                                                  </Assign>
                                                                                                </Sequence>
                                                                                              </If.Then>
                                                                                            </If>
                                                                                            <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;bkid&quot;,bkid},{&quot;authUser&quot;,authUser},{&quot;cuam&quot;,ocrValues(2).Replace(&quot;,&quot;,&quot;&quot;)},{&quot;pyme&quot;,pyme},{&quot;tepy&quot;,tepy},{&quot;cucd&quot;,cucd},{&quot;sino&quot;,ocrValues(0)},{&quot;division&quot;,division},{&quot;ivdate&quot;,ivdate},{&quot;SupplierNo&quot;,VendorID},{&quot;correlationID&quot;,correlationID},{&quot;discountTerms&quot;,discountTerms},{&quot;miscValues&quot;,miscValues}}]" ContinueOnError="True" DisplayName="Add Head Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_45" OutputArguments="[AddHeadOcrWorkflowOutput]" ResponseCode="[AddHeadOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\AddHead.xaml&quot;]" />
                                                                                            <If Condition="[AddHeadOcrWorkflowStatus = 200]" sap2010:WorkflowViewState.IdRef="If_409">
                                                                                              <If.Then>
                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_457">
                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_788">
                                                                                                    <Assign.To>
                                                                                                      <OutArgument x:TypeArguments="x:String">[inbnValue]</OutArgument>
                                                                                                    </Assign.To>
                                                                                                    <Assign.Value>
                                                                                                      <InArgument x:TypeArguments="x:String">[CType(AddHeadOcrWorkflowOutput("inbnValue"), String)]</InArgument>
                                                                                                    </Assign.Value>
                                                                                                  </Assign>
                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_789">
                                                                                                    <Assign.To>
                                                                                                      <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                                    </Assign.To>
                                                                                                    <Assign.Value>
                                                                                                      <InArgument x:TypeArguments="x:String">[CType(AddHeadOcrWorkflowOutput("Status"), String)]</InArgument>
                                                                                                    </Assign.Value>
                                                                                                  </Assign>
                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_790">
                                                                                                    <Assign.To>
                                                                                                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                    </Assign.To>
                                                                                                    <Assign.Value>
                                                                                                      <InArgument x:TypeArguments="x:String">[CType(AddHeadOcrWorkflowOutput("commentStatus"), String)]</InArgument>
                                                                                                    </Assign.Value>
                                                                                                  </Assign>
                                                                                                  <If Condition="[inbnValue &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_408">
                                                                                                    <If.Then>
                                                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_465">
                                                                                                        <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;M3TotalTableRows&quot;,M3TotalTableRows},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;division&quot;,division},{&quot;inbnValue&quot;,inbnValue}}]" ContinueOnError="True" DisplayName="Add Lines Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_46" OutputArguments="[AddLineOcrWorkflowOutput]" ResponseCode="[AddLineOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\AddLine.xaml&quot;]" />
                                                                                                        <If Condition="[AddLineOcrWorkflowStatus= 200]" sap2010:WorkflowViewState.IdRef="If_415">
                                                                                                          <If.Then>
                                                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_467">
                                                                                                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" Response="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_18" StatusCode="[validateStatus]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS455MI/ValidByBatchNo&quot;]">
                                                                                                                <iai:IONAPIRequestWizard.Headers>
                                                                                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                      <x:String>Accept</x:String>
                                                                                                                    </scg:List>
                                                                                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                      <x:String>application/json</x:String>
                                                                                                                    </scg:List>
                                                                                                                  </scg:List>
                                                                                                                </iai:IONAPIRequestWizard.Headers>
                                                                                                                <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                      <x:String>INBN</x:String>
                                                                                                                      <x:String>DIVI</x:String>
                                                                                                                    </scg:List>
                                                                                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                      <x:String>inbnValue</x:String>
                                                                                                                      <x:String>division</x:String>
                                                                                                                    </scg:List>
                                                                                                                  </scg:List>
                                                                                                                </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                              </iai:IONAPIRequestWizard>
                                                                                                              <If Condition="[validateStatus = 200]" sap2010:WorkflowViewState.IdRef="If_416">
                                                                                                                <If.Then>
                                                                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_468">
                                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_801">
                                                                                                                      <Assign.To>
                                                                                                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                                      </Assign.To>
                                                                                                                      <Assign.Value>
                                                                                                                        <InArgument x:TypeArguments="x:String">Invoice created in the M3 but amount in invoice is not matched with M3. please verify the amounts.</InArgument>
                                                                                                                      </Assign.Value>
                                                                                                                    </Assign>
                                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_802">
                                                                                                                      <Assign.To>
                                                                                                                        <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                                                      </Assign.To>
                                                                                                                      <Assign.Value>
                                                                                                                        <InArgument x:TypeArguments="x:String">SUCCESS</InArgument>
                                                                                                                      </Assign.Value>
                                                                                                                    </Assign>
                                                                                                                  </Sequence>
                                                                                                                </If.Then>
                                                                                                                <If.Else>
                                                                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_469">
                                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_803">
                                                                                                                      <Assign.To>
                                                                                                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                                      </Assign.To>
                                                                                                                      <Assign.Value>
                                                                                                                        <InArgument x:TypeArguments="x:String">["Invoice Header and lines are created. Error occured while validating the invoice."]</InArgument>
                                                                                                                      </Assign.Value>
                                                                                                                    </Assign>
                                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_804">
                                                                                                                      <Assign.To>
                                                                                                                        <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                                                      </Assign.To>
                                                                                                                      <Assign.Value>
                                                                                                                        <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                                                                      </Assign.Value>
                                                                                                                    </Assign>
                                                                                                                  </Sequence>
                                                                                                                </If.Else>
                                                                                                              </If>
                                                                                                            </Sequence>
                                                                                                          </If.Then>
                                                                                                          <If.Else>
                                                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_466">
                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_799">
                                                                                                                <Assign.To>
                                                                                                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                                </Assign.To>
                                                                                                                <Assign.Value>
                                                                                                                  <InArgument x:TypeArguments="x:String">["Invoice header added and error occured while adding the lines."]</InArgument>
                                                                                                                </Assign.Value>
                                                                                                              </Assign>
                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_800">
                                                                                                                <Assign.To>
                                                                                                                  <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                                                </Assign.To>
                                                                                                                <Assign.Value>
                                                                                                                  <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                                                                </Assign.Value>
                                                                                                              </Assign>
                                                                                                            </Sequence>
                                                                                                          </If.Else>
                                                                                                        </If>
                                                                                                      </Sequence>
                                                                                                    </If.Then>
                                                                                                  </If>
                                                                                                </Sequence>
                                                                                              </If.Then>
                                                                                              <If.Else>
                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_458">
                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_791">
                                                                                                    <Assign.To>
                                                                                                      <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                                    </Assign.To>
                                                                                                    <Assign.Value>
                                                                                                      <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                                                    </Assign.Value>
                                                                                                  </Assign>
                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_792">
                                                                                                    <Assign.To>
                                                                                                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                    </Assign.To>
                                                                                                    <Assign.Value>
                                                                                                      <InArgument x:TypeArguments="x:String">Error occured while adding the lines</InArgument>
                                                                                                    </Assign.Value>
                                                                                                  </Assign>
                                                                                                </Sequence>
                                                                                              </If.Else>
                                                                                            </If>
                                                                                          </Sequence>
                                                                                        </If.Then>
                                                                                        <If.Else>
                                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_460">
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_793">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="x:String">["Amount is not allocated with the amount in M3. Please check the delivery note numbers."]</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_794">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="x:String">NEEDSVERIFICATION</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                          </Sequence>
                                                                                        </If.Else>
                                                                                      </If>
                                                                                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_141" Line="[commentStatus]" Source="[logfile]" />
                                                                                    </Sequence>
                                                                                  </If.Else>
                                                                                </If>
                                                                              </Sequence>
                                                                            </If.Then>
                                                                          </If>
                                                                        </Sequence>
                                                                      </If.Then>
                                                                      <If.Else>
                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_464">
                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_795">
                                                                            <Assign.To>
                                                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                            </Assign.To>
                                                                            <Assign.Value>
                                                                              <InArgument x:TypeArguments="x:String">An error occurred with the AI Optimizer.</InArgument>
                                                                            </Assign.Value>
                                                                          </Assign>
                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_796">
                                                                            <Assign.To>
                                                                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                            </Assign.To>
                                                                            <Assign.Value>
                                                                              <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                                                                            </Assign.Value>
                                                                          </Assign>
                                                                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_142" Line="[commentStatus]" Source="[logfile]" />
                                                                        </Sequence>
                                                                      </If.Else>
                                                                    </If>
                                                                  </Sequence>
                                                                </If.Then>
                                                              </If>
                                                            </Sequence>
                                                          </ActivityAction>
                                                        </ForEach>
                                                      </Sequence>
                                                    </If.Then>
                                                  </If>
                                                </Sequence>
                                              </If.Else>
                                            </If>
                                          </Sequence>
                                        </If.Then>
                                      </If>
                                    </Sequence>
                                  </Sequence>
                                </If.Else>
                              </If>
                            </Sequence>
                          </Sequence>
                        </If.Then>
                      </If>
                    </Sequence>
                  </If.Then>
                  <If.Else>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_317">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_589">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_590">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">["Invoice number " + ocrValues(0)+" already exists."]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                  </If.Else>
                </If>
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_292">
                <Sequence.Variables>
                  <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="vendorResponseDictionary" />
                  <Variable x:TypeArguments="x:Int32" Name="vendorResponseCode" />
                </Sequence.Variables>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_541">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[CType(POocrWorkflowOutput("Status"), String)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_542">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[CType(POocrWorkflowOutput("commentStatus"), String)]</InArgument>
                  </Assign.Value>
                </Assign>
                <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;VendorName&quot;,ocrValues(8)},{&quot;VendorAddress&quot;,ocrValues(12)},{&quot;VendorPhone&quot;,ocrValues(13)}}]" ContinueOnError="True" DisplayName="VendorAddress Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_47" OutputArguments="[vendorResponseDictionary]" ResponseCode="[vendorResponseCode]" WorkflowFile="[projectPath+&quot;\vendorIDAddressMatch.xaml&quot;]" />
                <If Condition="[vendorResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_414">
                  <If.Then>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_798">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("vendorID"), String)]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </If.Then>
                </If>
              </Sequence>
            </If.Else>
          </If>
        </Sequence>
      </If.Then>
      <If.Else>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_293">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_543">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching company number and division for the PO " + pono +"."]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_544">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
            </Assign.Value>
          </Assign>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_105" Line="[commentStatus]" Source="[logfile]" />
        </Sequence>
      </If.Else>
    </If>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_893" sap:VirtualizedContainerService.HintSize="3470,60" />
      <sap2010:ViewStateData Id="Assign_855" sap:VirtualizedContainerService.HintSize="680,62" />
      <sap2010:ViewStateData Id="Assign_856" sap:VirtualizedContainerService.HintSize="680,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_23" sap:VirtualizedContainerService.HintSize="680,22" />
      <sap2010:ViewStateData Id="Assign_857" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_495" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_858" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_496" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_433" sap:VirtualizedContainerService.HintSize="554,340" />
      <sap2010:ViewStateData Id="If_434" sap:VirtualizedContainerService.HintSize="680,494" />
      <sap2010:ViewStateData Id="Sequence_497" sap:VirtualizedContainerService.HintSize="3470,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="3470,60" />
      <sap2010:ViewStateData Id="Assign_630" sap:VirtualizedContainerService.HintSize="3470,60" />
      <sap2010:ViewStateData Id="Assign_631" sap:VirtualizedContainerService.HintSize="3470,60" />
      <sap2010:ViewStateData Id="Assign_685" sap:VirtualizedContainerService.HintSize="3470,60" />
      <sap2010:ViewStateData Id="Assign_136" sap:VirtualizedContainerService.HintSize="3470,60" />
      <sap2010:ViewStateData Id="Assign_686" sap:VirtualizedContainerService.HintSize="3470,60" />
      <sap2010:ViewStateData Id="Assign_687" sap:VirtualizedContainerService.HintSize="3470,60" />
      <sap2010:ViewStateData Id="Assign_137" sap:VirtualizedContainerService.HintSize="3470,60" />
      <sap2010:ViewStateData Id="Assign_602" sap:VirtualizedContainerService.HintSize="3470,60" />
      <sap2010:ViewStateData Id="Assign_603" sap:VirtualizedContainerService.HintSize="3470,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_10" sap:VirtualizedContainerService.HintSize="3470,22" />
      <sap2010:ViewStateData Id="Assign_528" sap:VirtualizedContainerService.HintSize="3159,60" />
      <sap2010:ViewStateData Id="Assign_859" sap:VirtualizedContainerService.HintSize="2626,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_17" sap:VirtualizedContainerService.HintSize="2626,22" />
      <sap2010:ViewStateData Id="Assign_579" sap:VirtualizedContainerService.HintSize="2626,60" />
      <sap2010:ViewStateData Id="Assign_529" sap:VirtualizedContainerService.HintSize="2315,60" />
      <sap2010:ViewStateData Id="Assign_530" sap:VirtualizedContainerService.HintSize="2315,60" />
      <sap2010:ViewStateData Id="Assign_531" sap:VirtualizedContainerService.HintSize="2315,60" />
      <sap2010:ViewStateData Id="Assign_532" sap:VirtualizedContainerService.HintSize="2315,60" />
      <sap2010:ViewStateData Id="Assign_533" sap:VirtualizedContainerService.HintSize="2315,60" />
      <sap2010:ViewStateData Id="Assign_534" sap:VirtualizedContainerService.HintSize="2315,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_11" sap:VirtualizedContainerService.HintSize="2315,22" />
      <sap2010:ViewStateData Id="Assign_546" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_547" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_294" sap:VirtualizedContainerService.HintSize="2168,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_540" sap:VirtualizedContainerService.HintSize="2168,60" />
      <sap2010:ViewStateData Id="Assign_538" sap:VirtualizedContainerService.HintSize="2168,60" />
      <sap2010:ViewStateData Id="Assign_545" sap:VirtualizedContainerService.HintSize="2168,60" />
      <sap2010:ViewStateData Id="Assign_931" sap:VirtualizedContainerService.HintSize="2168,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_13" sap:VirtualizedContainerService.HintSize="553,22" />
      <sap2010:ViewStateData Id="Assign_548" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_549" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_296" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_550" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_551" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_106" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_297" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_274" sap:VirtualizedContainerService.HintSize="553,494" />
      <sap2010:ViewStateData Id="Sequence_328" sap:VirtualizedContainerService.HintSize="575,680">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_616" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_617" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_329" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_303" sap:VirtualizedContainerService.HintSize="2146,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_552" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_553" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_107" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_298" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_878" sap:VirtualizedContainerService.HintSize="1813,60" />
      <sap2010:ViewStateData Id="Assign_556" sap:VirtualizedContainerService.HintSize="1761,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_12" sap:VirtualizedContainerService.HintSize="1761,22" />
      <sap2010:ViewStateData Id="Assign_632" sap:VirtualizedContainerService.HintSize="1514,60" />
      <sap2010:ViewStateData Id="Assign_633" sap:VirtualizedContainerService.HintSize="1514,60" />
      <sap2010:ViewStateData Id="Append_Line_118" sap:VirtualizedContainerService.HintSize="287,22" />
      <sap2010:ViewStateData Id="InvokeMethod_27" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="Sequence_353" sap:VirtualizedContainerService.HintSize="240,252">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_53" sap:VirtualizedContainerService.HintSize="287,400" />
      <sap2010:ViewStateData Id="Sequence_354" sap:VirtualizedContainerService.HintSize="309,586">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_863" sap:VirtualizedContainerService.HintSize="1158,60" />
      <sap2010:ViewStateData Id="Assign_864" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_865" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_150" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_866" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_499" sap:VirtualizedContainerService.HintSize="264,446">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_885" sap:VirtualizedContainerService.HintSize="825,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_48" sap:VirtualizedContainerService.HintSize="825,22" />
      <sap2010:ViewStateData Id="Assign_886" sap:VirtualizedContainerService.HintSize="678,60" />
      <sap2010:ViewStateData Id="Assign_887" sap:VirtualizedContainerService.HintSize="678,60" />
      <sap2010:ViewStateData Id="Assign_888" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_889" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_154" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_890" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_509" sap:VirtualizedContainerService.HintSize="264,446">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_875" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_876" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_152" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_877" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_510" sap:VirtualizedContainerService.HintSize="264,446">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_442" sap:VirtualizedContainerService.HintSize="553,594" />
      <sap2010:ViewStateData Id="If_441" sap:VirtualizedContainerService.HintSize="678,742" />
      <sap2010:ViewStateData Id="Sequence_508" sap:VirtualizedContainerService.HintSize="700,1066">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_440" sap:VirtualizedContainerService.HintSize="825,1214" />
      <sap2010:ViewStateData Id="Sequence_504" sap:VirtualizedContainerService.HintSize="847,1500">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_502" sap:VirtualizedContainerService.HintSize="869,1624">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_436" sap:VirtualizedContainerService.HintSize="1158,1772" />
      <sap2010:ViewStateData Id="Sequence_503" sap:VirtualizedContainerService.HintSize="1180,1996">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_315" sap:VirtualizedContainerService.HintSize="1514,2144" />
      <sap2010:ViewStateData Id="Sequence_348" sap:VirtualizedContainerService.HintSize="1536,2468">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_127" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_314" sap:VirtualizedContainerService.HintSize="1761,2616" />
      <sap2010:ViewStateData Id="Sequence_347" sap:VirtualizedContainerService.HintSize="1783,2902">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_51" sap:VirtualizedContainerService.HintSize="1813,3050">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_932" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_933" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_155" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_524" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_688" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_879" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_11" sap:VirtualizedContainerService.HintSize="1248,22" />
      <sap2010:ViewStateData Id="InvokeMethod_35" sap:VirtualizedContainerService.HintSize="464,128" />
      <sap2010:ViewStateData Id="Assign_689" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="InvokeMethod_36" sap:VirtualizedContainerService.HintSize="242,128" />
      <sap2010:ViewStateData Id="Sequence_393" sap:VirtualizedContainerService.HintSize="264,352">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_349" sap:VirtualizedContainerService.HintSize="464,500" />
      <sap2010:ViewStateData Id="Sequence_394" sap:VirtualizedContainerService.HintSize="486,792">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_690" sap:VirtualizedContainerService.HintSize="611,60" />
      <sap2010:ViewStateData Id="Assign_691" sap:VirtualizedContainerService.HintSize="611,60" />
      <sap2010:ViewStateData Id="InvokeMethod_37" sap:VirtualizedContainerService.HintSize="464,128" />
      <sap2010:ViewStateData Id="Assign_692" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="InvokeMethod_38" sap:VirtualizedContainerService.HintSize="242,128" />
      <sap2010:ViewStateData Id="Sequence_395" sap:VirtualizedContainerService.HintSize="264,352">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_350" sap:VirtualizedContainerService.HintSize="464,500" />
      <sap2010:ViewStateData Id="Sequence_396" sap:VirtualizedContainerService.HintSize="486,792">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_351" sap:VirtualizedContainerService.HintSize="611,940" />
      <sap2010:ViewStateData Id="Sequence_397" sap:VirtualizedContainerService.HintSize="633,1264">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_352" sap:VirtualizedContainerService.HintSize="1144,1412" />
      <sap2010:ViewStateData Id="Sequence_398" sap:VirtualizedContainerService.HintSize="1166,1536">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_61" sap:VirtualizedContainerService.HintSize="1196,1684" />
      <sap2010:ViewStateData Id="Sequence_399" sap:VirtualizedContainerService.HintSize="1218,1808">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_62" sap:VirtualizedContainerService.HintSize="1248,1956">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_693" sap:VirtualizedContainerService.HintSize="1248,60" />
      <sap2010:ViewStateData Id="InvokeMethod_39" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="Sequence_400" sap:VirtualizedContainerService.HintSize="240,252">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_401" sap:VirtualizedContainerService.HintSize="262,376">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_353" sap:VirtualizedContainerService.HintSize="464,524" />
      <sap2010:ViewStateData Id="InvokeMethod_40" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="Sequence_402" sap:VirtualizedContainerService.HintSize="240,252">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_354" sap:VirtualizedContainerService.HintSize="729,672" />
      <sap2010:ViewStateData Id="ForEach`1_63" sap:VirtualizedContainerService.HintSize="1248,820" />
      <sap2010:ViewStateData Id="Assign_694" sap:VirtualizedContainerService.HintSize="1248,60" />
      <sap2010:ViewStateData Id="Sequence_403" sap:VirtualizedContainerService.HintSize="1270,3202">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_355" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_749" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_750" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="InvokeMethod_42" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="If_384" sap:VirtualizedContainerService.HintSize="464,288" />
      <sap2010:ViewStateData Id="InvokeMethod_43" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="If_385" sap:VirtualizedContainerService.HintSize="707.333333333333,442" />
      <sap2010:ViewStateData Id="Sequence_435" sap:VirtualizedContainerService.HintSize="729.333333333333,566">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_68" sap:VirtualizedContainerService.HintSize="760,718.666666666667" />
      <sap2010:ViewStateData Id="InvokeMethod_44" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="If_386" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_751" sap:VirtualizedContainerService.HintSize="3339.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_752" sap:VirtualizedContainerService.HintSize="3286.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_926" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_927" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_520" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_451" sap:VirtualizedContainerService.HintSize="464,442" />
      <sap2010:ViewStateData Id="Assign_928" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_929" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_521" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Catch`1_3" sap:VirtualizedContainerService.HintSize="468.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_3" sap:VirtualizedContainerService.HintSize="3138.66666666667,680" />
      <sap2010:ViewStateData Id="Assign_925" sap:VirtualizedContainerService.HintSize="3139,60" />
      <sap2010:ViewStateData Id="Assign_916" sap:VirtualizedContainerService.HintSize="3139,60" />
      <sap2010:ViewStateData Id="Assign_917" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_918" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_518" sap:VirtualizedContainerService.HintSize="264,286">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_80" sap:VirtualizedContainerService.HintSize="3139,434">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_919" sap:VirtualizedContainerService.HintSize="3139,60" />
      <sap2010:ViewStateData Id="Assign_920" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_448" sap:VirtualizedContainerService.HintSize="3139,210" />
      <sap2010:ViewStateData Id="Assign_921" sap:VirtualizedContainerService.HintSize="3139,60" />
      <sap2010:ViewStateData Id="Assign_922" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_923" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_519" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_449" sap:VirtualizedContainerService.HintSize="464,432" />
      <sap2010:ViewStateData Id="If_450" sap:VirtualizedContainerService.HintSize="3139,580" />
      <sap2010:ViewStateData Id="Assign_695" sap:VirtualizedContainerService.HintSize="3139,60" />
      <sap2010:ViewStateData Id="Assign_924" sap:VirtualizedContainerService.HintSize="509,60" />
      <sap2010:ViewStateData Id="Assign_696" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_697" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_356" sap:VirtualizedContainerService.HintSize="509,208" />
      <sap2010:ViewStateData Id="Assign_698" sap:VirtualizedContainerService.HintSize="509,60" />
      <sap2010:ViewStateData Id="Sequence_404" sap:VirtualizedContainerService.HintSize="531,532">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_64" sap:VirtualizedContainerService.HintSize="3139,680">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_38" sap:VirtualizedContainerService.HintSize="3139,22" />
      <sap2010:ViewStateData Id="Assign_754" sap:VirtualizedContainerService.HintSize="2828,60" />
      <sap2010:ViewStateData Id="Assign_755" sap:VirtualizedContainerService.HintSize="2828,60" />
      <sap2010:ViewStateData Id="Assign_756" sap:VirtualizedContainerService.HintSize="2828,60" />
      <sap2010:ViewStateData Id="Assign_757" sap:VirtualizedContainerService.HintSize="738,60" />
      <sap2010:ViewStateData Id="Assign_758" sap:VirtualizedContainerService.HintSize="738,60" />
      <sap2010:ViewStateData Id="Assign_759" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_760" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_439" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_761" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_440" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_389" sap:VirtualizedContainerService.HintSize="553,332">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_762" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Sequence_441" sap:VirtualizedContainerService.HintSize="575,656">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_71" sap:VirtualizedContainerService.HintSize="738,804" />
      <sap2010:ViewStateData Id="InvokeWorkflow_39" sap:VirtualizedContainerService.HintSize="738,22" />
      <sap2010:ViewStateData Id="Assign_763" sap:VirtualizedContainerService.HintSize="590,62" />
      <sap2010:ViewStateData Id="Assign_764" sap:VirtualizedContainerService.HintSize="590,62" />
      <sap2010:ViewStateData Id="Assign_765" sap:VirtualizedContainerService.HintSize="590,62" />
      <sap2010:ViewStateData Id="Assign_766" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_812" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_473" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_390" sap:VirtualizedContainerService.HintSize="464,442" />
      <sap2010:ViewStateData Id="If_391" sap:VirtualizedContainerService.HintSize="590,596" />
      <sap2010:ViewStateData Id="Sequence_442" sap:VirtualizedContainerService.HintSize="612,1026">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_392" sap:VirtualizedContainerService.HintSize="738,1180" />
      <sap2010:ViewStateData Id="Sequence_443" sap:VirtualizedContainerService.HintSize="760,2410">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_393" sap:VirtualizedContainerService.HintSize="885,2558" />
      <sap2010:ViewStateData Id="Sequence_444" sap:VirtualizedContainerService.HintSize="907,2682">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_813" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_445" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_394" sap:VirtualizedContainerService.HintSize="2681,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_767" sap:VirtualizedContainerService.HintSize="2681,60" />
      <sap2010:ViewStateData Id="Assign_768" sap:VirtualizedContainerService.HintSize="975,60" />
      <sap2010:ViewStateData Id="Assign_769" sap:VirtualizedContainerService.HintSize="975,60" />
      <sap2010:ViewStateData Id="Assign_770" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_814" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Catch`1_1" sap:VirtualizedContainerService.HintSize="404,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_1" sap:VirtualizedContainerService.HintSize="975,287">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_797" sap:VirtualizedContainerService.HintSize="975,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_49" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_914" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_446" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Assign_913" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Sequence_516" sap:VirtualizedContainerService.HintSize="486,494">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_912" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_515" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_444" sap:VirtualizedContainerService.HintSize="464,332" />
      <sap2010:ViewStateData Id="If_445" sap:VirtualizedContainerService.HintSize="975,642" />
      <sap2010:ViewStateData Id="InvokeWorkflow_40" sap:VirtualizedContainerService.HintSize="975,22" />
      <sap2010:ViewStateData Id="Assign_771" sap:VirtualizedContainerService.HintSize="886,62" />
      <sap2010:ViewStateData Id="Assign_772" sap:VirtualizedContainerService.HintSize="886,62" />
      <sap2010:ViewStateData Id="Assign_773" sap:VirtualizedContainerService.HintSize="886,62" />
      <sap2010:ViewStateData Id="Assign_774" sap:VirtualizedContainerService.HintSize="738,62" />
      <sap2010:ViewStateData Id="Assign_829" sap:VirtualizedContainerService.HintSize="738,62" />
      <sap2010:ViewStateData Id="Assign_830" sap:VirtualizedContainerService.HintSize="738,62" />
      <sap2010:ViewStateData Id="Assign_823" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_824" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_46" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Sequence_480" sap:VirtualizedContainerService.HintSize="264,462">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_75" sap:VirtualizedContainerService.HintSize="294.666666666667,614.666666666667" />
      <sap2010:ViewStateData Id="Assign_827" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_828" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_48" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Sequence_482" sap:VirtualizedContainerService.HintSize="264,462">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_425" sap:VirtualizedContainerService.HintSize="464,616" />
      <sap2010:ViewStateData Id="ForEach`1_78" sap:VirtualizedContainerService.HintSize="494.666666666667,768.666666666667" />
      <sap2010:ViewStateData Id="ForEach`1_79" sap:VirtualizedContainerService.HintSize="525.333333333333,921.333333333333" />
      <sap2010:ViewStateData Id="If_424" sap:VirtualizedContainerService.HintSize="738,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_805" sap:VirtualizedContainerService.HintSize="738,62" />
      <sap2010:ViewStateData Id="Assign_819" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_476" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_419" sap:VirtualizedContainerService.HintSize="464,340" />
      <sap2010:ViewStateData Id="Sequence_477" sap:VirtualizedContainerService.HintSize="486,464">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_820" sap:VirtualizedContainerService.HintSize="590,62" />
      <sap2010:ViewStateData Id="Assign_821" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_822" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_478" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_420" sap:VirtualizedContainerService.HintSize="464,442" />
      <sap2010:ViewStateData Id="If_421" sap:VirtualizedContainerService.HintSize="590,596" />
      <sap2010:ViewStateData Id="Sequence_479" sap:VirtualizedContainerService.HintSize="612,822">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_422" sap:VirtualizedContainerService.HintSize="738,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_806" sap:VirtualizedContainerService.HintSize="738,62" />
      <sap2010:ViewStateData Id="Assign_831" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_832" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_426" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="Sequence_483" sap:VirtualizedContainerService.HintSize="486,442">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_427" sap:VirtualizedContainerService.HintSize="738,596" />
      <sap2010:ViewStateData Id="Assign_809" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_471" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_74" sap:VirtualizedContainerService.HintSize="294.666666666667,338.666666666667" />
      <sap2010:ViewStateData Id="If_418" sap:VirtualizedContainerService.HintSize="738,492.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_41" sap:VirtualizedContainerService.HintSize="738,22" />
      <sap2010:ViewStateData Id="Assign_883" sap:VirtualizedContainerService.HintSize="738,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_42" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_884" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_507" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_396" sap:VirtualizedContainerService.HintSize="464,402" />
      <sap2010:ViewStateData Id="If_397" sap:VirtualizedContainerService.HintSize="738,556" />
      <sap2010:ViewStateData Id="Assign_775" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_882" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_25" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_26" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_438" sap:VirtualizedContainerService.HintSize="464,214" />
      <sap2010:ViewStateData Id="Sequence_446" sap:VirtualizedContainerService.HintSize="486,542">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_398" sap:VirtualizedContainerService.HintSize="612,696" />
      <sap2010:ViewStateData Id="If_399" sap:VirtualizedContainerService.HintSize="738,850" />
      <sap2010:ViewStateData Id="InvokeWorkflow_50" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_930" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_522" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_452" sap:VirtualizedContainerService.HintSize="464,402" />
      <sap2010:ViewStateData Id="Assign_892" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_28" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_511" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_439" sap:VirtualizedContainerService.HintSize="464,402" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_16" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Delay_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_776" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_778" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_910" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_911" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_49" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="InvokeMethod_50" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="InvokeMethod_51" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="InvokeMethod_52" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="InvokeMethod_53" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="Sequence_514" sap:VirtualizedContainerService.HintSize="239.333333333333,954">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_443" sap:VirtualizedContainerService.HintSize="464,1108" />
      <sap2010:ViewStateData Id="Sequence_513" sap:VirtualizedContainerService.HintSize="486,1232">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_837" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_838" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_485" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_849" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_850" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_491" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_428" sap:VirtualizedContainerService.HintSize="554,442" />
      <sap2010:ViewStateData Id="Assign_851" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_852" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_492" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_429" sap:VirtualizedContainerService.HintSize="844,596" />
      <sap2010:ViewStateData Id="Sequence_448" sap:VirtualizedContainerService.HintSize="866,720">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_401" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_138" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_449" sap:VirtualizedContainerService.HintSize="264,770.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_512" sap:VirtualizedContainerService.HintSize="286,894.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_880" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_881" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_153" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_506" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_437" sap:VirtualizedContainerService.HintSize="464,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_505" sap:VirtualizedContainerService.HintSize="486,1060.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_781" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_782" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_139" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_450" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_402" sap:VirtualizedContainerService.HintSize="738,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_43" sap:VirtualizedContainerService.HintSize="612,22" />
      <sap2010:ViewStateData Id="Assign_783" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_44" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Append_Line_140" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_784" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_451" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_403" sap:VirtualizedContainerService.HintSize="464,402" />
      <sap2010:ViewStateData Id="Sequence_452" sap:VirtualizedContainerService.HintSize="486,690">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_404" sap:VirtualizedContainerService.HintSize="612,844" />
      <sap2010:ViewStateData Id="Sequence_453" sap:VirtualizedContainerService.HintSize="634,1030">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_405" sap:VirtualizedContainerService.HintSize="738,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_454" sap:VirtualizedContainerService.HintSize="760,3783.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_406" sap:VirtualizedContainerService.HintSize="886,3937.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_455" sap:VirtualizedContainerService.HintSize="908,4367.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_407" sap:VirtualizedContainerService.HintSize="975,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_456" sap:VirtualizedContainerService.HintSize="997,1546">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_785" sap:VirtualizedContainerService.HintSize="1322,60" />
      <sap2010:ViewStateData Id="Assign_786" sap:VirtualizedContainerService.HintSize="1322,60" />
      <sap2010:ViewStateData Id="Assign_816" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_817" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Catch`1_2" sap:VirtualizedContainerService.HintSize="404,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_2" sap:VirtualizedContainerService.HintSize="1322,287" />
      <sap2010:ViewStateData Id="Assign_915" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_517" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_447" sap:VirtualizedContainerService.HintSize="1322,332" />
      <sap2010:ViewStateData Id="InvokeWorkflow_45" sap:VirtualizedContainerService.HintSize="1322,22" />
      <sap2010:ViewStateData Id="Assign_788" sap:VirtualizedContainerService.HintSize="1011,60" />
      <sap2010:ViewStateData Id="Assign_789" sap:VirtualizedContainerService.HintSize="1011,60" />
      <sap2010:ViewStateData Id="Assign_790" sap:VirtualizedContainerService.HintSize="1011,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_46" sap:VirtualizedContainerService.HintSize="864,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_18" sap:VirtualizedContainerService.HintSize="553,22" />
      <sap2010:ViewStateData Id="Assign_801" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_802" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_468" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_803" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_804" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_469" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_416" sap:VirtualizedContainerService.HintSize="553,432" />
      <sap2010:ViewStateData Id="Sequence_467" sap:VirtualizedContainerService.HintSize="575,618">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_799" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_800" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_466" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_415" sap:VirtualizedContainerService.HintSize="864,766" />
      <sap2010:ViewStateData Id="Sequence_465" sap:VirtualizedContainerService.HintSize="886,952">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_408" sap:VirtualizedContainerService.HintSize="1011,1100" />
      <sap2010:ViewStateData Id="Sequence_457" sap:VirtualizedContainerService.HintSize="1033,1524">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_791" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_792" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_458" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_409" sap:VirtualizedContainerService.HintSize="1322,1672" />
      <sap2010:ViewStateData Id="Sequence_459" sap:VirtualizedContainerService.HintSize="1344,2757">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_793" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_794" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_460" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_410" sap:VirtualizedContainerService.HintSize="1637,2905" />
      <sap2010:ViewStateData Id="Append_Line_141" sap:VirtualizedContainerService.HintSize="1637,22" />
      <sap2010:ViewStateData Id="Sequence_461" sap:VirtualizedContainerService.HintSize="1659,3091">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_411" sap:VirtualizedContainerService.HintSize="2681,3239" />
      <sap2010:ViewStateData Id="Sequence_462" sap:VirtualizedContainerService.HintSize="2703,3554">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_412" sap:VirtualizedContainerService.HintSize="2828,3702" />
      <sap2010:ViewStateData Id="Sequence_463" sap:VirtualizedContainerService.HintSize="2850,4126">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_795" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_796" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_142" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_464" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_413" sap:VirtualizedContainerService.HintSize="3139,4274">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_438" sap:VirtualizedContainerService.HintSize="3160.66666666667,7744">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_388" sap:VirtualizedContainerService.HintSize="3286.66666666667,7898" />
      <sap2010:ViewStateData Id="Sequence_437" sap:VirtualizedContainerService.HintSize="3308.66666666667,8124">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_70" sap:VirtualizedContainerService.HintSize="3339.33333333333,8276.66666666667" />
      <sap2010:ViewStateData Id="Sequence_436" sap:VirtualizedContainerService.HintSize="3361.33333333333,8502.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_387" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_525" sap:VirtualizedContainerService.HintSize="264,757">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_454" sap:VirtualizedContainerService.HintSize="553,905" />
      <sap2010:ViewStateData Id="Sequence_427" sap:VirtualizedContainerService.HintSize="575,1029">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_380" sap:VirtualizedContainerService.HintSize="1813,1177" />
      <sap2010:ViewStateData Id="Sequence_299" sap:VirtualizedContainerService.HintSize="1835,4491">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_346" sap:VirtualizedContainerService.HintSize="1857,4615">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_275" sap:VirtualizedContainerService.HintSize="2146,4763">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_295" sap:VirtualizedContainerService.HintSize="2168,4978">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_290" sap:VirtualizedContainerService.HintSize="2190,5826">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_271" sap:VirtualizedContainerService.HintSize="2315,5974" />
      <sap2010:ViewStateData Id="Sequence_291" sap:VirtualizedContainerService.HintSize="2337,6760">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_589" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_590" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_317" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_286" sap:VirtualizedContainerService.HintSize="2626,6908" />
      <sap2010:ViewStateData Id="Sequence_310" sap:VirtualizedContainerService.HintSize="2648,7294">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_541" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_542" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_47" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_798" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_414" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Sequence_292" sap:VirtualizedContainerService.HintSize="486,594">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_273" sap:VirtualizedContainerService.HintSize="3159,7442" />
      <sap2010:ViewStateData Id="Sequence_287" sap:VirtualizedContainerService.HintSize="3181,7666">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_543" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_544" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_105" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_293" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_270" sap:VirtualizedContainerService.HintSize="3470,7814" />
      <sap2010:ViewStateData Id="Sequence_17" sap:VirtualizedContainerService.HintSize="3492,9191">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="3532,9431" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>