﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:iae="clr-namespace:Infor.Activities.Email;assembly=Infor.Activities.Email"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:si="clr-namespace:System.IO;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="submails" Type="InArgument(scg:List(iae:Mail))" />
    <x:Property Name="emailsubjects" Type="OutArgument(scg:List(x:String))" />
    <x:Property Name="enableMessageBoxes" Type="InArgument(x:Boolean)" />
    <x:Property Name="logfile" Type="InArgument(x:String)" />
    <x:Property Name="invoiceSource" Type="InArgument(x:String)" />
    <x:Property Name="configurationFolder" Type="InArgument(x:String)" />
    <x:Property Name="seqnumber" Type="InArgument(x:String)" />
    <x:Property Name="DownloadTo" Type="InArgument(x:String)" />
    <x:Property Name="FileRenameFormat" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="ocrKeysDictionary" Type="InArgument(scg:Dictionary(x:String, scg:List(x:String)))" />
    <x:Property Name="colemanAPI" Type="InArgument(x:String)" />
    <x:Property Name="chargeCode" Type="InArgument(x:String)" />
    <x:Property Name="authUser" Type="InArgument(x:String)" />
    <x:Property Name="userIdentifier" Type="InArgument(x:String)" />
    <x:Property Name="distributionType" Type="InArgument(x:String)" />
    <x:Property Name="poFilterValues" Type="InArgument(x:String)" />
    <x:Property Name="poFilterCondition" Type="InArgument(x:String)" />
    <x:Property Name="datalakeAPILogicalId" Type="InArgument(x:String)" />
    <x:Property Name="discountCode" Type="InArgument(x:String)" />
    <x:Property Name="imsAPIUrl" Type="InArgument(x:String)" />
    <x:Property Name="extractNumericFromPO" Type="InArgument(x:Boolean)" />
    <x:Property Name="vatCodeConfig" Type="InArgument(x:String)" />
    <x:Property Name="poDiscountsHandlingConfig" Type="InArgument(x:Boolean)" />
    <x:Property Name="matchVendorItemCode" Type="InArgument(x:Boolean)" />
    <x:Property Name="extractFromWidgetDatalake" Type="InArgument(x:Boolean)" />
    <x:Property Name="approvalRequired" Type="InArgument(x:Boolean)" />
    <x:Property Name="approvalWorkflow" Type="InArgument(x:String)" />
    <x:Property Name="processExpenseInvoice" Type="InArgument(x:Boolean)" />
    <x:Property Name="checkAmountBussinessRule" Type="InArgument(x:String)" />
    <x:Property Name="blnFailureExists" Type="OutArgument(x:Boolean)" />
    <x:Property Name="strJsonString" Type="OutArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>System</x:String>
      <x:String>System.IO</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>System.Text.RegularExpressions</x:String>
      <x:String>Microsoft.VisualBasic</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_2">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="inProgressFolder" />
      <Variable x:TypeArguments="x:Int32" Default="0" Name="pdfs_count" />
      <Variable x:TypeArguments="si:FileInfo" Name="fileDetails" />
      <Variable x:TypeArguments="x:Int32" Name="RenameCounter" />
      <Variable x:TypeArguments="scg:List(x:String)" Name="ListDownloadedFiles" />
      <Variable x:TypeArguments="x:Boolean" Name="attachmentsAvail" />
      <Variable x:TypeArguments="x:String" Name="subject" />
      <Variable x:TypeArguments="x:Int32" Name="processPoRespCode" />
      <Variable x:TypeArguments="x:String" Name="subDownloadFolder" />
      <Variable x:TypeArguments="x:String" Name="InvoiceFileName" />
      <Variable x:TypeArguments="x:Boolean" Name="blnDownload" />
      <Variable x:TypeArguments="x:Int32" Name="intFailureMailCounter" />
    </Sequence.Variables>
    <Assign DisplayName="Assign blnFailureExists" sap2010:WorkflowViewState.IdRef="Assign_77">
      <Assign.To>
        <OutArgument x:TypeArguments="x:Boolean">[blnFailureExists]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:Boolean">False</InArgument>
      </Assign.Value>
    </Assign>
    <Assign DisplayName="Initialize strJsonString" sap2010:WorkflowViewState.IdRef="Assign_78">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[strJsonString]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">
          <Literal x:TypeArguments="x:String" Value="" />
        </InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_31">
      <Assign.To>
        <OutArgument x:TypeArguments="x:Int32">[RenameCounter]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:Int32">1</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_10">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[inProgressFolder]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[configurationFolder+ "\InProgress"]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_1">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(x:String)">[emailsubjects]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(x:String)">[new list( of string)]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign DisplayName="Assign intFailureMailCounter" sap2010:WorkflowViewState.IdRef="Assign_89">
      <Assign.To>
        <OutArgument x:TypeArguments="x:Int32">[intFailureMailCounter]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:Int32">1</InArgument>
      </Assign.Value>
    </Assign>
    <Switch x:TypeArguments="x:Boolean" Expression="[submails.count&gt;0]" sap2010:WorkflowViewState.IdRef="Switch`1_5">
      <ForEach x:TypeArguments="iae:Mail" x:Key="True" DisplayName="ForEach&lt;Mail&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_9" Values="[submails]">
        <ActivityAction x:TypeArguments="iae:Mail">
          <ActivityAction.Argument>
            <DelegateInArgument x:TypeArguments="iae:Mail" Name="email" />
          </ActivityAction.Argument>
          <TryCatch DisplayName="TryCatch - Process Each email (Inside for each sequence)" sap2010:WorkflowViewState.IdRef="TryCatch_6">
            <TryCatch.Variables>
              <Variable x:TypeArguments="x:String" Name="MailCounter" />
              <Variable x:TypeArguments="x:String" Name="strException" />
            </TryCatch.Variables>
            <TryCatch.Try>
              <Sequence DisplayName="AttachmentSequence" sap2010:WorkflowViewState.IdRef="Sequence_35">
                <Sequence.Variables>
                  <Variable x:TypeArguments="scg:List(x:String)" Name="inProgressFiles" />
                  <Variable x:TypeArguments="scg:List(x:String)" Name="attachments" />
                  <Variable x:TypeArguments="x:Boolean" Name="pdfFileAvailable" />
                  <Variable x:TypeArguments="x:Double" Name="filelength" />
                  <Variable x:TypeArguments="scg:List(x:String)" Default="[New List(Of String)]" Name="ListInprogressNames" />
                </Sequence.Variables>
                <If Condition="[enableMessageBoxes]" DisplayName="Check If enableMessageBoxes" sap2010:WorkflowViewState.IdRef="If_25">
                  <If.Then>
                    <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_7" Selection="OK" Text="[&quot;Email Subject :&quot;+email.subject]" />
                  </If.Then>
                </If>
                <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[inProgressFolder]" DisplayName="Get Files in Directory - inProgressFolder for deletion" FileType="All" Files="[inProgressFiles]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_5" IncludeSubDir="True" />
                <Assign sap2010:WorkflowViewState.IdRef="Assign_67">
                  <Assign.To>
                    <OutArgument x:TypeArguments="scg:List(x:String)">[ListInprogressNames]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="scg:List(x:String)">[new list( of string)]</InArgument>
                  </Assign.Value>
                </Assign>
                <ForEach x:TypeArguments="x:String" DisplayName="Get FileNames from Inprogress" sap2010:WorkflowViewState.IdRef="ForEach`1_10" Values="[inProgressFiles]">
                  <ActivityAction x:TypeArguments="x:String">
                    <ActivityAction.Argument>
                      <DelegateInArgument x:TypeArguments="x:String" Name="item" />
                    </ActivityAction.Argument>
                    <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_7" MethodName="Add">
                      <InvokeMethod.TargetObject>
                        <InArgument x:TypeArguments="scg:List(x:String)">[ListInprogressNames]</InArgument>
                      </InvokeMethod.TargetObject>
                      <InArgument x:TypeArguments="x:String">[Path.GetFileName(item).Tostring]</InArgument>
                    </InvokeMethod>
                  </ActivityAction>
                </ForEach>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_79">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:Boolean">[blnDownload]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                  </Assign.Value>
                </Assign>
                <Switch x:TypeArguments="x:String" DisplayName="Downlaod Attachments based on Email Source" Expression="[invoiceSource]" sap2010:WorkflowViewState.IdRef="Switch`1_6">
                  <x:Null x:Key="OutlookGraphEmail" />
                  <Sequence x:Key="OutlookClientEmail" DisplayName="OutlookClientEmail Sequence" sap2010:WorkflowViewState.IdRef="Sequence_41">
                    <iae:DownloadOutlookAttachment ResponseCode="{x:Null}" ContinueOnError="False" DisplayName="Download Outlook Attachment" Email="[email]" sap2010:WorkflowViewState.IdRef="DownloadOutlookAttachment_5" OutputFilePaths="[attachments]" Path="[configurationFolder+&quot;\OutlookDownloads\&quot;+DownloadTo+seqnumber]" />
                    <Assign DisplayName="blnDownload Assign" sap2010:WorkflowViewState.IdRef="Assign_80">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:Boolean">[blnDownload]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                      </Assign.Value>
                    </Assign>
                  </Sequence>
                </Switch>
                <Assign DisplayName="Assign pdfFileAvailable as false" sap2010:WorkflowViewState.IdRef="Assign_33">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:Boolean">[pdfFileAvailable]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                  </Assign.Value>
                </Assign>
                <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_3">
                  <iad:CommentOut.Activities>
                    <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[configurationFolder+&quot;\OutlookDownloads\&quot;+DownloadTo+seqnumber]" DisplayName="Get Files in OutlookDownloads" FileType="PDF" Files="[attachments]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_9" IncludeSubDir="True" />
                  </iad:CommentOut.Activities>
                </iad:CommentOut>
                <ForEach x:TypeArguments="x:String" DisplayName="ForEach&lt;String&gt; - attachment in attachments" sap2010:WorkflowViewState.IdRef="ForEach`1_8" Values="[attachments]">
                  <ActivityAction x:TypeArguments="x:String">
                    <ActivityAction.Argument>
                      <DelegateInArgument x:TypeArguments="x:String" Name="attachment" />
                    </ActivityAction.Argument>
                    <Sequence DisplayName="Inside - attachment in attachments" sap2010:WorkflowViewState.IdRef="Sequence_33">
                      <Sequence.Variables>
                        <Variable x:TypeArguments="x:String" Name="copiedFile" />
                      </Sequence.Variables>
                      <If Condition="[attachment.contains(&quot;.zip&quot;) or attachment.contains(&quot;.ZIP&quot;)]" DisplayName="If .ZIP file Attachment" sap2010:WorkflowViewState.IdRef="If_27">
                        <If.Then>
                          <Sequence DisplayName="Extract Zip" sap2010:WorkflowViewState.IdRef="Sequence_36">
                            <Sequence.Variables>
                              <Variable x:TypeArguments="x:String" Name="extractedFolderPath" />
                              <Variable x:TypeArguments="scg:List(x:String)" Name="extractedFiles" />
                              <Variable x:TypeArguments="x:String" Name="extractedFilePath" />
                            </Sequence.Variables>
                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_21" Line="ZIP File Extracted" Source="[logFile]" />
                            <ias:Directory_Extract ErrorCode="{x:Null}" Password="{x:Null}" ContinueOnError="False" DisplayName="Extract Directory" sap2010:WorkflowViewState.IdRef="Directory_Extract_1" OutputFile="[extractedFolderPath]" Source="[attachment]" Target="[configurationFolder+&quot;\OutlookDownloads\&quot;+DownloadTo+seqnumber]" />
                            <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete ZIP File" sap2010:WorkflowViewState.IdRef="File_Delete_8" Source="[attachment]" />
                          </Sequence>
                        </If.Then>
                      </If>
                    </Sequence>
                  </ActivityAction>
                </ForEach>
                <Sequence DisplayName="Process Downlaoded Files" sap2010:WorkflowViewState.IdRef="Sequence_52">
                  <Sequence.Variables>
                    <Variable x:TypeArguments="x:String" Name="copiedFilePath" />
                  </Sequence.Variables>
                  <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[configurationFolder+&quot;\OutlookDownloads\&quot;+DownloadTo+seqnumber]" DisplayName="Get Files in OutlookDownloads" FileType="PDF" Files="[ListDownloadedFiles]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_10" IncludeSubDir="True" />
                  <ForEach x:TypeArguments="x:String" DisplayName="File rename ListDownloadedFiles" sap2010:WorkflowViewState.IdRef="ForEach`1_18" Values="[ListDownloadedFiles]">
                    <ActivityAction x:TypeArguments="x:String">
                      <ActivityAction.Argument>
                        <DelegateInArgument x:TypeArguments="x:String" Name="item" />
                      </ActivityAction.Argument>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_54">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_75">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[Path.GetFilename(item)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <If Condition="[System.Text.RegularExpressions.Regex.IsMatch(InvoiceFileName, &quot;[:#&amp;@`~'*/\?&lt;&gt;|&quot;&quot;]&quot;)]" DisplayName="Check if the FileName Contains the special Characters" sap2010:WorkflowViewState.IdRef="If_41">
                          <If.Then>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_55">
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_76">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(InvoiceFileName, "[:#&amp;@`~'*/\?&lt;&gt;|""]", "_")]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <ias:File_Copy ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Copy File" sap2010:WorkflowViewState.IdRef="File_Copy_6" OutputFile="[copiedFilePath]" OverwriteFile="False" Source="[item]" Target="[configurationFolder+&quot;\OutlookDownloads\&quot;+DownloadTo+seqnumber]" TargetFilename="[InvoiceFileName]" />
                              <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_9" Source="[item]" />
                            </Sequence>
                          </If.Then>
                        </If>
                      </Sequence>
                    </ActivityAction>
                  </ForEach>
                  <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[configurationFolder+&quot;\OutlookDownloads\&quot;+DownloadTo+seqnumber]" DisplayName="Get Files in OutlookDownloads" FileType="PDF" Files="[ListDownloadedFiles]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_11" IncludeSubDir="True" />
                  <If Condition="[miscValues(&quot;SplittingDoc&quot;).ToString.ToLower = &quot;true&quot;]" DisplayName="Check if SplittingDoc is True" sap2010:WorkflowViewState.IdRef="If_40">
                    <If.Then>
                      <ForEach x:TypeArguments="x:String" DisplayName="Split Doc for each file in ListDownloadedFiles" sap2010:WorkflowViewState.IdRef="ForEach`1_17" Values="[ListDownloadedFiles]">
                        <ActivityAction x:TypeArguments="x:String">
                          <ActivityAction.Argument>
                            <DelegateInArgument x:TypeArguments="x:String" Name="attachment" />
                          </ActivityAction.Argument>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_53">
                            <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From{{&quot;documentPath&quot;,attachment},{&quot;logFile&quot;,logFile},{&quot;configurationFolder&quot;,configurationFolder},{&quot;tenantID&quot;,tenantID},{&quot;projectPath&quot;,projectPath},{&quot;miscValues&quot;,miscValues},{&quot;promptPath&quot;,ConfigurationFolder + &quot;\GenAISplit1.txt&quot;},{&quot;promptPath2&quot;,ConfigurationFolder + &quot;\GenAISplit2.txt&quot;},{&quot;subDownloadFolder&quot;,configurationFolder+&quot;\OutlookDownloads\&quot;+DownloadTo+seqnumber},{&quot;customPrompt&quot;,ConfigurationFolder + &quot;\GenAISplit1_Ext.txt&quot;},{&quot;customPrompt1&quot;,ConfigurationFolder + &quot;\GenAISplit2_Ext.txt&quot;}}]" ContinueOnError="True" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_2" WorkflowFile="[projectPath+&quot;\Split.xaml&quot;]" />
                          </Sequence>
                        </ActivityAction>
                      </ForEach>
                    </If.Then>
                  </If>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_42">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Boolean">[attachmentsAvail]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                    </Assign.Value>
                  </Assign>
                  <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[configurationFolder+&quot;\OutlookDownloads\&quot;+DownloadTo+seqnumber]" DisplayName="Get Files in OutlookDownloads" FileType="All" Files="[ListDownloadedFiles]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_8" IncludeSubDir="True" />
                  <TryCatch DisplayName="TryCatch Get Subject" sap2010:WorkflowViewState.IdRef="TryCatch_7">
                    <TryCatch.Try>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_43">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[subject]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[email.Subject]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </TryCatch.Try>
                    <TryCatch.Catches>
                      <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_7">
                        <ActivityAction x:TypeArguments="s:Exception">
                          <ActivityAction.Argument>
                            <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                          </ActivityAction.Argument>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_44">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[subject]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">No Subject</InArgument>
                            </Assign.Value>
                          </Assign>
                        </ActivityAction>
                      </Catch>
                    </TryCatch.Catches>
                  </TryCatch>
                  <ForEach x:TypeArguments="x:String" DisplayName="ForEach File in OutlookDownloads" sap2010:WorkflowViewState.IdRef="ForEach`1_13" Values="[ListDownloadedFiles]">
                    <ActivityAction x:TypeArguments="x:String">
                      <ActivityAction.Argument>
                        <DelegateInArgument x:TypeArguments="x:String" Name="item" />
                      </ActivityAction.Argument>
                      <Sequence DisplayName="Inside For each file in OutlookDownloads" sap2010:WorkflowViewState.IdRef="Sequence_37">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_45">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Boolean">[attachmentsAvail]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Switch x:TypeArguments="x:Boolean" DisplayName="Rename .PDF files if any" Expression="[item.contains(&quot;.PDF&quot;)]" sap2010:WorkflowViewState.IdRef="Switch`1_7">
                          <Sequence x:Key="True" DisplayName="PDF Sequence" sap2010:WorkflowViewState.IdRef="Sequence_30">
                            <Sequence.Variables>
                              <Variable x:TypeArguments="x:String" Name="pdfFileName" />
                              <Variable x:TypeArguments="x:String" Name="copiedFilePath" />
                            </Sequence.Variables>
                            <Assign DisplayName="Assign pdfFileName" sap2010:WorkflowViewState.IdRef="Assign_34">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[pdfFileName]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[Path.GetFileName(item).replace(".PDF","_copy.pdf")]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <ias:File_Copy ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Copy File" sap2010:WorkflowViewState.IdRef="File_Copy_4" OutputFile="[copiedFilePath]" OverwriteFile="False" Source="[item]" Target="[Path.GetDirectoryName(item)]" TargetFilename="[pdfFileName]" />
                            <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_6" Source="[item]" />
                            <Assign DisplayName="Assign attachment+_Copy" sap2010:WorkflowViewState.IdRef="Assign_35">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[item]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[item.replace(".PDF","_copy.pdf")]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </Sequence>
                        </Switch>
                        <Switch x:TypeArguments="x:Boolean" DisplayName=".pdf Check If attachment.contains(&quot;.pdf&quot;)" Expression="[item.contains(&quot;.pdf&quot;)]" sap2010:WorkflowViewState.IdRef="Switch`1_9">
                          <Sequence x:Key="True" DisplayName="If .pdf extension" sap2010:WorkflowViewState.IdRef="Sequence_32">
                            <Sequence.Variables>
                              <Variable x:TypeArguments="x:String" Name="MovedFilePath" />
                            </Sequence.Variables>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_46">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Boolean">[pdfFileAvailable]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_36">
                              <Assign.To>
                                <OutArgument x:TypeArguments="si:FileInfo">[fileDetails]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="si:FileInfo">[New FileInfo(item)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign DisplayName="fileLength Assign" sap2010:WorkflowViewState.IdRef="Assign_37">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Double">[filelength]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Double">[(fileDetails.length/1024)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <If Condition="[filelength&lt;=5120]" DisplayName="Check file Length" sap2010:WorkflowViewState.IdRef="If_26">
                              <If.Then>
                                <Sequence DisplayName="ProcessEmailAttachment" sap2010:WorkflowViewState.IdRef="Sequence_31">
                                  <Sequence.Variables>
                                    <Variable x:TypeArguments="x:String" Name="variable1" />
                                  </Sequence.Variables>
                                  <Assign DisplayName="Assign pdfFileAvailable to True" sap2010:WorkflowViewState.IdRef="Assign_38">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Boolean">[pdfFileAvailable]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_5" MethodName="Add">
                                    <InvokeMethod.TargetObject>
                                      <InArgument x:TypeArguments="scg:List(x:String)">[emailsubjects]</InArgument>
                                    </InvokeMethod.TargetObject>
                                    <InArgument x:TypeArguments="x:String">[item.Substring(item.LastIndexOf("\"c)+1,(item.Length()-item.LastIndexOf("\"c))-1)]</InArgument>
                                  </InvokeMethod>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_39">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Int32">[pdfs_count]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Int32">[pdfs_count+1]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </Sequence>
                              </If.Then>
                            </If>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_73">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[Path.GetFilename(item)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Switch x:TypeArguments="x:Boolean" DisplayName="Move Files to InProgress Folder" Expression="[ListInprogressNames.Contains(Path.GetFileName(item))]" sap2010:WorkflowViewState.IdRef="Switch`1_8">
                              <Sequence x:Key="True" sap2010:WorkflowViewState.IdRef="Sequence_48">
                                <If Condition="[System.Text.RegularExpressions.Regex.IsMatch(InvoiceFileName, &quot;[:#&amp;@`'*/\?&lt;&gt;|&quot;&quot;]&quot;)]" DisplayName="Check if the FileName Contains the special Characters" sap2010:WorkflowViewState.IdRef="If_36">
                                  <If.Then>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_68">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(InvoiceFileName, "[:#&amp;@`'*/\?&lt;&gt;|""]", "_")]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                  </If.Then>
                                </If>
                                <ias:File_Move ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Move File" sap2010:WorkflowViewState.IdRef="File_Move_7" OutputFile="[MovedFilePath]" OverwriteFile="False" Source="[item]" Target="[configurationFolder+&quot;\InProgress&quot;]" targetName="[InvoiceFileName.Replace(&quot;.pdf&quot;,&quot;_&quot;+FileRenameFormat+&quot;_&quot;+RenameCounter.Tostring+&quot;.pdf&quot;)]" />
                              </Sequence>
                              <Sequence x:Key="False" sap2010:WorkflowViewState.IdRef="Sequence_49">
                                <If Condition="[System.Text.RegularExpressions.Regex.IsMatch(InvoiceFileName, &quot;[:#&amp;@`'*/\?&lt;&gt;|&quot;&quot;]&quot;)]" DisplayName="Check if the FileName Contains the special Characters" sap2010:WorkflowViewState.IdRef="If_38">
                                  <If.Then>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_74">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(InvoiceFileName, "[:#&amp;@`'*/\?&lt;&gt;|""]", "_")]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                  </If.Then>
                                </If>
                                <ias:File_Move ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Move File" sap2010:WorkflowViewState.IdRef="File_Move_4" OutputFile="[MovedFilePath]" OverwriteFile="False" Source="[item]" Target="[configurationFolder+&quot;\InProgress&quot;]" targetName="[InvoiceFileName]" />
                              </Sequence>
                            </Switch>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_40">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Int32">[RenameCounter]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Int32">[RenameCounter+1]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[inProgressFolder]" DisplayName="Get Files in Directory - inProgressFolder for deletion" FileType="All" Files="[inProgressFiles]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_6" IncludeSubDir="True" />
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_41">
                              <Assign.To>
                                <OutArgument x:TypeArguments="scg:List(x:String)">[ListInprogressNames]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <ForEach x:TypeArguments="x:String" DisplayName="Get FileNames from Inprogress" sap2010:WorkflowViewState.IdRef="ForEach`1_11" Values="[inProgressFiles]">
                              <ActivityAction x:TypeArguments="x:String">
                                <ActivityAction.Argument>
                                  <DelegateInArgument x:TypeArguments="x:String" Name="item1" />
                                </ActivityAction.Argument>
                                <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_8" MethodName="Add">
                                  <InvokeMethod.TargetObject>
                                    <InArgument x:TypeArguments="scg:List(x:String)">[ListInprogressNames]</InArgument>
                                  </InvokeMethod.TargetObject>
                                  <InArgument x:TypeArguments="x:String">[Path.GetFileName(item1).Tostring]</InArgument>
                                </InvokeMethod>
                              </ActivityAction>
                            </ForEach>
                          </Sequence>
                          <Sequence x:Key="False" sap2010:WorkflowViewState.IdRef="Sequence_50">
                            <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_7" Source="[item]" />
                          </Sequence>
                        </Switch>
                        <If Condition="[item.EndsWith(&quot;.pdf&quot;) OR item.EndsWith(&quot;.PDF&quot;) OR item.EndsWith(&quot;.txt&quot;) OR item.EndsWith(&quot;.jpg&quot;) OR item.EndsWith(&quot;.jpeg&quot;) OR item.EndsWith(&quot;.png&quot;) OR item.EndsWith(&quot;.zip&quot;) OR item.EndsWith(&quot;.ZIP&quot;)]" DisplayName="Check other file extensions" sap2010:WorkflowViewState.IdRef="If_33">
                          <If.Else>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_39">
                              <Sequence.Variables>
                                <Variable x:TypeArguments="x:String" Name="status" />
                                <Variable x:TypeArguments="x:String" Name="statusComments" />
                                <Variable x:TypeArguments="x:String" Name="fileName" />
                                <Variable x:TypeArguments="x:String" Name="message" />
                                <Variable x:TypeArguments="x:String" Name="emailSubject" />
                                <Variable x:TypeArguments="x:String" Name="emailReceivedTime" />
                                <Variable x:TypeArguments="x:String" Name="notificationRequestStr" />
                                <Variable x:TypeArguments="njl:JToken" Name="notificationToken" />
                                <Variable x:TypeArguments="iru:ResponseObject" Name="notificationResponse" />
                                <Variable x:TypeArguments="x:Int32" Name="notificationResponseCode" />
                              </Sequence.Variables>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_54">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[status]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_55">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[statusComments]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">Wrong Format of the document</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_56">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[fileName]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[Path.GetFileName(item)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_57">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[message]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">["RPA M3 IP" +" - " + status + "( "+statusComments+")"]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_58">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[emailSubject]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[email.Subject]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_59">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[emailReceivedTime]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[email.ReceivedTime]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <If Condition="[emailSubject.contains(&quot;'&quot;)]" sap2010:WorkflowViewState.IdRef="If_32">
                                <If.Then>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_60">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[emailSubject]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[emailSubject.Replace("'","")]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </If.Then>
                              </If>
                              <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_2" Template="{}{&#xA;    'message': '{{%msg%}}',&#xA;    'parameters': [&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_STATUS',&#xA;            'value': '{{%status%}}',&#xA;            'label': 'Status',&#xA;            'readOnly': true&#xA;        },&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_Subject',&#xA;            'value': '{{%Subject%}}',&#xA;            'label': 'Subject',&#xA;            'readOnly': true&#xA;        },&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_RcvdTime',&#xA;            'value': '{{%RcvdTime%}}',&#xA;            'label': 'Recieved Time',&#xA;            'readOnly': true&#xA;        },&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_FileName',&#xA;            'value': '{{%fileName%}}',&#xA;            'label': 'File Name',&#xA;            'readOnly': true&#xA;        },&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_Remarks',&#xA;            'value': '{{%Remarks%}}',&#xA;            'label': 'Remarks',&#xA;            'readOnly': true&#xA;        }&#xA;    ],&#xA;    'category': 'RPA',&#xA;    'distribution': [&#xA;        {&#xA;            'identifier': '{{%userIdentifier%}}',&#xA;            'type': '{{%distributionType%}}',&#xA;            'sendMail': false&#xA;        }&#xA;    ]&#xA;}&#xA;" Text="[notificationRequestStr]">
                                <ias:Template_Apply.Values>
                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                    <scg:List x:TypeArguments="x:String" Capacity="8">
                                      <x:String>status</x:String>
                                      <x:String>userIdentifier</x:String>
                                      <x:String>distributionType</x:String>
                                      <x:String>Remarks</x:String>
                                      <x:String>RcvdTime</x:String>
                                      <x:String>Subject</x:String>
                                      <x:String>msg</x:String>
                                      <x:String>fileName</x:String>
                                    </scg:List>
                                    <scg:List x:TypeArguments="x:String" Capacity="8">
                                      <x:String>status</x:String>
                                      <x:String>userIdentifier</x:String>
                                      <x:String>distributionType</x:String>
                                      <x:String>statusComments</x:String>
                                      <x:String>emailReceivedTime</x:String>
                                      <x:String>emailSubject</x:String>
                                      <x:String>message</x:String>
                                      <x:String>fileName</x:String>
                                    </scg:List>
                                  </scg:List>
                                </ias:Template_Apply.Values>
                              </ias:Template_Apply>
                              <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_2" JTokenObject="[notificationToken]" JTokenString="[notificationRequestStr]" />
                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="False" DisplayName="IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_2" PostData="[notificationToken.tostring]" Response="[notificationResponse]" ResponseCode="[notificationResponseCode]" Url="[tenantID+ &quot;IONSERVICES/process/application/v1/pulse/notification/create&quot;]">
                                <iai:IONAPIRequestWizard.QueryParameters>
                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>logicalId</x:String>
                                    </scg:List>
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>lid://infor.rpa.1</x:String>
                                    </scg:List>
                                  </scg:List>
                                </iai:IONAPIRequestWizard.QueryParameters>
                              </iai:IONAPIRequestWizard>
                            </Sequence>
                          </If.Else>
                        </If>
                      </Sequence>
                    </ActivityAction>
                  </ForEach>
                  <If Condition="[not attachmentsAvail]" sap2010:WorkflowViewState.IdRef="If_35">
                    <If.Then>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_40">
                        <Sequence.Variables>
                          <Variable x:TypeArguments="x:String" Name="status" />
                          <Variable x:TypeArguments="x:String" Name="statusComments" />
                          <Variable x:TypeArguments="x:String" Name="fileName" />
                          <Variable x:TypeArguments="x:String" Name="message" />
                          <Variable x:TypeArguments="x:String" Name="emailSubject" />
                          <Variable x:TypeArguments="x:String" Name="emailReceivedTime" />
                          <Variable x:TypeArguments="x:String" Name="notificationRequestStr" />
                          <Variable x:TypeArguments="njl:JToken" Name="notificationToken" />
                          <Variable x:TypeArguments="iru:ResponseObject" Name="notificationResponse" />
                          <Variable x:TypeArguments="x:Int32" Name="notificationResponseCode" />
                        </Sequence.Variables>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_61">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[status]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_62">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[statusComments]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">["No attachments available."]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_63">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[message]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">["RPA M3 IP" +" - " + status + "( "+statusComments+")"]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_64">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[emailSubject]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[email.Subject]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_65">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[emailReceivedTime]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[email.ReceivedTime]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_24" Line="No pdf files available to process" Source="[logFIle]" />
                        <If Condition="[emailSubject.contains(&quot;'&quot;)]" sap2010:WorkflowViewState.IdRef="If_34">
                          <If.Then>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_66">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[emailSubject]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[emailSubject.Replace("'","")]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </If.Then>
                        </If>
                        <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_3" Template="{}{&#xA;    'message': '{{%msg%}}',&#xA;    'parameters': [&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_STATUS',&#xA;            'value': '{{%status%}}',&#xA;            'label': 'Status',&#xA;            'readOnly': true&#xA;        },&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_Subject',&#xA;            'value': '{{%Subject%}}',&#xA;            'label': 'Subject',&#xA;            'readOnly': true&#xA;        },&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_RcvdTime',&#xA;            'value': '{{%RcvdTime%}}',&#xA;            'label': 'Recieved Time',&#xA;            'readOnly': true&#xA;        },&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_Remarks',&#xA;            'value': '{{%Remarks%}}',&#xA;            'label': 'Remarks',&#xA;            'readOnly': true&#xA;        }&#xA;    ],&#xA;    'category': 'RPA',&#xA;    'distribution': [&#xA;        {&#xA;            'identifier': '{{%userIdentifier%}}',&#xA;            'type': '{{%distributionType%}}',&#xA;            'sendMail': false&#xA;        }&#xA;    ]&#xA;}&#xA;" Text="[notificationRequestStr]">
                          <ias:Template_Apply.Values>
                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                              <scg:List x:TypeArguments="x:String" Capacity="8">
                                <x:String>status</x:String>
                                <x:String>userIdentifier</x:String>
                                <x:String>distributionType</x:String>
                                <x:String>Remarks</x:String>
                                <x:String>RcvdTime</x:String>
                                <x:String>Subject</x:String>
                                <x:String>msg</x:String>
                                <x:String>fileName</x:String>
                              </scg:List>
                              <scg:List x:TypeArguments="x:String" Capacity="8">
                                <x:String>status</x:String>
                                <x:String>userIdentifier</x:String>
                                <x:String>distributionType</x:String>
                                <x:String>statusComments</x:String>
                                <x:String>emailReceivedTime</x:String>
                                <x:String>emailSubject</x:String>
                                <x:String>message</x:String>
                                <x:String>fileName</x:String>
                              </scg:List>
                            </scg:List>
                          </ias:Template_Apply.Values>
                        </ias:Template_Apply>
                        <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_3" JTokenObject="[notificationToken]" JTokenString="[notificationRequestStr]" />
                        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_3" PostData="[notificationToken.tostring]" Response="[notificationResponse]" ResponseCode="[notificationResponseCode]" Url="[tenantID+ &quot;IONSERVICES/process/application/v1/pulse/notification/create&quot;]">
                          <iai:IONAPIRequestWizard.QueryParameters>
                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                <x:String>logicalId</x:String>
                              </scg:List>
                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                <x:String>lid://infor.rpa.1</x:String>
                              </scg:List>
                            </scg:List>
                          </iai:IONAPIRequestWizard.QueryParameters>
                        </iai:IONAPIRequestWizard>
                      </Sequence>
                    </If.Then>
                  </If>
                </Sequence>
                <Switch x:TypeArguments="x:Boolean" DisplayName="Is any PDF file available" Expression="[pdfFileAvailable]" sap2010:WorkflowViewState.IdRef="Switch`1_10">
                  <ias:Append_Line ErrorCode="{x:Null}" x:Key="False" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_18" Line="[&quot;No pdf files available to process in email - &quot;+email.subject]" Source="[logFIle]" />
                </Switch>
              </Sequence>
            </TryCatch.Try>
            <TryCatch.Catches>
              <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_6">
                <ActivityAction x:TypeArguments="s:Exception">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                  </ActivityAction.Argument>
                  <Sequence DisplayName="Catch Block For Each Mail - Sequence" sap2010:WorkflowViewState.IdRef="Sequence_44">
                    <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_4">
                      <iad:CommentOut.Activities>
                        <Assign DisplayName="Assign strException" sap2010:WorkflowViewState.IdRef="Assign_81">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[strException]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[exception.GetType().Name]</InArgument>
                          </Assign.Value>
                        </Assign>
                      </iad:CommentOut.Activities>
                    </iad:CommentOut>
                    <Assign DisplayName="Assign strException" sap2010:WorkflowViewState.IdRef="Assign_90">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[strException]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[exception.Message]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <ias:StudioWriteLine ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Write Line" sap2010:WorkflowViewState.IdRef="StudioWriteLine_1" Line="[&quot;Error occurred while downloading attachments from email with subject - &quot;+email.Subject]" />
                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line - Catch block inside for each email" sap2010:WorkflowViewState.IdRef="Append_Line_19" Line="[&quot;Exception occurred in TryCatch - Process Each email Inside for each sequence&quot;+environment.newline+&quot;Exception message is - &quot;+exception.message+Environment.newline+&quot;Email Subject is -&quot;+email.subject+environment.newline+&quot;Received time is -&quot;+email.ReceivedTime.ToString]" Source="[logFile]" />
                  </Sequence>
                </ActivityAction>
              </Catch>
            </TryCatch.Catches>
            <TryCatch.Finally>
              <If Condition="[Not blnDownload]" DisplayName="Mark emails as Unread if Download Fails" sap2010:WorkflowViewState.IdRef="If_42">
                <If.Then>
                  <Sequence DisplayName="Failure Data Collection" sap2010:WorkflowViewState.IdRef="Sequence_56">
                    <Sequence.Variables>
                      <Variable x:TypeArguments="x:String" Name="strFailureData">
                        <Variable.Default>
                          <Literal x:TypeArguments="x:String" Value="" />
                        </Variable.Default>
                      </Variable>
                    </Sequence.Variables>
                    <Assign DisplayName="Assign blnFailureExists" sap2010:WorkflowViewState.IdRef="Assign_82">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:Boolean">[blnFailureExists]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign DisplayName="Assign MailCounter" sap2010:WorkflowViewState.IdRef="Assign_83">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[MailCounter]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[Right(FileRenameFormat,1)+intFailureMailCounter.tostring]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign DisplayName="Assign strFailureData" sap2010:WorkflowViewState.IdRef="Assign_84">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[strFailureData]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String" xml:space="preserve">[("🗂️Email Subject: " &amp; email.Subject).PadRight(60, " "c) &amp;
("⚠️ Exception Message: " &amp; strException).PadRight(60, " "c) &amp;
("🕒 Email Received Time: " &amp; email.ReceivedTime.ToString).PadRight(60, " "c)]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign DisplayName="Assign strJsonString" sap2010:WorkflowViewState.IdRef="Assign_85">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[strJsonString]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(strJsonString="", 
"{" &amp; 
" 'dataType': 'STRING'," &amp;
" 'name': '" &amp; MailCounter &amp; "'," &amp;
" 'value': '" &amp; strFailureData &amp; "'," &amp;
" 'label': 'Failure Details - " &amp; MailCounter &amp; "'," &amp; 
" 'readOnly': true" &amp; 
" }",
strJsonString &amp; "," &amp; 
"{" &amp; 
" 'dataType': 'STRING'," &amp;
" 'name': '" &amp; MailCounter &amp; "'," &amp;
" 'value': '" &amp; strFailureData &amp; "'," &amp;
" 'label': 'Failure Details - " &amp; MailCounter &amp; "'," &amp; 
" 'readOnly': true" &amp; 
" }")]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign DisplayName="Assign MailCounter NULL" sap2010:WorkflowViewState.IdRef="Assign_86">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[MailCounter]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">
                          <Literal x:TypeArguments="x:String" Value="" />
                        </InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign DisplayName="Assign intFailureMailCounter" sap2010:WorkflowViewState.IdRef="Assign_87">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:Int32">[intFailureMailCounter]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:Int32">[intFailureMailCounter+1]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Switch x:TypeArguments="x:String" DisplayName="Mark Failure emails as Unread invoiceSource" Expression="[invoiceSource]" sap2010:WorkflowViewState.IdRef="Switch`1_11">
                      <iae:MarkOutlookAsRead ErrorCode="{x:Null}" x:Key="OutlookClientEmail" ContinueOnError="True" DisplayName="Mark Outlook Emails as Read/Unread" sap2010:WorkflowViewState.IdRef="MarkOutlookAsRead_1" Mail="[email]" MarkAsRead="False" />
                      <x:Null x:Key="OutlookGraphEmail" />
                    </Switch>
                  </Sequence>
                </If.Then>
              </If>
            </TryCatch.Finally>
          </TryCatch>
        </ActivityAction>
      </ForEach>
      <ias:Append_Line ErrorCode="{x:Null}" x:Key="False" ContinueOnError="False" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_20" Line="[&quot;No emails available to process in the Sub Mails List - &quot; + &quot; &quot; + seqnumber]" Source="[logFile]" />
    </Switch>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_77" sap:VirtualizedContainerService.HintSize="532,60" />
      <sap2010:ViewStateData Id="Assign_78" sap:VirtualizedContainerService.HintSize="532,60" />
      <sap2010:ViewStateData Id="Assign_31" sap:VirtualizedContainerService.HintSize="532,60" />
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="532,60" />
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="532,60" />
      <sap2010:ViewStateData Id="Assign_89" sap:VirtualizedContainerService.HintSize="532,60" />
      <sap2010:ViewStateData Id="MessageBox_7" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_25" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Directory_GetFiles_5" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_67" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="InvokeMethod_7" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="ForEach`1_10" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_79" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="DownloadOutlookAttachment_5" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_80" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_41" sap:VirtualizedContainerService.HintSize="264,246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_6" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_33" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Directory_GetFiles_9" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_3" sap:VirtualizedContainerService.HintSize="242,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_21" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Directory_Extract_1" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="File_Delete_8" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_36" sap:VirtualizedContainerService.HintSize="222,270">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_27" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_33" sap:VirtualizedContainerService.HintSize="222,175">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_8" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Directory_GetFiles_10" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_75" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_76" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="File_Copy_6" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="File_Delete_9" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_55" sap:VirtualizedContainerService.HintSize="264,308">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_41" sap:VirtualizedContainerService.HintSize="464,456">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_54" sap:VirtualizedContainerService.HintSize="486,680">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_18" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Directory_GetFiles_11" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_2" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_53" sap:VirtualizedContainerService.HintSize="222,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_17" sap:VirtualizedContainerService.HintSize="287,294">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_40" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_42" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Directory_GetFiles_8" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_43" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_44" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_7" sap:VirtualizedContainerService.HintSize="404.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_7" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_45" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_34" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="File_Copy_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="File_Delete_6" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_35" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_30" sap:VirtualizedContainerService.HintSize="264,407">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_7" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_46" sap:VirtualizedContainerService.HintSize="506,60" />
      <sap2010:ViewStateData Id="Assign_36" sap:VirtualizedContainerService.HintSize="506,60" />
      <sap2010:ViewStateData Id="Assign_37" sap:VirtualizedContainerService.HintSize="506,60" />
      <sap2010:ViewStateData Id="Assign_38" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_5" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Assign_39" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_31" sap:VirtualizedContainerService.HintSize="264,462">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_26" sap:VirtualizedContainerService.HintSize="506,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_73" sap:VirtualizedContainerService.HintSize="506,57" />
      <sap2010:ViewStateData Id="Assign_68" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_36" sap:VirtualizedContainerService.HintSize="464,210">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="File_Move_7" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Sequence_48" sap:VirtualizedContainerService.HintSize="486,391">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_74" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_38" sap:VirtualizedContainerService.HintSize="464,216">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="File_Move_4" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Sequence_49" sap:VirtualizedContainerService.HintSize="486,402">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_8" sap:VirtualizedContainerService.HintSize="506,617">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_40" sap:VirtualizedContainerService.HintSize="506,62" />
      <sap2010:ViewStateData Id="Directory_GetFiles_6" sap:VirtualizedContainerService.HintSize="506,22" />
      <sap2010:ViewStateData Id="Assign_41" sap:VirtualizedContainerService.HintSize="506,62" />
      <sap2010:ViewStateData Id="InvokeMethod_8" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="ForEach`1_11" sap:VirtualizedContainerService.HintSize="506,286.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_32" sap:VirtualizedContainerService.HintSize="528,1830">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="File_Delete_7" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_50" sap:VirtualizedContainerService.HintSize="222,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_9" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_54" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_55" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_56" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_57" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_58" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_59" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_60" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_32" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Template_Apply_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_39" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_33" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_37" sap:VirtualizedContainerService.HintSize="264,457">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_13" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_61" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_62" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_63" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_64" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_65" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_24" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_66" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_34" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Template_Apply_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_40" sap:VirtualizedContainerService.HintSize="264,934.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_35" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_52" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_18" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Switch`1_10" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_35" sap:VirtualizedContainerService.HintSize="264,1090">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_81" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="CommentOut_4" sap:VirtualizedContainerService.HintSize="242,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_90" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="StudioWriteLine_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Append_Line_19" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_44" sap:VirtualizedContainerService.HintSize="264,406">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Catch`1_6" sap:VirtualizedContainerService.HintSize="468,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_82" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_83" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_84" sap:VirtualizedContainerService.HintSize="242,64" />
      <sap2010:ViewStateData Id="Assign_85" sap:VirtualizedContainerService.HintSize="242,64" />
      <sap2010:ViewStateData Id="Assign_86" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_87" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="MarkOutlookAsRead_1" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Switch`1_11" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_56" sap:VirtualizedContainerService.HintSize="264,783">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_42" sap:VirtualizedContainerService.HintSize="464,931">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_6" sap:VirtualizedContainerService.HintSize="482,1158">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_9" sap:VirtualizedContainerService.HintSize="512,1306">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_20" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Switch`1_5" sap:VirtualizedContainerService.HintSize="532,1512">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="554,2236">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="594,2916" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>