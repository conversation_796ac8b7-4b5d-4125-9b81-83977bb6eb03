﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow" this:Workflow.documentPath="C:\CSIInvoiceProcessing\Inprogress\687C64007.pdf" this:Workflow.tenantID="https://mingle-cqa-ionapi.cqa.inforcloudsuite.com/CSI10WIDGET_AX1/" this:Workflow.promptPath="C:\CSIInvoiceProcessing\UEDP.txt"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iro="clr-namespace:Infor.RPA.OCR;assembly=Infor.RPA.OCR"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:this="clr-namespace:RehostedWorkflowDesigner"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="ocrValues" Type="OutArgument(s:String[])" />
    <x:Property Name="documentPath" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="promptPath" Type="InArgument(x:String)" />
    <x:Property Name="feedback" Type="OutArgument(njl:JToken)" />
    <x:Property Name="genAiVersion" Type="InArgument(x:String)" />
    <x:Property Name="genAiModel" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.OCR</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System.Text.RegularExpressions</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.OCR</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="GenAI Sequence" sap2010:WorkflowViewState.IdRef="Sequence_3">
    <Sequence.Variables>
      <Variable x:TypeArguments="scg:Dictionary(x:String, scg:List(x:String))" Name="ocrDictionary" />
      <Variable x:TypeArguments="njl:JToken" Name="values" />
      <Variable x:TypeArguments="x:Int32" Name="responseCode" />
      <Variable x:TypeArguments="x:String" Name="promptText" />
      <Variable x:TypeArguments="x:String" Name="promptRequest" />
    </Sequence.Variables>
    <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Read Text File" sap2010:WorkflowViewState.IdRef="File_Read_1" Source="[promptPath]" Text="[promptText]" />
    <Assign sap2010:WorkflowViewState.IdRef="Assign_1">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(x:String))">[ocrDictionary]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:Dictionary(x:String, scg:List(x:String))">[New Dictionary (Of String, List(Of String))]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_2">
      <Assign.To>
        <OutArgument x:TypeArguments="s:String[]">[ocrValues]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="s:String[]">[New String(20) {"","","", "","","","","","","","","","","","","","","","","",""}]</InArgument>
      </Assign.Value>
    </Assign>
    <iro:DocumentOC Pages="{x:Null}" ActivityID="f69e5a30-0b3c-4f29-9d8a-1e6947a44cd8" ContinueOnError="True" DisplayName="Get OCR Text" ErrorCode="[responseCode]" ExecuteFile="True" FilePath="[documentPath]" sap2010:WorkflowViewState.IdRef="DocumentOC_1" ResponseObject="[values]" />
    <If Condition="[responseCode = 200]" sap2010:WorkflowViewState.IdRef="If_1">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_1">
          <Sequence.Variables>
            <Variable x:TypeArguments="x:String" Name="ocrOutput" />
            <Variable x:TypeArguments="x:String" Name="IonBody" />
            <Variable x:TypeArguments="njl:JToken" Name="IonBodyJson" />
            <Variable x:TypeArguments="njl:JToken" Name="genAIRequestToken" />
            <Variable x:TypeArguments="iru:ResponseObject" Name="genAIResponse" />
            <Variable x:TypeArguments="njl:JToken" Name="out1" />
            <Variable x:TypeArguments="x:String" Name="out2" />
            <Variable x:TypeArguments="njl:JToken" Name="jout" />
            <Variable x:TypeArguments="njl:JArray" Name="jArr1" />
            <Variable x:TypeArguments="x:Int32" Name="i" />
            <Variable x:TypeArguments="njl:JArray" Name="dn" />
            <Variable x:TypeArguments="x:String" Name="outputStructure" />
            <Variable x:TypeArguments="x:String" Name="value" />
          </Sequence.Variables>
          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_11">
            <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_1">
              <iad:CommentOut.Activities>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_28">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[promptText.Replace("{text}",values.tostring)]</InArgument>
                  </Assign.Value>
                </Assign>
              </iad:CommentOut.Activities>
            </iad:CommentOut>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_29">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[promptText.Replace("'","\'")]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_59">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[outputStructure]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[promptRequest.Split(New String() {"====="}, StringSplitOptions.None)(1)]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_60">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[promptRequest.Split(New String() {"====="}, StringSplitOptions.None)(0)]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_61">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[value]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[Regex.Replace(Values.ToString(), "[^A-Za-z0-9\s!@#$%^&amp;*()_+={}\[\]:;,'&lt;&gt;.?//|-]", String.Empty).Replace("'","\'")]</InArgument>
              </Assign.Value>
            </Assign>
            <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Latest Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_5" Template="{}{&#xA;  'model': '{{%model%}}',&#xA;  'version': '{{%version%}}',&#xA;  'temperature': 0,&#xA;  'max_response': 8192,&#xA;  'stop_sequence': [&#xA;    'Explanations',&#xA;    'Explanation'&#xA;  ],&#xA;  'system': 'You are an expert document based Question-Answering tool. Your task is to answer the questions using the ocr text.',&#xA;  'messages': [&#xA;      {&#xA;        'role': 'user',&#xA;        'content': [&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%prompt%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%ocrText%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Use the following output format for your response. Your response should only have the JSON without code blocks as the response. Do not include any of your analysis statements before or after the JSON in the response. Do not add any additional objects to the JSON that are not present in the following Output Format Structure.'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%outputStructure%}}'&#xA;          }&#xA;        ]&#xA;      },&#xA;      {&#xA;        'role': 'assistant',&#xA;        'content': [&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Based on the invoice document, here are the extracted values in the requested JSON format without any code blocks:'&#xA;          }&#xA;        ]&#xA;      }&#xA;  ]&#xA;}&#xA;" Text="[IonBody]">
              <ias:Template_Apply.Values>
                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                  <scg:List x:TypeArguments="x:String" Capacity="8">
                    <x:String>prompt</x:String>
                    <x:String>model</x:String>
                    <x:String>version</x:String>
                    <x:String>ocrText</x:String>
                    <x:String>outputStructure</x:String>
                  </scg:List>
                  <scg:List x:TypeArguments="x:String" Capacity="8">
                    <x:String>promptRequest</x:String>
                    <x:String>genAiModel</x:String>
                    <x:String>genAiVersion</x:String>
                    <x:String>value</x:String>
                    <x:String>outputStructure</x:String>
                  </scg:List>
                </scg:List>
              </ias:Template_Apply.Values>
            </ias:Template_Apply>
            <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_2">
              <iad:CommentOut.Activities>
                <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_4" Template="{}{ &#xA;'config': { &#xA;'temperature': 0, &#xA;'max_response':8192,&#xA;'stop_sequence': [&#xA;                      'Explanations','Explanation'&#xA;                ]   &#xA;}, &#xA;'prompt':'{{%prompt%}}',&#xA; 'model': '{{%model%}}',   'version': '{{%version%}}'&#xA;" Text="[IonBody]">
                  <ias:Template_Apply.Values>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>prompt</x:String>
                        <x:String>model</x:String>
                        <x:String>version</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>promptRequest</x:String>
                        <x:String>genAiModel</x:String>
                        <x:String>genAiVersion</x:String>
                      </scg:List>
                    </scg:List>
                  </ias:Template_Apply.Values>
                </ias:Template_Apply>
              </iad:CommentOut.Activities>
            </iad:CommentOut>
          </Sequence>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_3">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[ocrOutput]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[values.toString.Replace("'","")]</InArgument>
            </Assign.Value>
          </Assign>
          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_5">
            <Sequence.Variables>
              <Variable x:TypeArguments="x:Int32" Name="genAIRespCode" />
            </Sequence.Variables>
            <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_1" JTokenObject="[genAIRequestToken]" JTokenString="[IonBody]" />
            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_2" PostData="[genAIRequestToken.tostring]" Response="[genAIResponse]" ResponseCode="[genAIRespCode]" Url="[tenantID + &quot;GENAI/chatsvc/api/v1/messages&quot;]">
              <iai:IONAPIRequestWizard.Headers>
                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>x-infor-logicalidprefix</x:String>
                  </scg:List>
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>lid://infor.syteline</x:String>
                  </scg:List>
                </scg:List>
              </iai:IONAPIRequestWizard.Headers>
              <iai:IONAPIRequestWizard.QueryParameters>
                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                  <scg:List x:TypeArguments="x:String" Capacity="0" />
                  <scg:List x:TypeArguments="x:String" Capacity="0" />
                </scg:List>
              </iai:IONAPIRequestWizard.QueryParameters>
            </iai:IONAPIRequestWizard>
            <If Condition="[genAIRespCode = 200]" sap2010:WorkflowViewState.IdRef="If_4">
              <If.Then>
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_10">
                  <Sequence.Variables>
                    <Variable x:TypeArguments="njl:JArray" Name="jArr2" />
                    <Variable x:TypeArguments="scg:List(x:String)" Name="lines" />
                  </Sequence.Variables>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_4">
                    <Assign.To>
                      <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(genAIResponse.ReadAsText)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_5">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[out2]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[out1("content").ToString.Replace("\n","")]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_6">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[out2]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[(out2.ToString.Replace("None","''")).Replace("''''","''")]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_58">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[out2]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[out2.Substring(out2.Indexof("{"c),out2.LastIndexOf("}"c) - out2.Indexof("{"c)+1)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_3" JTokenObject="[jout]" JTokenString="[out2.ToString]" />
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_13">
                    <Assign.To>
                      <OutArgument x:TypeArguments="njl:JArray">[jArr1]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="njl:JArray">[JArray.Parse(jout("Details").ToString)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_14">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Int32">[i]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Int32">0</InArgument>
                    </Assign.Value>
                  </Assign>
                  <ForEach x:TypeArguments="njl:JToken" DisplayName="ForEach&lt;JToken&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_3" Values="[jArr1]">
                    <ActivityAction x:TypeArguments="njl:JToken">
                      <ActivityAction.Argument>
                        <DelegateInArgument x:TypeArguments="njl:JToken" Name="item" />
                      </ActivityAction.Argument>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_7">
                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_17">
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_15">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[ocrValues(i)]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[item.ToString]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_16">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:Int32">[i]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:Int32">[i+1]</InArgument>
                            </Assign.Value>
                          </Assign>
                        </Sequence>
                      </Sequence>
                    </ActivityAction>
                  </ForEach>
                  <If Condition="[ocrvalues(0).tostring=&quot;NotInvoice&quot;]" sap2010:WorkflowViewState.IdRef="If_17">
                    <If.Then>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_41">
                        <Assign.To>
                          <OutArgument x:TypeArguments="njl:JToken">[feedback]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="njl:JToken">[jout("Feedback".tostring)]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </If.Then>
                    <If.Else>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_56">
                        <Assign.To>
                          <OutArgument x:TypeArguments="njl:JToken">[feedback]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="njl:JToken">["Invoice"]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </If.Else>
                  </If>
                </Sequence>
              </If.Then>
            </If>
          </Sequence>
        </Sequence>
      </If.Then>
      <If.Else>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_2" />
      </If.Else>
    </If>
    <sads:DebugSymbol.Symbol>d2JDOlxVc2Vyc1xkbWl0dGFwYWxsaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYyXEdlbkFJIC0gQ2xhc3NpZmljYXRpb24ueGFtbF4BcgGjAQEEAZoCAbsCAQMBvAEB/wEBAk0D4gIOAgEBVQVVtwEDAYEBVgVdDgIBfV4FZQ4CAXlmBWadAgIBcWcF4AIKAgECVaYBVbQBAwGEAVWSAVWgAQMBggFbVFuBAQMBgAFYVVhkAgF+YzJjhAECAXxgM2A+AgF6ZpACZpoCAgF3ZpQBZqQBAgF1ZsEBZtEBAgFzZjFmVwIBcmcTZykCAQNpCdsCFAIBBt4CCd4CQgIBBXkLzgEWAgFZzwEL1gEUAgFV1wEL2gIWAgEHeg2FAR4CAXCGAQ2NARYCAWuOAQ2VARYCAWeWAQ2dARYCAWOeAQ2lARYCAV+mAQ25ASICAVu6AQ3NAR4CAVrUATbUAVcCAVjRATfRAUICAVbbAQ3bAd8BAgFQ3AEN7QEnAgFI7gEN2QISAgEIiwE4iwFWAgFuiAE5iAFIAgFskwE4kwGBAQIBapABOZABSgIBaJsBOJsBgQECAWaYATmYAUgCAWSjATijAbUBAgFioAE5oAFAAgFgpgGZDaYBpA0CAV2mAbIBpgGTDQIBXNsB0QHbAdwBAgFT2wGuAdsBwwECAVHcAf0B3AGbAgIBT9wBxALcAdUCAgFN3AGlAtwBtgICAUvcAdoC3AGSAwIBSe4BG+4BMgIBCfABEdcCHAIBC/UBE/wBHAIBRP0BE4QCHAIBQIUCE4wCHAIBO40CE5QCHAIBM5UCE5UC3gECAS6WAhOdAhwCASqeAhOlAhwCASamAhPAAh0CARjBAhPWAhgCAQz6AUD6AWgCAUf3AUH3AUcCAUWCAj6CAmkCAUP/AT//AUUCAUGKAj6KAnkCAT6HAj+HAkUCATySAj6SApABAgE2jwI/jwJFAgE0lQLKAZUC2wECATGVArQBlQK8AQIBL5sCQJsCaAIBLZgCQZgCSAIBK6MCPaMCPgIBKaACPqACQQIBJ6YCkgGmApsBAgElqwIXvgIiAgEZwQIhwQJRAgENwwIXygIgAgETzQIX1AIgAgEPrAIZvQIkAgEayAJEyAJfAgEWxQJFxQJPAgEU0gJE0gJPAgESzwJFzwJPAgEQrQIbtAIkAgEgtQIbvAIkAgEbsgJGsgJVAgEkrwJHrwJVAgEhugJFugJKAgEetwJGtwJJAgEc</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="File_Read_1" sap:VirtualizedContainerService.HintSize="928,22" />
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="928,62" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="928,62" />
      <sap2010:ViewStateData Id="DocumentOC_1" sap:VirtualizedContainerService.HintSize="928,22" />
      <sap2010:ViewStateData Id="Assign_28" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="CommentOut_1" sap:VirtualizedContainerService.HintSize="242,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_29" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_59" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_60" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_61" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Template_Apply_5" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Template_Apply_4" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_2" sap:VirtualizedContainerService.HintSize="242,118" />
      <sap2010:ViewStateData Id="Sequence_11" sap:VirtualizedContainerService.HintSize="680,810">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="680,62" />
      <sap2010:ViewStateData Id="DeserializeJSON_1" sap:VirtualizedContainerService.HintSize="658,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_2" sap:VirtualizedContainerService.HintSize="658,22" />
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="510,62" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="510,62" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="510,62" />
      <sap2010:ViewStateData Id="Assign_58" sap:VirtualizedContainerService.HintSize="510,62" />
      <sap2010:ViewStateData Id="DeserializeJSON_3" sap:VirtualizedContainerService.HintSize="510,22" />
      <sap2010:ViewStateData Id="Assign_13" sap:VirtualizedContainerService.HintSize="510,62" />
      <sap2010:ViewStateData Id="Assign_14" sap:VirtualizedContainerService.HintSize="510,62" />
      <sap2010:ViewStateData Id="Assign_15" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_16" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_17" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="286,412">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_3" sap:VirtualizedContainerService.HintSize="510,564.666666666667" />
      <sap2010:ViewStateData Id="Assign_41" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_56" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_17" sap:VirtualizedContainerService.HintSize="510,216" />
      <sap2010:ViewStateData Id="Sequence_10" sap:VirtualizedContainerService.HintSize="532,1618.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="658,1772.66666666667" />
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="680,2020.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="702,3096.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="200,100.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="928,3250.66666666667" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="950,3702.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="990,3862.66666666667" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>